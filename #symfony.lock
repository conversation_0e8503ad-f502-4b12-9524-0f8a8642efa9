{"composer/ca-bundle": {"version": "1.3.1"}, "doctrine/annotations": {"version": "1.13", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "a2759dd6123694c8d901d0ec80006e044c2e6457"}, "files": ["config/routes/annotations.yaml"]}, "doctrine/cache": {"version": "1.12.1"}, "doctrine/collections": {"version": "1.6.8"}, "doctrine/common": {"version": "2.13.3"}, "doctrine/dbal": {"version": "2.13.5"}, "doctrine/deprecations": {"version": "v0.5.3"}, "doctrine/doctrine-bundle": {"version": "1.12", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.12", "ref": "3cc0efdb85531c72c2b25cbb41eec849a279aa64"}, "files": ["config/packages/doctrine.yaml", "config/packages/prod/doctrine.yaml", "src/Entity/.gitignore", "src/Repository/.gitignore"]}, "doctrine/doctrine-cache-bundle": {"version": "1.4.0"}, "doctrine/doctrine-migrations-bundle": {"version": "3.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.1", "ref": "ee609429c9ee23e22d6fa5728211768f51ed2818"}, "files": ["config/packages/doctrine_migrations.yaml", "migrations/.gitignore"]}, "doctrine/event-manager": {"version": "1.1.1"}, "doctrine/inflector": {"version": "1.4.4"}, "doctrine/instantiator": {"version": "1.4.0"}, "doctrine/lexer": {"version": "1.2.1"}, "doctrine/migrations": {"version": "3.3.2"}, "doctrine/orm": {"version": "2.7.5"}, "doctrine/persistence": {"version": "1.3.8"}, "doctrine/reflection": {"version": "1.2.2"}, "egulias/email-validator": {"version": "3.1.2"}, "firebase/php-jwt": {"version": "v5.5.1"}, "friendsofphp/proxy-manager-lts": {"version": "v1.0.5"}, "graylog2/gelf-php": {"version": "1.7.1"}, "guzzlehttp/guzzle": {"version": "6.5.5"}, "guzzlehttp/promises": {"version": "1.5.1"}, "guzzlehttp/psr7": {"version": "1.8.3"}, "jdorn/sql-formatter": {"version": "v1.2.17"}, "knplabs/knp-snappy": {"version": "v1.3.1"}, "laminas/laminas-code": {"version": "4.4.3"}, "league/html-to-markdown": {"version": "4.10.0"}, "mobiledetect/mobiledetectlib": {"version": "2.8.32"}, "monolog/monolog": {"version": "2.3.5"}, "myclabs/deep-copy": {"version": "1.10.2"}, "nikic/php-parser": {"version": "v4.0.3"}, "paragonie/constant_time_encoding": {"version": "v2.4.0"}, "php": {"version": "7.4"}, "phpdocumentor/reflection-common": {"version": "2.2.0"}, "phpdocumentor/reflection-docblock": {"version": "5.3.0"}, "phpdocumentor/type-resolver": {"version": "1.5.1"}, "psr/cache": {"version": "1.0.1"}, "psr/container": {"version": "1.1.2"}, "psr/http-message": {"version": "1.0.1"}, "psr/log": {"version": "1.1.4"}, "ralouphie/getallheaders": {"version": "3.0.3"}, "sabre/dav": {"version": "3.2.3"}, "sabre/event": {"version": "3.0.0"}, "sabre/http": {"version": "v4.2.4"}, "sabre/uri": {"version": "1.2.1"}, "sabre/vobject": {"version": "4.2.2"}, "sabre/xml": {"version": "1.5.1"}, "sebastian/code-unit-reverse-lookup": {"version": "1.0.2"}, "sebastian/comparator": {"version": "2.1.3"}, "sebastian/diff": {"version": "2.0.1"}, "sebastian/environment": {"version": "3.1.0"}, "sebastian/exporter": {"version": "3.1.4"}, "sebastian/global-state": {"version": "2.0.0"}, "sebastian/object-enumerator": {"version": "3.0.4"}, "sebastian/object-reflector": {"version": "1.1.2"}, "sebastian/recursion-context": {"version": "3.0.1"}, "sebastian/resource-operations": {"version": "1.0.0"}, "sebastian/version": {"version": "2.0.1"}, "sensio/framework-extra-bundle": {"version": "5.6", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.2", "ref": "fb7e19da7f013d0d422fa9bce16f5c510e27609b"}, "files": ["config/packages/sensio_framework_extra.yaml"]}, "sensiolabs/security-checker": {"version": "4.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.0", "ref": "160c9b600564faa1224e8f387d49ef13ceb8b793"}, "files": ["config/packages/security_checker.yaml"]}, "smalot/pdfparser": {"version": "v0.11"}, "swiftmailer/swiftmailer": {"version": "v6.3.0"}, "symfony/asset": {"version": "v4.4.27"}, "symfony/browser-kit": {"version": "v4.4.27"}, "symfony/cache": {"version": "v4.4.35"}, "symfony/cache-contracts": {"version": "v2.5.0"}, "symfony/config": {"version": "v4.4.34"}, "symfony/console": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.4", "ref": "fd5340d07d4c90504843b53da41525cf42e31f5c"}, "files": ["bin/console", "config/bootstrap.php"]}, "symfony/debug": {"version": "v4.4.31"}, "symfony/dependency-injection": {"version": "v4.4.34"}, "symfony/deprecation-contracts": {"version": "v2.5.0"}, "symfony/doctrine-bridge": {"version": "v4.4.34"}, "symfony/dom-crawler": {"version": "v5.3.7"}, "symfony/dotenv": {"version": "v4.4.33"}, "symfony/error-handler": {"version": "v4.4.34"}, "symfony/event-dispatcher": {"version": "v4.4.34"}, "symfony/event-dispatcher-contracts": {"version": "v1.1.11"}, "symfony/expression-language": {"version": "v4.4.34"}, "symfony/filesystem": {"version": "v5.3.4"}, "symfony/finder": {"version": "v5.3.7"}, "symfony/flex": {"version": "1.17", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "c0eeb50665f0f77226616b6038a9b06c03752d8e"}, "files": [".env"]}, "symfony/form": {"version": "v4.4.34"}, "symfony/framework-bundle": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.4", "ref": "3b9c85f14cad439042f88f94a1fd15fb8ed923c9"}, "files": ["config/bootstrap.php", "config/packages/cache.yaml", "config/packages/framework.yaml", "config/packages/test/framework.yaml", "config/preload.php", "config/routes/dev/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php"]}, "symfony/http-client": {"version": "v4.4.35"}, "symfony/http-client-contracts": {"version": "v2.5.0"}, "symfony/http-foundation": {"version": "v5.3.11"}, "symfony/http-kernel": {"version": "v4.4.35"}, "symfony/intl": {"version": "v4.4.34"}, "symfony/ldap": {"version": "v4.4.34"}, "symfony/maker-bundle": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/monolog-bridge": {"version": "v5.2.12"}, "symfony/monolog-bundle": {"version": "3.7", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.7", "ref": "a7bace7dbc5a7ed5608dbe2165e0774c87175fe6"}, "files": ["config/packages/dev/monolog.yaml", "config/packages/prod/deprecations.yaml", "config/packages/prod/monolog.yaml", "config/packages/test/monolog.yaml"]}, "symfony/options-resolver": {"version": "v5.3.7"}, "symfony/phpunit-bridge": {"version": "4.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.1", "ref": "2ba17ea3190d76e6e37a52378f6bca3d84891bc5"}, "files": [".env.test", "bin/phpunit", "phpunit.xml.dist", "tests/bootstrap.php"]}, "symfony/polyfill-ctype": {"version": "v1.23.0"}, "symfony/polyfill-iconv": {"version": "v1.23.0"}, "symfony/polyfill-intl-grapheme": {"version": "v1.23.1"}, "symfony/polyfill-intl-icu": {"version": "v1.23.0"}, "symfony/polyfill-intl-idn": {"version": "v1.23.0"}, "symfony/polyfill-intl-normalizer": {"version": "v1.23.0"}, "symfony/polyfill-mbstring": {"version": "v1.23.1"}, "symfony/polyfill-php72": {"version": "v1.23.0"}, "symfony/polyfill-php73": {"version": "v1.23.0"}, "symfony/polyfill-php80": {"version": "v1.23.1"}, "symfony/polyfill-php81": {"version": "v1.23.0"}, "symfony/process": {"version": "v5.3.12"}, "symfony/property-access": {"version": "v5.3.8"}, "symfony/property-info": {"version": "v5.3.8"}, "symfony/routing": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.2", "ref": "683dcb08707ba8d41b7e34adb0344bfd68d248a7"}, "files": ["config/packages/prod/routing.yaml", "config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/security-bundle": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.4", "ref": "7b4408dc203049666fe23fabed23cbadc6d8440f"}, "files": ["config/packages/security.yaml"]}, "symfony/security-core": {"version": "v4.4.34"}, "symfony/security-csrf": {"version": "v5.3.4"}, "symfony/security-guard": {"version": "v4.4.27"}, "symfony/security-http": {"version": "v4.4.34"}, "symfony/serializer": {"version": "v5.3.12"}, "symfony/service-contracts": {"version": "v2.5.0"}, "symfony/stopwatch": {"version": "v4.4.27"}, "symfony/string": {"version": "v5.3.10"}, "symfony/swiftmailer-bundle": {"version": "3.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "2.5", "ref": "f0b2fccdca2dfd97dc2fd5ad216d5e27c4f895ac"}, "files": ["config/packages/dev/swiftmailer.yaml", "config/packages/swiftmailer.yaml", "config/packages/test/swiftmailer.yaml"]}, "symfony/translation": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "2ad9d2545bce8ca1a863e50e92141f0b9d87ffcd"}, "files": ["config/packages/translation.yaml", "translations/.gitignore"]}, "symfony/translation-contracts": {"version": "v2.5.0"}, "symfony/twig-bridge": {"version": "v4.4.34"}, "symfony/twig-bundle": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.4", "ref": "73baff3f7b3cea12a73812a7cfd2c0924a9e250f"}, "files": ["config/packages/test/twig.yaml", "config/packages/twig.yaml", "templates/base.html.twig"]}, "symfony/var-dumper": {"version": "v5.3.11"}, "symfony/var-exporter": {"version": "v5.3.11"}, "symfony/web-profiler-bundle": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "6bdfa1a95f6b2e677ab985cd1af2eae35d62e0f6"}, "files": ["config/packages/dev/web_profiler.yaml", "config/packages/test/web_profiler.yaml", "config/routes/dev/web_profiler.yaml"]}, "symfony/webpack-encore-bundle": {"version": "1.13", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.9", "ref": "0f274572ea315eb3b5884518a50ca43f211b4534"}, "files": ["assets/app.js", "assets/bootstrap.js", "assets/controllers.json", "assets/controllers/hello_controller.js", "assets/styles/app.css", "config/packages/assets.yaml", "config/packages/prod/webpack_encore.yaml", "config/packages/test/webpack_encore.yaml", "config/packages/webpack_encore.yaml", "package.json", "webpack.config.js"]}, "symfony/workflow": {"version": "4.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "3b2f8ca32a07fcb00f899649053943fa3d8bbfb6"}, "files": ["config/packages/workflow.yaml"]}, "symfony/yaml": {"version": "v4.4.34"}, "tecnickcom/tcpdf": {"version": "6.4.2"}, "twig/extensions": {"version": "1.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "a86723ee8d8b2f9437c8ce60a5546a1c267da5ed"}, "files": ["config/packages/twig_extensions.yaml"]}, "twig/twig": {"version": "v2.14.8"}, "webmozart/assert": {"version": "1.10.0"}}