{"module/dashboard": "Dashboard", "module/box": "ABUS Box", "module/box2": "ABUS Box by NC", "module/nextcloud": "ABUS Nextcloud", "module/information": "Information", "module/information_sales": "Sales Information", "module/information_service": "After-Sales Service Information", "module/information_mounting": "Installation Information", "module/information_technical": "Technical information", "module/technical_data": "Technical data", "module/technical_data_eot_cranes": "EOT cranes", "module/technical_data_eot_cranes_abukonfis": "EOT cranes (ABUKonfis)", "module/technical_data_jib_cranes": "Jib cranes", "module/technical_data_jib_cranes_abukonfis": "Jib cranes (ABUKonfis)", "module/technical_data_mobile_gantry_cranes": "Mobile gantry cranes", "module/technical_data_mobile_gantry_cranes_abukonfis": "Mobile gantry cranes (ABUKonfis)", "module/technical_data_wire_rope_hoists": "Wire rope hoists", "module/technical_data_wire_rope_hoists_abukonfis": "Wire rope hoists (ABUKonfis)", "module/technical_data_chain_hoists_with_trolley": "Chain hoists with trolley", "module/technical_data_chain_hoists_with_trolley_abukonfis": "Chain hoists with trolley (ABUKonfis)", "module/technical_data_stationary_chain_hoists": "Stationary chain hoists", "module/technical_data_stationary_chain_hoists_abukonfis": "Stationary chain hoists (ABUKonfis)", "module/technical_data_crane_drives": "Crane drives", "module/technical_data_crane_drives_abukonfis": "Crane drives (ABUKonfis)", "module/technical_data_hb_systems": "HB-System", "module/technical_data_hb_systems_abukonfis": "HB-System (ABUKonfis)", "module/spare_parts": "Spare parts", "module/spare_parts_basket": "Shopping basket", "module/spare_parts_search": "Search", "module/spare_parts_winetis": "WINETIS", "module/spare_parts_prices": "Spare parts prices", "module/media": "Media", "module/abukonfis": "ABUKonfis", "module/abukonfis_online": "Online", "module/abukonfis_online_work": "Online work", "module/abukonfis_intern": "Internal", "module/abukonfis_intern_work": "Internal work", "module/abukonfis-technical-data": "Technical data", "module/abukonfis-test": "Test", "module/application": "Applications", "module/access_management": "Access management", "module/gitlab": "Gitlab", "module/chat": "Cha<PERSON>", "module/videoconference": " Video conference ", "module/crm": "CRM", "module/oviss": "OVISS", "module/oviss_over_ssl": "OVISS", "module/usermanagement": "User management", "module/usermanagement_users": "User", "module/usermanagement_companies": "Companies", "module/usermanagement_permission_package": "Rights package", "module/usermanagement_found_problems": "Problems found", "module/access_management_list_users": "List users", "module/access_management_find_problems": "Find problems", "module/access_management_account_delete": "Delete user", "module/access_management_account_workflow": "Workflow", "module/access_management_account_workflow_archive": "Archive", "module/access_management_account_create": "Create new ABUKonfis account", "module/access_management_account_vpn_create": "Create new VPN access", "module/access_management_account_reassign": "Reassign ABUKonfis", "module/access_management_account_vpn_reassign": "Reassign VPN access", "module/refresh_data": "Update data", "module/reporting": "Reporting", "module/reporting_users": "User", "module/reporting_forms": "Forms", "module/reporting_matomo": "<PERSON><PERSON>", "module/reporting_google_analytics": "Google Analytics", "module/reporting_adwords": "Google AdWords", "module/devops": "DevOps", "module/devops_sql": "phpMyAdmin", "module/devops_traefik": "Traefik", "module/devops_kibana": "Kibana", "module/devops_prometheus": "Prometheus", "module/devops_logs": "Logging", "module/devops_grafana": "Monitoring", "module/devops_portainer": "<PERSON><PERSON><PERSON>", "module/service": "Service features", "module/service_password_generator": "Password generator", "module/service_mail_generator": "Mail generator", "module/service_shop_sql_generator": "Shop SQL generator", "module/service_activedirectory": "Active Directory", "module/translation": "Translations", "module/intranet": "Intranet", "module/website": "Website", "module/website_germany": "Germany", "module/website_austria": "Austria", "module/website_england": "England", "module/website_spain": "Spain", "module/website_poland": "Poland", "module/website_sweden": "Sweden", "module/website_france": "France", "module/website_netherlands": "Netherlands", "module/website_international": "International", "module/website_pimcore_admin": "Administration", "module/further_services": "Further services", "module/documentation": "Documentation", "module/documentation_pdf": "PDF", "module/documentation_online": "Mobile documentation", "module/documentation_service": "Service documentation", "module/print_shop": "Shop for printed matter", "module/ilias": "ABUS Academy", "module/pricelist": "Price lists", "module/portalaccess_Videoconference_Chat": "Portal access - incl. chat", "module/media/description/en": "In the Media area images in different degrees of quality may be downloaded and be used for your own marketing purposes. You will find images in the areas of products, components, company images, and images of product applications.", "module/technical_data/description/en": "In the Technical Data area you will find technical data on different products and components for the design and calculation of cranes. This area is constantly being enlarged and updated.", "module/spareparts/description/en": "The sales prices can be found in all available spare parts catalogues.", "module/pricelist/description/en": "Here you will find the currently valid price lists.", "module/winetis/description/en": "Information system for the definition of spare parts", "module/bookingsTool": "Bookings", "vue/helper/modal/errorTitle": "An error has occurred.", "vue/helper/modal/close": "Close", "frame/portal": "Web portal", "frame/logout": "Logout", "frame/toggle_right_sidebar": "Show/hide right-hand side bar", "frame/right_sidebar_groups": "Groups", "frame/userprofile": "User profile", "frame/profile_settings": "Profile & settings", "frame/profile/profile_street": "street", "frame/profile/profile_postcode": "post code", "frame/profile/profile_town": "place", "frame/profile/profile_country": "country", "frame/profile/profile_email": "E-mail", "frame/profile/profile_phone": "Phone", "frame/profile/profile_mobile": "Mobile", "frame/profile/profile_fax": "fax", "frame/delete_account/title": "You want to delete your account?", "frame/delete_account/description": "If you wish to delete your access to the ABUS portal, please send us a short e-mail with the request for deletion to:", "frame/change_password": "Change password", "frame/language": "Language", "frame/help": "Help", "frame/report_problem": "Report problem", "frame/contacht_support": "Contact", "frame/imprint": "Legal Notice", "frame/abus_services": "ABUS services", "frame/dashboard": "Dashboard", "frame/usermode": "User mode", "frame/impersonate": "Log in as user", "frame/active_users": "User last 15 min.", "frame/name": "Surname", "frame/company": "Company", "frame/email": "E-mail", "frame/telephone": "Phone", "frame/cancel": "Cancel", "frame/username_(email)": "User name (e-mail)", "frame/password": "Password", "frame/forgot_your_password": "Forgotten your password?", "frame/login": "<PERSON><PERSON>", "frame/request_new_password": "Request new password", "frame/register_account": "Create user account", "frame/any_problems": "Do you need support?", "frame/back_to_login": "Return to login", "frame/invalid_credentials.": "The access data entered are not correct.", "frame/invalid_csrf_token.": "Your session has expired.<br /><br />Please reload the page.", "frame/username_could_not_be_found.": "User not found", "frame/dashboard/headline": "ABUS Portal News", "frame/dashboard/headline_archive": "ABUS Portal News - Archive", "frame/dashboard/welcome": "<h4>Welcome to the new ABUS portal!</h4>We are pleased to welcome you to our new portal. The main innovations at the moment are:<br /><br /><ul><li>no more tiles - but a navigation bar on the left side. Almost all services are now displayed \"inline\".</li><li>Depending on your authorization, you may also find the latest information that has not yet been read (e.g. sales information) on the start page.</li></ul>If you have any comments/problems/questions, please use the <a href=\"/reportProblem\">Report problem</a> button at the bottom left. We will check the incoming things and contact you.", "frame/dashboard/settings/email_headline": "Specify language for e-mails", "frame/dashboard/settings/email_text": "Please specify the language used for our e-mails to you under <b><a href='/profile'>Profile & settings</a></b>.", "frame/dashboard/settings/abukonfis_headline": "Define settings for \"ABUKonfis\" or \"Technical Data\"", "frame/dashboard/settings/abukonfis_text": "Please define the language for the output documents and the installation country under <b><a href='/profile'>Profile & settings</a>", "frame/reportProblem/title": "Report problem", "frame/reportProblem/headline": "Have you discovered a problem or error in the ABUS web portal or would you like to make a suggestion for improvement? In this case, please leave us a message.", "frame/reportProblem/successfullyTransferred": "Transmitted successfully", "frame/reportProblem/thankYou": "Thank you for reporting the problem.", "frame/reportProblem/typeOfProblem": "Type of problem", "frame/reportProblem/bug": "Error", "frame/reportProblem/improvement": "Improvement", "frame/reportProblem/suggestion": "Suggestion", "frame/reportProblem/chooseType": "Please select the type of problem.", "frame/reportProblem/affectedService": "Services affected", "frame/reportProblem/pleaseChoose": "Please select ...", "frame/reportProblem/images": "Photos", "frame/reportProblem/dropzone": "Store photos here by means of Drag & Drop", "frame/reportProblem/maxSize": "Max. size per file", "frame/reportProblem/setup": "You are currently using the following system. Please edit the data if you have established the problem on another system", "frame/reportProblem/os": "Operating system (OS)", "frame/reportProblem/browser": "Browser", "frame/reportProblem/browserVersion": "Browser version", "frame/reportProblem/description": "Description", "frame/reportProblem/descriptionError": "Please describe the problem as precisely as possible and use the option for uploading photos. Every piece of information that can help us to solve the problem is welcome.", "frame/reportProblem/serviceError": "Please select the service where the problem occurred.", "frame/reportProblem/send": "Report problem", "frame/reportProblem/others": "Other", "frame/reportProblem/report_another": "Report a further problem", "frame/reportProblem/noImages": "Please do not pull any photos directly into the description. These are shown in the description, but not transferred.", "frame/reportProblem/noImages2": "For this reason, pull the photos to the area below shown in yellow. The photos are then automatically added to the description.", "frame/profile/profile_details": "Profile details", "frame/profile/profile_settings": "Profile & settings", "frame/settings/settings": "Settings", "frame/settings/mailLocale": "E-mail language", "frame/settings/mailLocaleHelp": "Language in which you receive e-mails", "frame/settings/saved": "Saved", "frame/settings/successfullySaved": "Saved successfully", "frame/settings/abukonfis": "Settings ABUKonfis", "frame/settings/abukonfisOutputLanguage": "output language", "frame/settings/abukonfisBuiltInCountry": "Country of end user", "frame/settings/abukonfisOutputLanguageHelp": "Language in which the PDF documents are created.", "frame/settings/abukonfisBuiltInCountryHelp": "Country where the crane is assembled. The country where the crane is to be installed also defines various country regulations (including the mains voltage).", "frame/usermode/placeholder": "Name, company", "frame/usermode/nothing-found-text": "No suitable entries were found", "frame/usermode/leave_usermode": "Leave user mode", "frame/usermode/no_live_update_of_ad_roles": "No real-time update of AD groups in user mode possible!", "frame/legal_notice/headline": "Legal Notice", "frame/legal_notice/contact": "Contact", "frame/legal_notice/telephone": "Phone", "frame/legal_notice/fax": "Fax", "frame/legal_notice/internet": "Internet", "frame/legal_notice/email": "E-Mail", "frame/legal_notice/cologne": "Cologne", "frame/legal_notice/managing_directors": "Managing Directors", "frame/legal_notice/register": "Commercial Register", "frame/legal_notice/register_number": "Register number", "frame/legal_notice/content_1": "VAT ID number in accordance with § 27a Value Added Tax Act: VAT ID no.: DE ***********", "frame/legal_notice/content_2": "We assume no liability for the content of external links despite careful monitoring of contents. The operators of linked pages are solely responsible for the content of the linked pages.", "frame/legal_notice/further_legal_notices": "Please note that this portal is a service for commercial participants. When using the configurators, your data will be transferred to other ABUS companies or external sales partners in order to provide you, simply and quickly, with suitable offers and optimized customer support. You will find more detailed information in our", "frame/cookie/use_cookies": "In order to optimally present and improve our ABUS portal, we use cookies.", "frame/cookie/agree_cookies": "By continuing to use the ABUS portal, you agree to the use of cookies.", "frame/cookie/cookies_further_information_prefix": "You can find out more about this in our", "frame/cookie/cookies_further_information_privacy": "privacy statement", "frame/cookie/cookies_further_information_suffix": "", "frame/iframe/not_available": "Not available in user mode", "frame/iframe/not_available_description": "This view is not available in user mode since it is an external service.", "frame/news": "ABUS Portal News", "frame/news/archive": "News archive", "frame/news/archive_close_tooltip": "Move news to archive", "frame/news/administration": "Administration", "frame/news/create": "Create new news", "frame/news/leave_adminsitration": "Leave administration", "frame/news/draft": "draft version", "frame/news/draft_description": "The news is currently in draft status and is not displayed on the dashboard.", "frame/news/modules": "Services", "frame/news/groups": "Active Directory groups", "frame/news/edit": "Edit", "frame/news/publishDate": "Publication date", "frame/news/remove": "Delete news", "frame/news/remove_message": "Do you really want to delete the ABUS News?", "frame/news/buttonCancel": "Cancel", "frame/news/buttonDelete": "Delete", "frame/news/create_message": "Do you want to create a new ABUS News?", "frame/news/predefined_text_header": "Use predefined Text", "frame/news/predefined_text_usecase": "Predefined text", "frame/news/predefined_text_date": "Date", "frame/news/predefined_text_time_from": "From", "frame/news/predefined_text_time_until": "Until", "frame/news/predefined_text_setDateAndTime": "Set date and time within text", "frame/news/predefined_text_maintenance_option": "Maintenance", "frame/news/predefined_text_maintenance_header": "Current IT information - Maintenance work", "frame/news/predefined_text_maintenance": "<p>Dear <PERSON> or <PERSON><PERSON>,</p><p>on X, 00.00.0000 between 00:00 - 00:00 o&#39;clock the ABUS Portal, ABUKonfis and the website will not be available to you due to important maintenance work.</p><p>We ask for your understanding!</p><p>Sales Organisation Department</p>", "api/password/change_password": "Change password", "api/password/new_password": "New password", "api/password/current_password": "Current password", "api/password/repeat_new_password": "Repeat new password", "api/password/set_new_password": "Specify new password", "api/password/request_resetting": "Request to reset your ABUS web portal password", "api/password/mailText": "Dear ABUS web portal user,<br>We have received a request for your account due to a forgotten password.<br>To reset your password, please click the following link", "api/password/reset_your_password": "Reset password", "api/password/error": "Unfortunately, your request could not be processed.", "api/password/if_mail_found": "When we have found your e-mail address in the system you will receive an e-mail within a short time telling you how to reset your password.", "api/password/link_expired": "The link for resetting your password is invalid or has expired.", "api/password/redirect_to_homepage": "You are now being navigated to the start page...", "api/password/reset": "Reset password", "api/password/could_not_be_changed": "The password could not be changed.", "api/password/not_equal": "The passwords entered do not correlate.", "api/password/password_changed": "The password has been changed successfully.", "api/password/password_incorrect": "The current password entered is not correct.", "api/password/password_not_complex": "The password is not complex enough.", "api/password/password_complexity_length": "The password must be at least eight characters long.", "api/password/password_complexity_alpha_lowercase": "Lower case letters from a to z", "api/password/password_complexity_alpha_uppercase": "Capital letters from A to Z", "api/password/password_complexity_alpha_number": "digits of base 10 (0 to 9)", "api/password/password_complexity_special": "Non-alphanumeric characters (e.g. !, $, #, %)", "api/password/password_threeOfFour": "The password must contain at least one character from each of three of the following four categories:", "api/password/password_policy": "password policy", "api/password/password_name_1": "The password must not contain the complete login name or any part of the user name (first name, additional first name, surname) of the respective user, provided that it is at least three characters long.", "api/password/password_name_2": "Example:", "api/password/password_name_3": "If an employee of '<PERSON><PERSON>' had the login name 9876, no passwords containing the elements '9876', 'enie', 'van' or 'meiklok<PERSON><PERSON>' would be allowed, but the element 'de' would be allowed.", "api/password/password_name_4": "Not allowed would be e.g. \"A987654321!\"\"Vanda!en\" and \"!Genius!!\"", "api/password/password_name_5": "Permitted would be \"Aderla$$\" or \"!ednaveinE\".", "language/brazilian": "Brazilian", "language/german": "German", "language/english": "English", "language/danish": "Danish", "language/dutch": "Dutch", "language/czech": "Czech", "language/finnish": "Finnish", "language/french": "French", "language/greek": "Greek", "language/italian": "Italian", "language/multi": "MULTI (German - English - French - Spanish)", "language/norwegian": "Norwegian", "language/polish": "Polish", "language/swedish": "Swedish", "language/spanish": "Spanish", "language/turkish": "Turkish", "country/afghanistan": "Afghanistan", "country/albania": "Albania", "country/algeria": "Algeria", "country/american_samoa": "American Samoa", "country/andorra": "Andorra", "country/angola": "Angola", "country/anguilla": "<PERSON><PERSON><PERSON>", "country/antarctica": "Antarctica", "country/antigua_and_barbuda": "Antigua und Barbuda", "country/argentina": "Argentina", "country/armenia": "Armenia", "country/aruba": "Aruba", "country/ascension": "Ascension", "country/australia": "Australia", "country/austria": "Austria", "country/azerbaijan": "Azerbaijan", "country/bahamas": "Bahamas", "country/bahrain": "Bahrain", "country/bangladesh": "Bangladesh ", "country/barbados": "Barbados", "country/belarus": "Belarus", "country/belgium": "Belgium", "country/belize": "Belize", "country/benin": "Benin", "country/bermuda": "Bermuda", "country/bhutan": "Bhutan", "country/bolivia": "Bolivia", "country/bosnia_and_herzegovina": "Bosnia and Herzegovina", "country/botswana": "Botswana", "country/bouvet_island": "Bouvet Island", "country/brazil": "Brazil", "country/british_indian_ocean_territory": "British Indian Ocean Territory", "country/brunei_darussalam": "Brunei Darussalam", "country/bulgaria": "Bulgaria", "country/burkina_faso": "Burkina Faso", "country/burundi": "Burundi", "country/cambodia": "Cambodia", "country/cameroon": "Cameroon", "country/canada": "Canada", "country/cape_verde": "Cape Verde", "country/cayman_islands": "Cayman Islands", "country/central_african_republic": "Central African Republic", "country/chad": "Chad", "country/chile": "Chile", "country/china": "China", "country/christmas_island": "Christmas Island", "country/cocos_(keeling)_islands": "Cocos (Keeling) Islands", "country/colombia": "Colombia", "country/comoros": "Comoros", "country/congo": "Congo", "country/congo,_republic_of": "Congo, Republic of", "country/cook_islands": "Cook Islands", "country/costa_rica": "Costa Rica", "country/cote_d'ivoire_(ivory_coast)": "Cote d'i<PERSON> (Ivory Coast)", "country/croatia": "Croatia", "country/cuba": "Cuba", "country/cyprus": "Cyprus", "country/czech_republic": "Czech Republic", "country/democratic_republic_of_the_congo": "Democratic Republic of the Congo", "country/denmark": "Denmark", "country/djibouti": "Djibouti", "country/dominica": "Dominica", "country/dominican_republic": "Dominican Republic", "country/east_timor": "Timor-Leste (Osttimor)", "country/ecuador": "Ecuador", "country/egypt": "Egypt", "country/el_salvador": "El Salvador", "country/equatorial_guinea": "Equatorial Guinea", "country/eritrea": "Eritrea", "country/estonia": "Estonia", "country/ethiopia": "Ethiopia", "country/falkland_islands": "Falkland Islands", "country/faroe_islands": "Faroe Islands", "country/fiji": "Fiji", "country/finland": "Finland", "country/france": "France", "country/french_guiana": "French Guiana", "country/french_metropolitan_areas": "French Metropolitan Areas", "country/french_polynesia": "French Polynesia", "country/french_southern_territories": "French Southern Territories", "country/gabon": "Gabon", "country/gambia": "Gambia", "country/georgia": "Georgia", "country/germany": "Germany", "country/ghana": "Ghana", "country/gibraltar": "Gibraltar", "country/great_britain": "United Kingdom", "country/greece": "Greece", "country/greenland": "Greenland", "country/grenada": "Grenada", "country/guadaloupe": "Guadeloupe", "country/guam": "Guam", "country/guatemala": "Guatemala", "country/guernsey": "Guernsey", "country/guinea": "Guinea", "country/guinea-bissau": "Guinea-Bissau", "country/guyana": "Guyana", "country/haiti": "Haiti", "country/heard_and_mc_donald_islands": "Heard and Mc Donald Islands", "country/holy_see": "Holy See", "country/honduras": "Honduras", "country/hong_kong": "Hong Kong", "country/hungary": "Hungary", "country/iceland": "Iceland", "country/india": "India", "country/indonesia": "Indonesia", "country/iran": "Iran", "country/iraq": "Iraq", "country/ireland": "Ireland", "country/isle_of_man": "Isle of Man", "country/israel": "Israel", "country/italy": "Italy", "country/ivory_coast": "Cote d'i<PERSON> (Ivory Coast)", "country/jamaica": "Jamaica", "country/japan": "Japan", "country/jersey": "Jersey", "country/jordan": "Jordan", "country/kazakhstan": "Kazakhstan", "country/kenya": "Kenya", "country/kiribati": "Kiribati", "country/korea,_democratic_people's_rep._(north_korea)": "North Korea", "country/korea,_republic_of_(south_korea)": "South Korea", "country/kosovo": "Kosovo", "country/kuwait": "Kuwait", "country/kyrgyzstan": "Kyrgyzstan", "country/lao,_people's_democratic_republic": "Lao People's Democratic Republic", "country/latvia": "Latvia", "country/lebanon": "Lebanon", "country/lesotho": "Lesotho", "country/liberia": "Liberia", "country/libya": "Libya", "country/liechtenstein": "Liechtenstein", "country/lithuania": "Lithuania", "country/luxembourg": "Luxembourg", "country/macao": "Macao", "country/macedonia": "MacedoniaMazedonien", "country/madagascar": "Madagascar", "country/malawi": "Malawi", "country/malaysia": "Malaysia", "country/maldives": "Maldives", "country/mali": "Mali", "country/malta": "Malta", "country/marshall_islands": "Marshall Islands", "country/martinique": "Martinique", "country/mauritania": "Mauritania", "country/mauritius": "Mauritius", "country/mayotte": "Mayotte", "country/mexico": "Mexico", "country/micronesia": "Micronesia, Federal States of", "country/moldava": "Moldova, Republic of", "country/monaco": "Monaco", "country/mongolia": "Mongolia", "country/montenegro": "Montenegro", "country/montserrat": "Montserrat", "country/morocco": "Morocco", "country/mozambique": "Mozambique", "country/myanmar": "Myanmar", "country/myanmar,_burma": "Myanmar, Birma, Burma", "country/namibia": "Namibia", "country/nauru": "Nauru", "country/nepal": "Nepal", "country/netherlands": "Netherlands", "country/netherlands_antilles": "Netherlands Antilles", "country/new_caledonia": "New Caledonia", "country/new_zealand": "New Zealand", "country/nicaragua": "Nicaragua", "country/niger": "Niger", "country/nigeria": "Nigeria", "country/niue": "Niue", "country/norfolk_island": "Norfolk Island", "country/north_korea": "North Korea", "country/northern_mariana_islands": "Northern Mariana Islands", "country/norway": "Norway", "country/oman": "Oman", "country/pakistan": "Pakistan", "country/palau": "<PERSON><PERSON>", "country/palestinian_national_authority": "Palestinian National Authority", "country/panama": "Panama", "country/papua_new_guinea": "Papua New Guinea", "country/paraguay": "Paraguay", "country/peru": "Peru", "country/philippines": "Philippines", "country/pitcairn_island": "Pitcairn Island", "country/poland": "Poland", "country/portugal": "Portugal", "country/puerto_rico": "Puerto Rico", "country/qatar": "Qatar", "country/republic_of_korea": "South Korea", "country/reunion": "Reunion Island", "country/romania": "Romania", "country/russia": "Russia", "country/russian_federation": "Russia", "country/rwanda": "Rwanda", "country/saint_helena": "Saint Helena", "country/saint_kitts_and_nevis": "Saint Kitts and Nevis", "country/saint_lucia": "Saint Lucia", "country/saint_vincent_and_the_grenadines": "Saint Vincent and the Grenadines", "country/samoa": "Samoa", "country/san_marino": "San Marino", "country/sao_tome_and_príncipe": "Sao Tome and Pr<PERSON>cipe", "country/saudi_arabia": "Saudi Arabia", "country/senegal": "Senegal", "country/serbia": "Serbia", "country/seychelles": "Seychelles", "country/sierra_leone": "Sierra Leone", "country/singapore": "Singapore", "country/slovakia": "Slovakia", "country/slovenia": "Slovenia", "country/solomon_islands": "Solomon Islands", "country/somalia": "Somalia", "country/south_africa": "South Africa", "country/south_georgia_and_south_sandwich_islands": "South Georgia and South Sandwich Islands", "country/south_sudan": "South Sudan", "country/spain": "Spain", "country/sri_lanka": "Sri Lanka", "country/st._pierre_and_miquelon": "St. Pierre and Miquelon", "country/sudan": "Sudan", "country/suriname": "Suriname", "country/svalbard_and_jan_mayen_islands": "Svalbard and Jan Mayen Islands", "country/swaziland": "Swaziland", "country/sweden": "Sweden", "country/switzerland": "Switzerland", "country/syria": "Syria", "country/taiwan": "Taiwan", "country/tajikistan": "Tajikistan", "country/tanzania": "Tanzania", "country/thailand": "Thailand", "country/tibet": "Tibet", "country/timor-leste": "Timor-Leste", "country/togo": "Togo", "country/tokelau": "Tokelau", "country/tonga": "Tonga", "country/trinidad_and_tobago": "Trinidad und Tobago", "country/tunisia": "Tunisia", "country/turkey": "Turkey", "country/turkmenistan": "Turkmenistan", "country/turks_and_caicos_islands": "Turks and Caicos Islands", "country/tuvalu": "Tuvalu", "country/u.s._minor_outlying_islands": "U.S. Minor Outlying Islands", "country/uganda": "Uganda", "country/ukraine": "Ukraine", "country/united_arab_emirates": "United Arab Emirates", "country/united_kingdom": "United Kingdom", "country/united_states": "United States", "country/uruguay": "Uruguay", "country/uzbekistan": "Uzbekistan", "country/vanuatu": "Vanuatu", "country/vatican_city_state": "Holy See", "country/venezuela": "Venezuela", "country/vietnam": "Vietnam", "country/virgin_islands_(british)": "Virgin Islands (British)", "country/virgin_islands_(u.s.)": "Virgin Islands (U.S.)", "country/wallis_and_futuna_islands": "Wallis and Futuna Islands", "country/western_sahara": "Western Sahara", "country/yemen": "Yemen", "country/zaire": "Zaire", "country/zambia": "Zambia", "country/zimbabwe": "Zimbabwe", "box/search/placeholder": "What are you looking for?", "box/search/all_folders": "All Folders", "box/search/all_languages": "All Languages", "box/search/limited": "The search results were limited <b>to #LIMIT# results due to the high number of results</b>.<br />Total #FOUND# results have been found. Please narrow your search further.", "box/search/nothing_found": "No search results found.", "box/search/abuliner": "ABULiner", "box/search/allgemein": "Generic", "box/search/elektronik": "Electronic equipment", "box/search/funk": "Remote control", "box/search/hb-system": "HB System", "box/search/kettenzug": "Electric Chain Hoist", "box/search/laufkran": "Overhead travelling cranes", "box/search/leichtportalkran": "Lightweight mobile gantry", "box/search/schwenkkran": "Jib crane", "box/search/seilzug": "Wire rope hoist", "box/search/currentAndArchive": "all versions", "box/search/currentOnly": "current version", "box/search/archiveOnly": "archive", "box/frontend/loading": "Loading...", "box/frontend/settings": "Settings", "box/frontend/nothing_found": "No information found", "box/frontend/nothing_found_description": "Unfortunately, no information could be found related to your search request.", "box/frontend/show_link": "Show link", "box/frontend/upload_file": "Upload file", "box/frontend/create_subfolder": "Create subfolder(s)", "box/frontend/subfolder_name": "Folder name", "box/frontend/create": "Create", "box/frontend/abort": "Cancel", "box/dashboard/title": "ABUS Box - last updated files", "box/dashboard/latestFiles": "ABUS Box", "box/dashboard/lastWeek": "Last week", "box/dashboard/last2Weeks": "Last 2 weeks", "box/dashboard/last3Weeks": "Last 3 weeks", "box/dashboard/last4Weeks": "Last 4 weeks", "box/dashboard/show5": "Show 5", "box/dashboard/show20": "Show 20", "box/dashboard/show50": "Show 50", "box/dashboard/show100": "Show 100", "box/dashboard/showAll": "Show all", "box/dashboard/loading": "Loading...", "box/dashboard/loadingError": "Error occurred", "box/download_mode/download_mode": "Download mode", "box/download_mode/activate": "Switching on", "box/download_mode/deactivate": "Switching off", "box/download_mode/download_files": "Download files", "box/download_mode/ca": "approx.", "box/download_mode/info": "<b>In ABUS Box, you will always find the most updated data status.</b><br><br>For this reason, we would like recommend you to always consult these files online. However, there is an option to download different data in a speedy and comfortable way in the case you need them offline on your computer.<br><br>For this, please select the requested files with the left mouse button. After you have selected all files requested, please click on <i>\"Download selected files\"</i> and the selected files will be downloaded in a ZIP archive.", "box/admin/administration": "Administration", "box/admin/index": "Index directory structure", "box/admin/index_search": "Index files for search", "box/admin/index_modal_text": "Indexing has started. This can take some time, depending on the scope.<br /><br />You will be informed by e-mail as soon as the indexing of the directory structure has been completed.", "box/admin/last_indexed": "Last indexed on", "box/admin/takeAWhile": "Can take some time!", "box/admin/notSoOften": "It'll take a few minutes!", "box/admin/diskSpace": "Disc space", "box/admin/tags": "Reading tags", "box/admin/tags_modal_text": "The reading of the tags has been initiated.<br /><br />You will be informed via email, once the reading of the tags is completed.", "box/admin/disabled": "Deactivated", "box/admin/enabled": "Activated", "box/admin/on": "On", "box/admin/off": "Off", "box/admin/add": "Add", "box/admin/remove": "Remove", "box/admin/save": "Save", "box/admin/cancel": "Cancel", "box/admin/permission/public": "Public", "box/admin/permission/public/description": "Folder accessible without login. Is not used if \"Authorised ActiveDirectory groups\" or \"Authorised users\" are specified.", "box/admin/permission/clear_groups": "Reset groups", "box/admin/permission/clear_groups/description": "Deletes all authorisation groups in this folder which originated from higher order folders through inheritance.", "box/admin/permission/clear_users": "Reset users", "box/admin/permission/clear_users/description": "Deletes all authorised users in this folder who originated from higher order folders through inheritance.", "box/admin/permission/allowed_groups": "Group access", "box/admin/permission/allowed_groups/description": "Make folder accessible to ActiveDirectory groups.", "box/admin/permission/allowed_users": "User access", "box/admin/permission/allowed_users/description": "Make folder accessible to users.<br>All users with this e-mail extension can be cleared through entry in the form of <strong>*@abus-kransysteme.de</strong>.", "box/admin/languages/languages": "Languages", "box/admin/languages/languages/description": "Which languages does the folder contain?", "box/admin/languages/languages/placeholder": "Valid values: DE, EN, BR, etc.", "box/admin/languages/default": "Standard language", "box/admin/languages/default/description": "Specify a language for a folder.<br>A public folder is then shown in the language.", "box/admin/languages/default/placeholder": "Valid values exclusively: DE, EN, FR, ES", "box/admin/languages/only": "Only in certain languages", "box/admin/languages/only/description": "Only show the folder in certain web portal languages.", "box/admin/languages/only/placeholder": "Valid values exclusively: DE, EN, FR, ES", "box/admin/language/foldernameDE": "Folder or file name (German)", "box/admin/language/foldername/description": "Change the folder or file name in the given language.", "box/admin/language/foldernameEN": "(English)", "box/admin/language/foldernameFR": "(French)", "box/admin/language/foldernameES": "(Spanish)", "box/admin/search/searchable": "Searchable", "box/admin/search/searchable/description": "Can folders or files in the folder be found via the search?", "box/admin/search/searchFileExtension": "File extensions", "box/admin/search/searchFileExtension/description": "Show the search results with file extension (e.g. .pdf or .doc)?", "box/admin/search/tags": "Additional search terms", "box/admin/search/tags/description": "Additional search terms.", "box/admin/display/hideFromList": "Do not list", "box/admin/display/hideFromList/description": "Hide folder or file in the user's folder structure?<br>If this value is <strong>ON</strong> the folder or file is no longer shown in the user's folder structure.<br><br><strong>Direct access to the folder and files in the folder is still possible via the corresponding URLs.</strong>", "box/admin/display/listContent": "List contents", "box/admin/display/listContent/description": "Show folder and contents of the folder following direct access via the URL?<br>If this value is <strong>OFF</strong> this folder can no longer be opened directly via the URL, rather only individual files in this folder via the corresponding URLs.<br><br><strong>Direct access to files in the folder is still possible via the corresponding URLs.</strong>", "box/admin/display/downloadable": "Download possible", "box/admin/display/downloadable/description": "Can the file be downloaded via the download mode?", "box/admin/display/copyLink": "Direct link", "box/admin/display/copyLink/description": "Permit direct link for file or files in the folder to be shown <strong>for everyone</strong>.", "box/admin/display/fileExtension": "File extension", "box/admin/display/fileExtension/description": "Show file extensions (e.g. .pdf or .doc) for the file or files in the folder?", "box/admin/display/fileDate": "Change date", "box/admin/display/fileDate/description": "Show the last change date for the file or files in the folder?", "box/admin/display/filenameVersion": "Show version in file name", "box/admin/display/filenameVersion/description": "Show a version found in the file name of the file or folder?", "box/admin/display/metaVersion": "Show meta version", "box/admin/display/metaVersion/description": "Show a version given via meta:version?", "box/admin/display/MP4Video": "MP4 video", "box/admin/display/MP4Video/description": "Play an .mp4 video file directly in the ABUS Box", "box/admin/display/isArchived": "Archived", "box/admin/display/isArchived/description": "Is the content archived in the sense of no longer the latest version?", "box/admin/display/from": "Display from", "box/admin/display/from/description": "When should a folder or a file be displayed from?", "box/admin/display/until": "Display till", "box/admin/display/until/description": "When should a folder or a file be displayed to?", "box/admin/meta/version": "Version", "box/admin/meta/version/description": "Specify folder or file version.", "box/admin/dropdown/actions": "Actions", "box/admin/dropdown/settings": "Settings", "box/admin/dropdown/show_link": "Show link", "box/admin/dropdown/upload_file": "Upload file", "box/admin/dropdown/create_folder": "Create folder", "tdata/admin/administration": "Administration", "tdata/admin/live_database": "Live database", "tdata/admin/preview_database": "Preview database", "tdata/admin/index_database": "Index database", "tdata/admin/watching_preview": "Note: you are viewing the preview database.", "tdata/admin/error_occured": "Error occurred", "tdata/technical_data": "Technical data", "tdata/matching_records_found": "Matching records found", "tdata/eot_cranes": "EOT cranes", "tdata/mobile_gantry_cranes": "Mobile gantry cranes", "tdata/crane_drives": "Crane drives", "tdata/wire_rope_hoists": "Wire rope hoists", "tdata/jib_cranes": "Jib cranes", "tdata/hb-systems": "HB-systems", "tdata/pillar_jib_crane_ls": "Pillar jib crane LS", "tdata/pillar_jib_crane_lsx": "Pillar jib crane LSX", "tdata/pillar_jib_crane_vs": "Pillar jib crane VS", "tdata/wall_jib_crane_lw": "Wall jib crane LW", "tdata/wall_jib_crane_lwx": "Wall jib crane LWX", "tdata/wall_jib_crane_vw": "Wall jib crane VW", "tdata/crane_selection_help": "Can we help you with crane selection? (Technical explanations, dimensions, product data)", "tdata/please_select_three": "Please select at least three search arguments.", "tdata/single_girder_overhead_travelling_crane": "Single-girder travelling crane", "tdata/single_girder_travelling_crane_with_rolled_section_girder": "Single girder travelling crane with rolled section girder", "tdata/single_girder_travelling_crane_with_welded_box_girder": "Single girder travelling crane with welded box girder", "tdata/single_girder_torsion_box_crane_with_side-mounted_trolley": "Single girder torsion box crane with side-mounted trolley", "tdata/double_girder_overhead_travelling_crane": "Double-girder travelling crane", "tdata/double_girder_travelling_crane_with_welded_box_girder": "Double girder travelling crane with welded box girder", "tdata/underslung_overhead_travelling_crane": "Underslung travelling crane", "tdata/underslung_travelling_crane_with_rolled_section_girder_and_welded_main_girder_connection": "underslung travelling crane with rolled section girder and welded main girder connection", "tdata/underslung_travelling_crane_with_rolled_section_girder_and_bolted_main_girder_connection": "underslung travelling crane with rolled section girder and bolted main girder connection", "tdata/underslung_travelling_crane_with_box_girder_and_bolted_main_girder_connection": "underslung travelling crane with box girder and bolted main girder connection", "tdata/link_single-girder-overhead-travelling-crane": "http://www.abuscranes.com/products/overhead-travelling-cranes/single-girder-overhead-travelling-crane", "tdata/link_double-girder-overhead-travelling-crane": "http://www.abuscranes.com/products/overhead-travelling-cranes/double-girder-overhead-travelling-crane", "tdata/link_underslung-overhead-travelling-crane": "http://www.abuscranes.com/products/overhead-travelling-cranes/underslung-overhead-travelling-crane", "tdata/link_type-u-monorail-hoist-with-twin-trolleys": "http://www.abuscranes.com/products/wire-rope-hoists/type-u-monorail-hoist-with-twin-trolleys", "tdata/link_type-s-side-mounted-hoist": "http://www.abuscranes.com/products/wire-rope-hoists/type-s-side-mounted-hoist", "tdata/link_type-e-monorail-hoist": "http://www.abuscranes.com/products/wire-rope-hoists/type-e-monorail-hoist", "tdata/link_wire-rope-hoists": "http://www.abuscranes.com/products/wire-rope-hoists", "tdata/link_type-z-twin-barrel-crab-unit": "http://www.abuscranes.com/products/wire-rope-hoists/type-z-twin-barrel-crab-unit", "tdata/link_pillar-jib-crane-ls": "http://www.abuscranes.com/products/jib-cranes/pillar-jib-crane-ls", "tdata/link_pillar-jib-crane-lsx": "http://www.abuscranes.com/products/jib-cranes/pillar-jib-crane-lsx", "tdata/link_pillar-jib-crane-vs": "http://www.abuscranes.com/products/jib-cranes/pillar-jib-crane-vs", "tdata/link_wall-jib-crane-lw": "http://www.abuscranes.com/products/jib-cranes/wall-jib-crane-lw", "tdata/link_wall-jib-crane-lwx": "http://www.abuscranes.com/products/jib-cranes/wall-jib-crane-lwx", "tdata/link_wall-jib-crane-vw": "http://www.abuscranes.com/products/jib-cranes/wall-jib-crane-vw", "tdata/link_gmc": "http://www.abuscranes.com/products/electric-chain-hoists/abucompact-gmc", "tdata/link_gm2": "http://www.abuscranes.com/products/electric-chain-hoists/abucompact-gm2", "tdata/link_gm4": "http://www.abuscranes.com/products/electric-chain-hoists/abucompact-gm4", "tdata/link_gm6": "http://www.abuscranes.com/products/electric-chain-hoists/abucompact-gm6", "tdata/link_gm8": "http://www.abuscranes.com/products/electric-chain-hoists/abucompact-gm8", "tdata/link_lightweight-mobile-gantry": "http://www.abuscranes.com/products/lightweight-mobile-gantry", "tdata/higher_swl_and_bigger_spans_on_request": "Higher SWL and bigger spans on request. In the case you do not find the crane type or the SWL with the corresponding span in the Portal, <a href='http://www.abuscranes.com/contact' target='_blank' data-ajax='false' class='link noSpinner'>please do not hesitate to contact us.</a> We will be pleased to assist you and will make available the requested data sheets.", "tdata/up_to": "up to", "tdata/crane_type": "Crane type", "tdata/load_capacity": "Load capacity", "tdata/span": "Span", "tdata/crane_guiding_system": "Crane guiding system", "tdata/data_sheets_not_available_1": "Technical data sheets with the crane guiding system \"guide rollers\" are currently not available in this system for the ELS crane type.", "tdata/wheel_flange_guided": "Wheel flange guided", "tdata/guide_rollers": "Guide rollers", "tdata/total_width": "Total width", "tdata/total_height": "Overall height", "tdata/jib_length": "Jib length", "tdata/drive": "<PERSON><PERSON><PERSON>", "tdata/lifting": "Lifting speed", "tdata/trolley_travel": "Trolley travel", "tdata/manual": "manual", "tdata/electrical": "Electrical", "tdata/dowel_plate": "Dowel plate", "tdata/weld-on_plates": "Weld-on plates", "tdata/embracing_bracket": "Embracing bracket", "tdata/foundation_with_anchor_bolts": "Foundation with anchor bolts", "tdata/wall_bracket_/_wall_bearing": "Wall bracket / wall bearing", "tdata/height_of_lower_edge_of_jib": "Height of lower edge of jib", "tdata/slewing": "Slewing", "tdata/mounting": "Mounting", "tdata/type_": "Type", "tdata/type": "Type", "tdata/hoist": "Hoist", "tdata/hoist_size": "Hoist size", "tdata/hook_path": "Hook path", "tdata/fem/iso": "FEM/ISO", "tdata/reeving": "Reeving", "tdata/track": "Span", "tdata/operation": "Control", "tdata/operation_voltage": "Operating voltage", "tdata/wheel": "Wheel", "tdata/fem_group": "FEM group", "tdata/wheel_base": "Wheel base", "tdata/type_of_drive": "Crane guiding system", "tdata/spacing": "Spacing of means of guidance", "tdata/crane_drive": "Crane drive", "tdata/wheel_dia._(mm)": "Wheel<br />dia-<br />meter [mm]", "tdata/spacing_(mm)": "Spacing [mm]", "tdata/height_of_end_carriage_(mm)": "Height of end carriage", "tdata/drive_(m/min)": "Travelling [m/min]", "tdata/wheel_base_(mm)": "Wheel base [mm]", "tdata/rating_(kw)": "Rating [kW]", "tdata/screw_clearance_(mm)": "Screw clearance [mm]", "tdata/operating_voltage_(v/hz)": "Operating voltage [V/Hz]", "tdata/ver": "var", "tdata/mains_power_system": "Mains power system", "tdata/trolley_type": "Trolley type", "tdata/hook_path_(mm)": "Hook path [mm]", "tdata/lifting_(m/min)": "Lifting [m/min]", "tdata/energy_chain": "energy chain", "tdata/festoon_cable": "Festoon cable", "tdata/height_of_lower_edge_of_jib_(mm)": "Height of lower edge of jib [mm]", "tdata/overall_height_(mm)": "Overall height [mm]", "tdata/highest_hook_position_[mm)": "Highest hook position [mm]", "tdata/total_width_(mm)": "Total width [mm]", "tdata/total_height_(mm)": "Total height [mm]", "tdata/clear_width_(mm)": "Clear width [mm]", "tdata/load_capacity_(kg)": "Load<br />capacity [kg]", "tdata/track_(mm)": "Track [mm]", "tdata/hook": "Load hook", "tdata/push_trolley": "Push trolley", "tdata/via_push_button_pendant_mounted_on_the_trolley": "via push button pendant mounted on the trolley", "tdata/max_jib_length": "Max. jib length", "tdata/manual_or_electrical": "manual or electrical", "tdata/with_abus_electric_chain_hoist": "with ABUS electric chain hoist", "tdata/via_push_button_pendant,_mounted_on_the_trolley_or_mobile_on_separate_rail": "via push button pendant, mounted on the trolley or mobile on separate rail", "tdata/with_abus_electric_wire_rope_hoist": "with ABUS electric wire rope hoist", "tdata/electric_wire_rope_hoist": "Electric Wire Rope Hoist", "tdata/via_push_button_pendant,_mobile_on_separate_rail": "via push button pendant, mounted on the trolley or mobile on separate rail", "tdata/stationary_chain_hoists": "Stationary chain hoists", "tdata/electric_chain_hoist": "Electric Chain Hoist", "tdata/chain_hoists_with_trolley": "Chain hoists with trolley", "tdata/please_visit_website": "For more information, please visit our website", "tdata/max_span": "max. span", "tdata/max_total_width": "max. total width", "tdata/in_the_case_you_do_not_find_the_cran_type_or_the_swl_with_the_corresponding_jib_length_in_the_portal": "In case you do not find the crane type or the SWL with the corresponding total width in the Portal, <a href='http://www.abuscranes.com/contact' target='_blank' data-ajax='false' class='link noSpinner'>please do not hesitate to contact us.</a> We will be pleased to assist you and will make available the requested data sheets.", "tdata/in_the_case_you_do_not_find_the_crane_type_or_the_swl_with_the_corresponding_total_width_in_the_portal": "In case you do not find the crane type or the SWL with the corresponding total width in the Portal, <a href='http://www.abuscranes.com/contact' target='_blank' data-ajax='false' class='link noSpinner'>please do not hesitate to contact us.</a> We will be pleased to assist you and will make available the requested data sheets.", "tdata/hook_path_hint": "Hook path – chosen value must at least be reached, in addition all lines with higher values will be selected.", "tdata/fem_hint": "FEM/ISO – chosen value must at least be reached, in addition all lines with higher values will be selected.", "tdata/track_hint": "Track – not applicable to models E, S and U", "tdata/further_executions_on_request": "Further executions on request. In the case you do not find your favored execuction in the Portal, <a href='http://www.abuscranes.com/contact' target='_blank' data-ajax='false' class='link noSpinner'>please do not hesitate to contact us.</a> We will be pleased to assist you and will make available the requested data sheets.", "tdata/technische_daten/hb_system": "/Technische%20Daten/Englisch/HB%20System/", "tdata/technische_daten/mobile_gantry": "/Technische%20Daten/Englisch/Mobile%20Gantry/", "tdata/technische_daten/wire_rope_hoist": "/Technische%20Daten/Englisch/Wire%20Rope%20Hoist/", "tdata/technische_daten/chain_hoist": "/Technische%20Daten/Englisch/Chain%20Hoist/", "tdata/C": "C", "tdata/ELV": "ELV", "tdata/ELK": "ELK", "tdata/ELS": "ELS", "tdata/ZLK": "ZLK", "tdata/DLVM": "DLVM", "tdata/EDL": "EDL", "tdata/VS": "VS", "tdata/LS": "LS", "tdata/LSX": "LSX", "tdata/VW": "VW", "tdata/LW": "LW", "tdata/LWX": "LWX", "tdata/E": "E", "tdata/U": "U", "tdata/S": "S", "tdata/D": "D", "tdata/DA": "DA", "tdata/DQA": "DQA", "tdata/DB": "DB", "tdata/Z": "Z", "tdata/ZA": "ZA", "tdata/ZB": "ZB", "tdata/EL": "EL", "tdata/ZL": "ZL", "tdata/ZL8": "ZL8", "tdata/ED": "ED", "tdata/1/1": "1/1", "tdata/2/1": "2/1", "tdata/4/1": "4/1", "tdata/4/2": "4/2", "tdata/6/1": "6/1", "tdata/6/2": "6/2", "tdata/8/2": "8/2", "tdata/10/2": "10/2", "tdata/1Bm/M3": "1Bm/M3", "tdata/1Am/M4": "1Am/M4", "tdata/2m/M5": "2m/M5", "tdata/3m/M6": "3m/M6", "tdata/4m/M7": "4m/M7", "tdata/1Am": "1Am", "tdata/2m": "2m", "tdata/3m": "3m", "tdata/4m": "4m", "tdata/min": "min.", "tdata/220/60": "220/60", "tdata/230/50": "230/50", "tdata/380/60": "380/60", "tdata/400/50": "400/50", "tdata/460/60": "460/60", "tdata/0.8/3": "0.8/3", "tdata/0.7/4": "0.7/4", "tdata/1/4": "1/4", "tdata/1.3/5": "1.3/5", "tdata/0.8/5": "0.8/5", "tdata/1.5/6": "1.5/6", "tdata/2/8": "2/8", "tdata/5/20": "5/20", "reporting/forms/forms": "Forms", "reporting/forms/country_selection": "Country selection", "reporting/forms/update_identifiers": "Refresh identifiers", "reporting/forms/total_statistics": "Total statistics", "reporting/forms/forms_evaluated": "Forms evaluated", "reporting/forms/total_in_%": "Total in %", "reporting/forms/last_month_in_%": "Last month in %", "reporting/forms/this_month_in_%": "This month in %", "reporting/forms/overall_by_month_in_pieces": "Total according to month in units", "reporting/forms/top_10_countries": "Top 10 countries", "reporting/forms/number_of_received_forms_this_month": "TOP 10 countries - number of forms received this month", "reporting/forms/number_of_received_forms_last_month": "TOP 10 countries - number of forms received last month", "apis/sales/headline": "ABUS Sales information", "apis/sales/headline_short": "Sales Information", "apis/sales/headline_description": "", "apis/sales/copyright": "", "apis/sales/abukonfis": "ABUKonfis", "apis/sales/components_/_accessories": "Components / accessories", "apis/sales/electric_chain_hoist": "Chain hoist", "apis/sales/hb-system": "Suspended rail system", "apis/sales/it_and_programmes": "IT and programs", "apis/sales/jib_crane": "Jib crane", "apis/sales/lightweight_mobile_gantry": "Mobile gantry crane", "apis/sales/organisation_and_communication": "Organization and communication", "apis/sales/overhead_travelling_crane": "EOT crane", "apis/sales/prices": "Prices", "apis/sales/spare_parts": "Spare parts", "apis/sales/wire_rope_hoist": "Wire rope hoist", "apis/sales/mail_subject_request_translation": "ABUS information - sales information - please translate", "apis/sales/mail_subject_request_publishing": "ABUS information - sales information - please publish", "apis/sales/mail_subject_translate": "ABUS information - sales information - translation is complete - please publish", "apis/sales/mail_subject_translator_rejection": "ABUS information - sales information - please check acc. to text in internal memo box", "apis/sales/mail_subject_publisher_rejection": "ABUS information - sales information - please check acc. to text in internal memo box", "apis/sales/mail_subject_publish": "ABUS information - sales information - published!", "apis/sales/mail_subject_release_to_customer": "ABUS information - new sales information available", "apis/sales/mail_text_release_to_customer": "You will find new sales information under the following link:", "apis/service/headline": "ABUS After-Sales Service Information", "apis/service/headline_short": "After-Sales Service Information", "apis/service/headline_description": "<span style='font-size: 0.9rem'>All after-sales service information circular letters in the ABUS portal and the information contained therein are the exclusive property of ABUS Kransysteme GmbH and contain non-public and confidential information that may not be reproduced (in writing, orally, photographically) or published to third parties, nor modified or used in any other way without the prior written consent of ABUS Kransysteme GmbH. &copy; ABUS Kransysteme GmbH</span>", "apis/service/copyright": "All after-sales service information circular letters in the ABUS portal and the information contained therein are the exclusive property of ABUS Kransysteme GmbH and contain non-public and confidential information that may not be reproduced (in writing, orally, photographically) or published to third parties, nor modified or used in any other way without the prior written consent of ABUS Kransysteme GmbH.<br><br>&copy; ABUS Kransysteme GmbH", "apis/service/components_/_accessories": "Components / accessories", "apis/service/electric_chain_hoist": "Chain hoist", "apis/service/electrical_engineering": "Electrical engineering", "apis/service/hb-system": "Suspended rail system", "apis/service/jib_crane": "Jib crane", "apis/service/lightweight_mobile_gantry": "Mobile gantry crane", "apis/service/mounting": "assembly", "apis/service/occupational_safety": "Health and safety", "apis/service/organisation_/_communication_/_documentation": "Organisation, communication and documentation", "apis/service/overhead_travelling_crane": "EOT crane", "apis/service/prices": "Prices", "apis/service/qualification": "Qualification", "apis/service/repair": "Repairs", "apis/service/revision": "Revisions", "apis/service/spare_parts": "Spare parts", "apis/service/technical_pre-clarification": "Technical pre-clarification", "apis/service/trolley_track": "Crane track", "apis/service/welding_technology": "Welding technology", "apis/service/wire_rope_hoist": "Wire rope hoist", "apis/service/workshop_/_training": "Workshop training", "apis/service/test_/_inspection": "Workshop / Training ", "apis/service/mail_subject_request_translation": "ABUS information - after-sales service information - please translate", "apis/service/mail_subject_request_publishing": "ABUS information - after-sales service information - please publish", "apis/service/mail_subject_translate": "ABUS information - after-sales service information - translation is complete - please publish", "apis/service/mail_subject_translator_rejection": "ABUS information - after-sales service information - please check acc. to text in internal memo box", "apis/service/mail_subject_publisher_rejection": "ABUS information - after-sales service information - please check acc. to text in internal memo box", "apis/service/mail_subject_publish": "ABUS information - after-sales service information - published!", "apis/service/mail_subject_release_to_customer": "Technical information", "apis/service/mail_text_release_to_customer": "You will find new after-sales service information under the following link:", "apis/mounting/headline": "ABUS Installation information", "apis/mounting/headline_short": "Installation Information", "apis/mounting/headline_description": "", "apis/mounting/copyright": "", "apis/mounting/components_/_accessories": "Components / accessories", "apis/mounting/electric_chain_hoist": "Chain hoist", "apis/mounting/electrical_engineering": "Electrical engineering", "apis/mounting/hb-system": "Suspended rail system", "apis/mounting/jib_crane": "Jib crane", "apis/mounting/lightweight_mobile_gantry": "Mobile gantry crane", "apis/mounting/mounting": "assembly", "apis/mounting/occupational_safety": "Health and safety", "apis/mounting/organisation_/_communication_/_documentation": "Organisation, communication and documentation", "apis/mounting/overhead_travelling_crane": "EOT crane", "apis/mounting/prices": "Occupational safety / law", "apis/mounting/qualification": "Qualifications", "apis/mounting/trolley_track": "Crane track", "apis/mounting/welding_technology": "Welding technology", "apis/mounting/wire_rope_hoist": "Wire rope hoist", "apis/mounting/test_/_inspection": "Workshop / Training ", "apis/mounting/abucontrol": "ABUControl", "apis/mounting/core_drilling_technology": "Core drilling technology", "apis/mounting/dowel_technology": "Dowel technology", "apis/mounting/occupational_safety_/_law": "Occupational safety / law", "apis/mounting/prices_/_bill_of_cost": "Occupational safety / law", "apis/mounting/screwdriving_technology": "Screwdriving technology", "apis/mounting/steel_construction_hb": "Steel construction HB", "apis/mounting/surface_engineering": "Surface engineering", "apis/mounting/technical_clarification": "technical clarification", "apis/mounting/tightening_torques": "Tightening torques", "apis/mounting/mail_subject_request_translation": "ABUS information - installation information - please translate", "apis/mounting/mail_subject_request_publishing": "ABUS information - installation information - please publish", "apis/mounting/mail_subject_translate": "ABUS information - installation information - translation is complete - please publish", "apis/mounting/mail_subject_translator_rejection": "ABUS information - installation information - please check acc. to text in internal memo box", "apis/mounting/mail_subject_publisher_rejection": "ABUS information - installation information - please check acc. to text in internal memo box", "apis/mounting/mail_subject_publish": "ABUS information - installation information - published!", "apis/mounting/mail_subject_release_to_customer": "ABUS information - new installation information available", "apis/mounting/mail_text_release_to_customer": "You will find new installation information under the following link:", "apis/technical/headline": "ABUS Technical information", "apis/technical/headline_short": "Technical information", "apis/technical/headline_description": "", "apis/technical/copyright": "", "apis/technical/components_/_accessories": "Components / accessories", "apis/technical/hb-system": "Suspended rail system", "apis/technical/electric_chain_hoist": "Chain hoist", "apis/technical/overhead_travelling_crane": "EOT crane", "apis/technical/lightweight_mobile_gantry": "Mobile gantry crane", "apis/technical/jib_crane": "Jib crane", "apis/technical/wire_rope_hoist": "Wire rope hoist", "apis/technical/test_/_inspection": "Workshop / Training ", "apis/technical/electrics_/_electronics": "Electrics/Electronics", "apis/technical/mechanics": "Mechanics", "apis/technical/radio_control": "radio remote control", "apis/technical/abukonfis": "ABUKonfis", "apis/technical/mail_subject_request_translation": "ABUS information - technical information - please translate", "apis/technical/mail_subject_request_publishing": "ABUS information - technical information - please publish", "apis/technical/mail_subject_translate": "ABUS information - technical information - translation is complete - please publish", "apis/technical/mail_subject_translator_rejection": "ABUS information - technical information - please check acc. to text in internal memo box", "apis/technical/mail_subject_publisher_rejection": "ABUS information - technical information - please check acc. to text in internal memo box", "apis/technical/mail_subject_publish": "ABUS information - technical information - published!", "apis/technical/mail_subject_release_to_customer": "ABUS information - new technical information available", "apis/technical/mail_text_release_to_customer": "You will find new technical information under the following link:", "apis/german": "German", "apis/english": "English", "apis/french": "French", "apis/spanish": "Spanish", "apis/loading": "Loading...", "apis/settings": "Settings", "apis/settingsDescription": "I would like to receive an email notification on recently published ABUS Sales Information in the following categories.", "apis/nothingFound": "No information found", "apis/nothingFoundDescription": "Unfortunately, no information could be found related to your search request.", "apis/cancel": "Cancel", "apis/save": "Save", "apis/my_favorites": "My Favourites", "apis/show_all": "Show all", "apis/search": "Search", "apis/reset": "Reset", "apis/searchtext": "Search text", "apis/fromDate": "From date", "apis/untilDate": "To date", "apis/filter": "Filter", "apis/receiverlist": "List of recipients", "apis/name": "Surname", "apis/company": "Company", "apis/email": "E-mail", "apis/addFavorite": "Add to Favourites", "apis/removeFavorite": "Remove from Favourites", "apis/saveAsPDF": "Save as PDF", "apis/print": "Print", "apis/referenceBeginning": "This information", "apis/referenceBeginningPast": "This information was", "apis/complements": "complements", "apis/complementedBy": "complemented by", "apis/replaces": "replaces", "apis/replacedBy": "replaced by", "apis/salutation": "Dear Sir<PERSON>,", "apis/goodbye": "With best regards,", "apis/readMore": "Read more", "apis/unreadInformation": "Unread information", "apis/markedByReceivers": "Marked as read", "apis/markAsRead": "Mark this information as read", "apis/markAsReadExplanation": "Please mark the information as read once you have read it.The information will then lose the <span class=\"badge badge-danger\">New</span> label and will disappear from the dashboard (home page) section <i>Unread Information</i>.", "apis/markedAsRead": "Marked as read", "apis/clear": "Reset", "apis/title": "Title", "apis/author": "Author", "apis/date": "Date", "apis/responsible_for_content_and_wording": "Responsible for contents and editing", "apis/gummersbach": "Gummersbach", "apis/abus": "ABUS Kransysteme GmbH", "apis/address_1": "Sonnenweg 1", "apis/address_2": "51647 Gummersbach, Germany", "apis/address_3": "Phone +49 2261/37 -", "apis/attached": "Enclosure", "apis/no": "no.", "apis/gb": "D", "apis/admin/new": "New", "apis/admin/edit": "Edit", "apis/admin/unpublish": "Withdraw publication", "apis/admin/firstViewed": "First viewed", "apis/admin/lastViewed": "Last viewed", "apis/admin/gotEmail": "Informed by e-mail", "apis/admin/optout": "E-mail cancelled", "apis/admin/unpublishTitle": "Withdraw publication", "apis/admin/unpublishMessage": "Published information cannot be edited and thus has to be set to the state before publication.", "apis/admin/administration": "Administration", "apis/admin/waitingforrelease": "Published but not yet visible for users due to publishing date in the future", "apis/admin/waitingforpublishing": "'Waiting for publication", "apis/admin/waitingfortranslation": "Waiting for translation", "apis/admin/drafts": "Drafts", "apis/admin/leave_adminsitration": "Leave administration", "apis/admin/backendIndex": "Administration", "apis/admin/create_new_information": "Create new information", "apis/admin/ready": "I have finished,", "apis/admin/pleaseTranslate": "please translate and publish", "apis/admin/pleasePublish": "please publish", "apis/admin/backToAuthor": "Return to author", "apis/admin/publish": "Publish", "apis/admin/chooseNumber": "Please select a number", "apis/admin/createInformation": "Create information", "apis/admin/translate": "Please translate the complete German contents", "apis/admin/references": "References", "apis/admin/receivers": "Receiver", "apis/admin/receiverList": "View list of recipients", "apis/admin/categories": "Categories", "apis/admin/author": "Author", "apis/admin/publishing": "Publication", "apis/admin/memo": "Internal memo", "apis/admin/authorName": "Surname", "apis/admin/authorMail": "E-mail", "apis/admin/authorPhone": "Direct phone number", "apis/admin/releaseDate": "Publication date", "apis/admin/sendMail": "Inform recipient(s) by e-mail following publication", "apis/admin/store": "Save", "apis/admin/storeAndClose": "Save & close", "apis/admin/searchReference": "Search for reference using name or number", "apis/admin/reason": "Reason", "apis/admin/replacement": "Is being replaced by new information", "apis/admin/deleteReference": "Delete reference", "apis/admin/saved": "Saved", "apis/admin/successfullySaved": "Saved successfully", "apis/admin/requirements": "Minimum specifications", "apis/admin/requirements_description": "In order to release the information for translation or publication, the following criteria must be fulfilled:", "apis/admin/requirement_title": "German title", "apis/admin/requirement_text": "German text", "apis/admin/requirement_category": "At least one category", "apis/admin/requirement_receiver": "At least one recipient", "apis/admin/requirement_author_name": "Author's name", "apis/admin/requirement_author_mail": "Author's e-mail address", "apis/admin/requirement_author_phone": "Author's direct phone number", "apis/admin/requirement_release_date": "Publication date", "apis/admin/title": "Title", "apis/admin/attachmentsAndImages": "Attachments and photos", "apis/admin/dropzone": "Drag attachments and photos to here", "apis/admin/makeGlobal": "Make available in all languages", "apis/admin/isGlobal": "Available in all languages", "apis/admin/removeAttachment": "Remove attachment", "apis/admin/removeImage": "Remove photo", "apis/admin/makeAttachment": "Add photo as attachment", "apis/admin/makeImage": "Add attachment as photo", "apis/admin/attachment": "Attachment", "apis/admin/image": "Photo", "apis/admin/global": "Global", "apis/admin/local": "Local", "apis/admin/delete": "Delete", "apis/admin/maxSize": "Max. file size per file", "apis/admin/PORTAL_INFORMATION_SALES_INTERN": "Internal", "apis/admin/PORTAL_INFORMATION_SALES_INLAND": "Interior", "apis/admin/PORTAL_INFORMATION_SALES_EXPORT": "Export", "apis/admin/PORTAL_CRM_INNENDIENST_DE_NEWS": "Back office CRM news", "apis/admin/PORTAL_CRM_VERTRIEB_DE_NEWS": "Representative Domestic CRM News", "apis/admin/PORTAL_INFORMATION_MOUNTING_AUFTRAGSZENTRUM_DE": "Order Center DE", "apis/admin/PORTAL_INFORMATION_MOUNTING_INTERN": "Internal", "apis/admin/PORTAL_GRUPPE_VERTRIEBSPARTNER_INLAND": "Sales Germany", "apis/admin/PORTAL_GRUPPE_TECHNISCHER_AUSSENDIENST": "Technical Representative", "apis/admin/PORTAL_GRUPPE_MONTAGEPARTNER": "Mounting partners", "apis/admin/PORTAL_GRUPPE_SERVICEPARTNER": "Service partner", "apis/admin/PORTAL_GRUPPE_SERVICEPARTNER_BASIC": "Service partner (Basic)", "apis/admin/PORTAL_GRUPPE_SERVICEPARTNER_MASTER": "Service partner (Master)", "apis/admin/PORTAL_GRUPPE_ABNAHMEPARTNER": "Test engineers", "apis/admin/PORTAL_INFORMATION_SERVICE_INTERN": "Internal", "apis/admin/PORTAL_INFORMATION_SERVICE_TOECHTER": "Subsidiaries", "apis/admin/PORTAL_INFORMATION_SERVICE_EXPORTPARTNER": "Export partners", "apis/admin/PORTAL_INFORMATION_TECHNIK_INTERN": "Internal", "apis/admin/PORTAL_INFORMATION_TECHNIK_INLAND": "Interior", "apis/admin/PORTAL_INFORMATION_TECHNIK_TOECHTER": "Subsidiaries", "apis/admin/PORTAL_INFORMATION_TECHNIK_EXPORTPARTNER": "Export partners", "jobApplication/headline": "Online application", "jobApplication/log": "Log records", "jobApplication/administration": "Administration", "jobApplication/jobtitle": "Job description", "jobApplication/gender": "Form of address", "jobApplication/title": "Title", "jobApplication/firstname": "First name", "jobApplication/lastname": "Last name", "jobApplication/created": "Received", "jobApplication/transferData": "Data transmitted to buffer server", "jobApplication/viewApplication": "View application", "jobApplication/viewTitle": "Preview", "jobApplication/deleteTitle": "Do you really want to delete the application?", "jobApplication/deleteMessage": "<p><strong>This application will be deleted permanently and cannot be restored.</strong></p>", "jobApplication/buttonCancel": "Cancel", "jobApplication/buttonDelete": "Delete", "jobApplication/buttonTransferApplication": "Transfer application", "jobApplication/buttonDeleteApplication": "Delete application", "jobApplication/buttonPrint": "Print", "jobApplication/buttonPDF": "PDF", "jobApplication/search": "Search", "jobApplication/mr": "Mr.", "jobApplication/mrs": "Mrs.", "jobApplication/date": "Date", "jobApplication/dataset": "log data record", "jobApplication/user": "User", "taskrunner/close": "Close", "taskrunner/task_is_running": "Task in progress ...", "taskrunner/you_will_be_notified": "You will be informed by e-mail as soon as the task has been completed.", "taskrunner/error_occured": "Error occurred", "taskrunner/error_while_executing": "An error occurred during execution of your task. Please contact us and report the error.", "taskrunner/total_time": "Total time", "taskrunner/deleted_files": "Deleted files", "modal/cancel": "Cancel", "modal/ok": "Ok", "modal/delete": "Delete", "modal/Access Denied.": "You are not authorised to carry out this action.", "modal/errorTitle": "An error has occurred.", "catalog/preisliste": "price list", "catalog/ersatzteilkatalog": "Spare parts catalogue", "update/message": "An update is currently being installed, because we are always trying to further develop the ABUS portal and make it even better for you. Usually an update is completed within 5 minutes and you can continue using the portal as usual.<br><br>To reload the page, please update it via your browser.", "errorpage/403/message": "You were denied access to this site", "errorpage/403/description": "This can have several reasons:</b><br><br><br><li><you do not have the necessary rights</li><li><li>You have been given new rights that have not yet been updated.<br><a href='/logout'>Please log in again.</a></li>", "errorpage/404/message": "The page could not be found", "errorpage/404/description": "Back to the homepage <a href='/'>portal.abus-kransysteme.de</a>", "errorpage/500/message": "Unexpected error occurred", "errorpage/500/description": "If the error continues, please send us an email to <a href='mailto:<EMAIL>'><EMAIL></a>", "videoconference/predefinedRooms": "Predefined rooms", "videoconference/joinDepartmentRoom": "Join Department Conference Room", "videoconference/joinPersonalRoom": "Join Personal Conference Room", "videoconference/createRoom": "Create a room", "videoconference/roomName": "Room name", "videoconference/create": "Create", "videoconference/onlyAlphanumericAllowed": "Only alphanumeric characters and hyphens are allowed in the room name", "videoconference/joinCompanyRoom": "Join Company Conference Room", "videoconference/invalidRoomName": "Invalid room name", "videoconference/inviteLink": "Invitation link", "videoconference/doYouReallyWantToDelete": "Do you really want to delete this room?", "videoconference/roomAlreadyExists": "A room with this name already exists.", "videoconference/moderationLink": "Moderator link", "videoconference/attentionModerationLink": "Attention. This link grants moderation rights within the linked video conference.", "videoconference/instructionsText": "Here you will find the short instructions for operation:", "videoconference/instructionsHeader": "Instructions", "videoconference/videoInstructions": "Video instruction", "videoconference/shortInstructions": "Quick guide", "service/passwordGenerator/headline": "ABUS Service - Password Generator", "service/passwordGenerator/number": "Number", "service/passwordGenerator/password": "Password", "service/activedirectory/create/headline": "Active Directory - Create user", "service/activedirectory/check/headline": "Active Directory - Check user", "service/activedirectory/feedback/headline": "Active Directory - AD feedback", "service/activedirectory/selectUser": "Select user", "reporting/users/list": "All users", "reporting/users/lastname": "Last name", "reporting/users/firstname": "First name", "reporting/users/company": "Company", "reporting/users/TData": "TData", "reporting/users/lastvisit": "Last login", "reporting/users/whencreated": "Creation date", "reporting/users/Doku": "Do<PERSON>", "reporting/users/show": "Show user", "ABUS Portal - Registration": "ABUS Portal - Registration", "ABUS Technical Data - Registration": "ABUS Technical Data - Registration", "ABUS Portal - Registration - Complete": "ABUS Portal - Registration - Complete", "ABUS Technical Data - Registration - Complete": "ABUS Technical Data - Registration - Complete", "ABUS Portal - Further services requested": "ABUS Portal - Further services requested", "ABUS Portal - Further services requested - Complete": "ABUS Portal - Further services requested - Complete", "ABUS Portal - Request further services": "ABUS Portal - Request further services", "As soon as your registration is activated you will be informed by an email.": "As soon as your registration is activated you will be informed by an email.", "Consultancy Engineer": "Consultancy Engineer", "Architect": "Architect", "Planning of Structural Framework": "Planning of Structural Framework", "Planning Department": "Planning Department", "Technical Office": "Technical Office", "Reseller": "Reseller", "Distributor selling though catalogues": "Distributor selling though catalogues", "Crane Construction Company": "Crane Construction Company", "ABUS Technical Data": "ABUS Technical Data", "Your access to the ABUS Portal": "Your access to the ABUS Portal", "Dear Madam or Sir,": "Dear <PERSON><PERSON> or Sir,", "Thank you for your interest in the ABUS product range!": "Thank you for your interest in the ABUS product range!", "We would be pleased to provide you with well-founded technical data of our products for the planning or conception of your project.": "We would be pleased to provide you with well-founded technical data of our products for the planning or conception of your project.", "The ABUS technical data are only accessible to registered users who log on to the ABUS portal with their personal e-mail address. As access requirements, we therefore need some information from you and ask you to provide us with this below:": "The <b>ABUS technical data</b> are only accessible to registered users who log on to the ABUS portal with their <b>personal e-mail address</b>. As access requirements, we therefore need some information from you and ask you to provide us with this below:", "Your request will be checked and, if approved, further services will be activated promptly. You will receive the confirmation of the activation via email to the email address provided by you.": "Your request will be checked and, if approved, further services will be activated promptly. You will receive the confirmation of the activation via email to the email address provided by you.", "There are no further services to request": "There are no further services to request.", "Thank you for your kind interest in further ABUS services.": "Thank you for your kind interest in further ABUS services.", "We will check your request and you will then be authorised to access these services.": "We will check your request and you will then be authorised to access these services.", "Please observe that new services are only available after authorisation and a new log in.": "Please observe that new services are only available after authorisation and a new log in.", "Request": "Request", "newregister/status": "Check reply status", "newregister/registrationChecked": "Your registration will be checked and, if approved, activated promptly. You will receive the confirmation of the activation via email to the email address provided by you.", "newregister/pleaseRegister": "Please register only if you are a partner company known to, and authorised by, the company ABUS Kransysteme GmbH. We reserve the right of any activation of access to the services.", "newregister/registration": "Registration", "newregister/notice": "Important notice", "newregister/company": "Company:", "newregister/customernumber": "Customer number:", "newregister/position": "Position:", "newregister/branch": "Branch", "newregister/genderTitle": "Gender/Title:", "newregister/gender": "Gender", "newregister/title": "Title", "newregister/mr": "Mr.", "newregister/mrs": "Mrs.", "newregister/firstname": "First name:", "newregister/lastname": "Last name:", "newregister/name": "Name:", "newregister/street": "Street:", "newregister/postcodeTown": "Postcode/Town:", "newregister/postcode": "Postcode", "newregister/location": "Locaton:", "newregister/town": "Town", "newregister/country": "Country:", "newregister/telephone": "Telephone:", "newregister/email": "Email:", "newregister/adUsername": "AD Username:", "newregister/password": "Password:", "newregister/subsidiary": "ABUS Subsidiary", "newregister/distributor": "ABUS Distributor (Dealer / Reseller / Agent)", "newregister/services": "Services, Contracting Companies and Institutions", "newregister/werksvertretung": "ABUS Werksvertretung (Deutschland)", "newregister/mitarbeiter": "ABUS Mitarbeiter (Deutschland)", "newregister/monteur": "Monteur (Deutschland)", "newregister/other": "Other", "newregister/legalNotice": "Legal notice", "newregister/next": "Next", "newregister/serviceRequest": "Which services do you like to request?", "newregister/pleaseProvide": "Please provide ...", "newregister/registerComplete": "Registration complete", "newregister/requestingAccess": "Thank you for requesting an access to the ABUS online services. We received your data and will process them soon.", "newregister/requestedServices": "Requested services:", "newregister/submittedData": "You have submitted following data:", "newregister/dataprotection": "Data Privacy Statement", "newregister/moreInformation": "You will find more detailed information in our ", "newregister/dataprotectionText": "Please note that this portal is a service for commercial participants. When using the configurators, your data will be transferred to other ABUS companies or external sales partners in order to provide you, simply and quickly, with suitable offers and optimized customer support.", "newregister/chooseService": "... choose at least one service", "service/There are potential collisions with existing users": "There are potential collisions with existing users", "service/language for mail": "Select the language for the automatic mailing", "service/english": "English", "service/german": "German", "service/usergroup": "User group", "service/otherGroups": "Other groups", "service/requestedService": "Requested service", "service/adGroup": "AD group", "service/createUser": "Create User", "service/rejected": "REJECTED", "service/approved": "APPROVED", "service/not yet approved": "not yet approved", "service/mailGenerator/headline": "ABUS Service - Reply mail generator", "service/mailGenerator/language": "Language", "service/mailGenerator/typ": "<PERSON><PERSON>", "service/mailGenerator/abus_Portal_New_logon": "ABUS Portal - New Logon", "service/mailGenerator/abus_Portal_Add_new_services": "ABUS Portal - Further services requested", "service/mailGenerator/abus_Portal_Reset_password": "ABUS Portal - Reset password", "service/mailGenerator/abus_Technical_Data_New_logon": "ABUS technical data - New logon", "service/mailGenerator/abus_Technical_Data_Reset_password": "ABUS technical data - Reset password", "service/mailGenerator/abus_Teamroom_New_logon": "ABUS Teamroom - New logon", "service/mailGenerator/abus_Teamroom_Reset_password": "ABUS Teamroom - Reset password", "service/mailGenerator/abus_Media_Reset_password": "ABUS Media - Reset password", "service/mailGenerator/email": "Email", "service/mailGenerator/password": "Password", "service/mailGenerator/generate_Password": "Generate password", "service/mailGenerator/generate_email": "Generate email", "service/mailGenerator/none": "none", "service/mailGenerator/allowed": "allowed", "service/mailGenerator/denied": "denied", "service/mailGenerator/create_email": "preview of the email", "Request_of_new_services_ABUS_Portal": "Thank you for your request for further services in the ABUS portal.", "rejected_by": "Rejected by", "FIRSTNAME_IS_BLANK_ERROR": "... your first name", "LASTNAME_IS_BLANK_ERROR": "... your last name", "POSITION_IS_BLANK_ERROR": "... your position", "STREET_IS_BLANK_ERROR": "... your street", "POSTCODE_IS_BLANK_ERROR": "... your postcode", "TOWN_IS_BLANK_ERROR": "... your town", "COUNTRY_IS_BLANK_ERROR": "... your country", "TELEPHONE_IS_BLANK_ERROR": "... your telephone number", "COMPANY_IS_BLANK_ERROR": "... your company name", "BRANCH_IS_BLANK_ERROR": "... your branch", "EMAIL_IS_BLANK_ERROR": "... your email address", "EMAIL_NOT_VALID_EMAIL": "... your valid email address", "TRY_TO_REGISTER_AN_EXISTING_MAIL_ADDRESS": "... an email address that is not yet registered", "YES": "YES", "NO": "NO", "Thank_your_very_much_for_your_registration_in_the_ABUS_Portal": "Thank you very much for your registration in the ABUS Portal", "Thank_your_very_much_for_your_registration_in_the_ABUS_Technical_Data": "Thank you very much for your registration in the ABUS Technical Data", "We_verified_your_registration_and_activated_the_following_services": "We verified your registration and activated the following services", "For_the_following_services": "For the following services the account activation has been refused", "Your_access_data_to_the_ABUS_portal": "Your access data to the ABUS portal", "are_as_follows": "are as follows", "We_only_use_automatically_generated_passwords": "We only use automatically generated passwords", "For_safety_reasons": "For safety reasons and due to the fact that such passwords are hard to memorise, you should change your password after your fist ABUS Portal login.", "LDAP_USER_WAS_NOT_CREATED_BECAUSE_IT_ALREADY_EXISTS": "The user was not created because it already exists", "LDAP_USER_WAS_SUCCESSFULLY_CREATED": "The user was successfully created", "EMAIL_WAS_SUCCESSFULLY_SEND": "The email was sent successfully!", "ABUS_Portal_Registration": "ABUS Portal - Registration", "ABUS_Technical_Data_Registration": "ABUS Technical Data - Registration", "Dear_Madam_or_Sir": "Dear <PERSON><PERSON> or Sir", "Thank_you_for_using_the": "Thank you for using the", "ABUS_Technical_Data": "ABUS Technical Data", "for_the_planning_and_design": "for the planning and design of your material handling solution", "We_verified_your_registration_and_activated_your_account": "We verified your registration and activated your account", "Your_access_data_to_the_ABUS_Technical_Data": "Your access data to the ABUS Technical Data", "Allow access": "Allow access", "The_use_of_ABUS_Technical_Data_is_easy": "", "Generate_PDF_documents": "", "Generate_2D_3D_data": "", "Explanation_of_the_colors_in_the_selection_menus": "", "blue_minimum_input": "", "white_does_not_have_to_select": "", "green_function_is_possible": "", "red_function_is_not_possible": "", "Lots_of_additional_equipment_or_projectrelated_designs": "", "For_any_questions": "", "account/accessManagement/success/update/data": "Data has been successfully transferred!", "account/accessManagement/users/updatetable": "Update of the table of users", "account/accessManagement/delete/denied": "Your request has been denied.", "The_requester_was_informed_of_the_decision": "The requester was informed of the decision.", "account/workflow/headline": "ABUS Account workflow", "account/workflow/type/delete": "delete", "account/workflow/type/create": "ABUKonfis", "account/workflow/type/reassign": "reassign", "account/workflow/type/request": "request", "account/workflow/type/vpn_create": "VPN Access", "account/workflow/type/vpn_reassign": "VPN reassignment", "account/workflow/type/type_request": "request", "account/workflow/type/type_reassign": "token", "account/list/sales/information": "List Sales Information", "account/list/services/information": "List service Information", "account/list/tdata/information": "List Technical Data Information", "account/list/mounting/information": "List Mounting Information", "account/vpn/reassign": "Reassign VPN access", "account/from/user": "user", "account/vpn_reassigned": "We've reassigned VPN", "account/vpn_send": "We've send a new VPN Access", "account/workflow/open": "Open processes", "account/workflow/close": "Completed processes", "account/workflow/denied": "Rejected processes", "account/workflow/action": "Action", "account/workflow/User": "Username", "account/workflow/Remark": "Comment", "OVISS user": "OVISS user", "account/workflow/AD_Username": "AD Username", "account/workflow/OVISS": "<PERSON><PERSON><PERSON>", "account/workflow/Token_serial_number": "Serial Token Number", "account/workflow/Reference_user": "User reference", "account/workflow/Company": "Company", "account/workflow/MA": "MA", "account/workflow/EO": "EO", "account/workflow/IT": "IT", "account/workflow/VO": "VO", "account/workflow/PA": "PA", "account/workflow/ProAlpha": "ProAlpha", "account/workflow/OV": "OV", "account/workflow/reason": "reason", "account/workflow/remark": "remark", "account/workflow/Message": "Message", "account/workflow/Created": "Created:", "account/workflow/lastmodified": "Last modified", "account/workflow/Requested_by": "Requested by:", "account/workflow/token_for_later_usage": "We'll keep the token for later usage", "account/workflow/Token": "token", "Your_request_has_been_successfully_sent": "Your request has been successfully sent", "account/notComplete/form/fields": "Please ensure that all required fields are completed and formatted correctly.", "account/name": "Name", "account/Remark": "Remark", "account/Sales/Information": "Sales Information", "account/Mounting/Information": "Mounting Information", "account/Service/Information": "Service Information", "account/Technical/Information": "Technical Information", "account/Technical/Data": "Technical Data", "account/Spare/Parts/prices": "Spare- Parts prices", "account/allow": "Allow", "account/denied": "Denied", "account/email/verify": "Verify email", "account/please/choose": "Please choose...", "account/new/abuKonfis": "New ABUKonfis user", "account/Reference/user": "Reference user", "account/create": "Create new account", "account/reassign": "Reassign ABUKonfis account", "account/vpn/user": "New VPN user", "Your_request_has_already_be_sent": "your request has already been submitted please wait.", "account/vh": "VH", "account/title": "Titel", "account/First/reading/Date": "First reading Date", "account/Last/reading/Date": "Last reading Date", "account/action/already/done": "Job done", "account/Portal": "Portal", "account/Docu": "Do<PERSON>", "account/BOX": "BOX", "account/PL": "Price lists", "account/Media": "Media", "account/SpareParts": "SpareParts", "account/Spare- Parts prices": "Spare- Parts prices", "account/WinETIS": "WinETIS", "account/AccessManagement": "AccessManagement", "account/VPN": "VPN", "account/ABUKonfis": "ABUKonfis", "account/OVISS": "OVISS", "account/ProALPHA": "ProALPHA", "account/ABUKonfis-WEB": "ABUKonfis-WEB", "account/accessManagement": "ABUS Account Management", "account/listUsers": "List users", "list_description": "This function allows you to list <strong>all users</strong> according to their first name and surname communicated to us.", "delete_description": "This function allows you to request the immediate cancellation of an access to the ABUS Portal and to ABUKonfis, e.g. in the case a staff member will leave the company, or change internally to another position etc.<br /><br /><strong>For safety reasons, the information about a staff member leaving the company must reach us promptly!</strong>", "create_description": "This function allows you to request a new access for ABUKonfis.<br /><br />Please start and select the new user from the list. The selection from this list is only possible under the precondition that this user has already an <a href=\"https://portal.abus-kransysteme.de/register\" rel=\"external\">account</a> (please refer to entry page of the module).<br /><br />Please continue and enter the position or function of the new user, e.g. Sales Manager, field staff, sales of spare parts etc.<br /><br />With your selection of the reference user, you will define to which access authorization the new user will be released under the roof of ABUKonfis.<br /><br />Once you have entered the requested data you can complete the process by a click on “Create access”.", "vpn_create_description": "<strong>In dieser Funktion können Sie einen neuen VPN-Zugang beantragen.</strong><br /><br>Bitte wählen Sie hierzu zunächst aus der Liste den <strong>neuen Benutzer</strong> aus. Voraussetzung ist, dass dieser Benutzer bereits über ein <a href=\"https://portal.abus-kransysteme.de/register\" rel=\"external\">Account</a> bei uns verfügt.<br /><br>Nach Eingabe aller erforderlichen Daten wird der Vorgang durch einen Klick auf das Feld <strong>VPN-Zugang anlegen</strong> abgeschlossen.", "vpn_reassign_description": "In dieser Funktion können Sie innerhalb Ihrer Organisation einen bestehenden VPN-Softtoken auf ein anderes Gerät umziehen. Wählen Sie hierzu zunächst aus der Liste den Namen des bestehende Benutzers aus, dessen VPN-Softtoken auf ein neues Mobilgerät transferiert werden soll.", "account/deleted": "ABUS account deleted", "account/oviss/transfered": "We've transferred OVISS data", "account/oviss/subject/transfer": "ABUS Account Management - Transfer OVISS data", "account/oviss/subtitle/transfer": "Transfer OVISS data", "account/oviss/subtitle/transfered": "OVISS data has been transfered", "account/oviss/subject/transfered": "ABUS Account Management - OVISS data has been transfered", "account/proAlpha/subject/deactivate": "ABUS Account Management - Deactivate ProALPHA account", "account/proAlpha/subtitle/deactivate": "Deactivate ProALPHA account", "account/proAlpha/subtitle/deactivated": "ProALPHA account has been deactivated", "account/proAlpha/subject/deactivated": "ABUS Account Management - ProALPHA account has been deactivated", "account/media/subject/delete": "ABUS Account Management - Delete Media account", "account/media/subtitle/delete": "Delete Media account", "account/academy/subject/delete": "ABUS Account Management - Delete Academy account", "account/academy/subtitle/delete": "Delete Academy account", "account/media/academy/subject/delete": "ABUS Account Management - Delete account: Media, Academy", "account/media/academy/subtitle/delete": "Delete account: Media, Academy", "account/media/subtitle/deleted": "We have deleted the media account", "account/media/subject/deleted": "ABUS Account Management - Media account has been deleted", "account/abukonfis/subject/delete": "ABUS Account Management - Delete ABUKonfis account", "account/abukonfis/subtitle/delete": "Delete ABUKonfis account", "account/abukonfis/subject/deleted": "ABUS Account Management - ABUKonfis account has been denied", "account/abukonfis/subtitle/created": "We have created access for the Abukonfis", "account/account/subject/delete": "ABUS Account Management - Delete ABUS account", "account/account/subject/denied": "ABUS Account Management - Delete ABUS account has been denied", "account/account/subject/deleted": "ABUS Account Management - ABUS account has been deleted", "account/vpn/create/subject/allowed": "ABUS Account Management - Create new VPN access", "account/vpn/create/subtitle/allowed": "Create new VPN access", "account/vpn/create/subject/denied": "ABUS Account Management - New VPN access has been denied", "account/vpn/create/subject/done": "ABUS Account Management - New VPN access has been created", "account/vpn/create/subject/subtitle/done": "New VPN access has been created", "account/vpn/create/subject/subtitle/denied": "New VPN access has been denied", "account/vpn/reassign/subject/allowed": "ABUS Account Management - Reassign VPN access", "account/vpn/reassign/subtitle/allowed": "Reassign VPN access", "account/vpn/reassign/subject/denied": "ABUS Account Management - Reassign VPN access has been denied", "account/vpn/reassign/subject/done": "ABUS Account Management - VPN access has been reassigned", "account/vpn/reassign/subject/subtitle/done": "Reassign VPN access has been denied", "account/abukonfis/subject/allowed": "ABUS Account Management - Create new ABUKonfis account", "account/abukonfis/subtitle/allowed": "Create new ABUKonfis account", "account/abukonfis/subject/denied": "ABUS Account Management - New ABUKonfis account has been denied", "account/abukonfis/subject/done": "ABUS Account Management - New ABUKonfis account has been created", "account/abukonfis/subject/subtitle/done": "New ABUKonfis account has been created", "account/abukonfis/proceed/same/title": "Please proceed in the same manner as for", "account/oviss/receiver": "OVISS Receiver", "abus/account/message": "Message", "abus/account/messages": "Messages", "account/oviss/to/user/transfer": "Transfer OVISS data to user", "account/thank/for/support": "Thank you for your support.", "abus/account/message/placeholder": "You can leave a workflow message here ...", "abus/account/transfer/oviss": "Please transfer OVISS data to following user", "abus/account/delete": "Delete ABUS account", "account/position/function": "Position / Function", "create_footer_information": "Please take into account that, by activating the button „Create user“ the access is not blocked in real-time! For safety reasons, in case of a staff member leaving the company, the token should be returned without delay", "reassign_footer_information": "Please take into account that, by activating the button ”Create access”, the access is not available in real-time. With this button you will start a process which prompts the ABUS IT Service to create the requested access. This procedure may take some working days.", "delete_footer_information": "Please take into account that, by activating the button „Delete user“ the access is not blocked in real-time! For safety reasons, in case of a staff member leaving the company, the token should be returned without delay.", "sales_description": "In this function, all sales information letters are listed which had been sent to the corresponding user. The date specification indicates if and when the sales information letter has been read.<br /><br />Sales information letters which have a red coloured background are information letters from those categories where the user unsubscribed under “My settings”.", "mounting_description": "In this function, all installation information letters are listed which had been sent to the corresponding user. The date specification indicates if and when the installation information letter has been read.<br /><br />Installation information letters which have a red coloured background are information letters from those categories where the user unsubscribed under “My settings”.", "service_description": "In this function, all after-sales service information letters are listed which had been sent to the corresponding user. The date specification indicates if and when the after-sales service information letter has been read.<br /><br />After-sales service information letters which have a red coloured background are information letters from those categories where the user unsubscribed under “My settings”.", "technical_description": "In this function, all technical information letters are listed which had been sent to the corresponding user. The date specification indicates if and when the technical information letter has been read.<br /><br />Technical information letters which have a red coloured background are information letters from those categories where the user unsubscribed under “My settings”.", "The_User_was_deleted_from_Chat": "The following user was successfully deleted from the chat!"}