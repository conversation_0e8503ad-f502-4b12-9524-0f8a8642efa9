if (global.angularApp === undefined ) {
    require('angular');
    require('angular-sanitize');
    require('angular-animate');
    require('angular-touch');
    require('angular-ui-bootstrap');
    require('re-tree');
    require('angular-utf8-base64');

    global.angularApp = angular.module('abus', ['ngSanitize', 'ngAnimate', 'ngTouch', 'ui.bootstrap', 'reTree', 'utf8-base64']);
}

export const app = global.angularApp;
