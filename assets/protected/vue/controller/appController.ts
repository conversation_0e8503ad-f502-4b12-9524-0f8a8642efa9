import Vue from 'vue';
import Vuex from 'vuex';
import { store } from '../store';
import VueI18n from 'vue-i18n';
import { BootstrapVue } from 'bootstrap-vue';

import { VTooltip, VClosePopper, Dropdown, Tooltip, Menu } from 'floating-vue';

import 'bootstrap-vue/dist/bootstrap-vue.css';
import 'floating-vue/dist/style.css';

import Modal from '../component/helper/Modal.vue';
import ModalStoreDriven from '../component/helper/ModalStoreDriven.vue';
import ModalButton from '../component/helper/ModalButton.vue';
import Toast from '../component/helper/Toast.vue';
import SortTable from '@assets/protected/vue/component/helper/SortTable.vue';
import ListUsers from '../component/reporting/ListUsers.vue';
import UserMode from '../component/frame/UserMode.vue';
import ReportProblem from '../component/frame/ReportProblem.vue';
import Dashboard from '../component/frame/dashboard/Dashboard.vue';
import ActiveUsers from '../component/frame/ActiveUsers.vue';
import ApisFrontend from '../component/apis/ApisFrontend.vue';
import ApisBackend from '../component/apis/ApisBackend.vue';
import ApisBackendSidebar from '../component/apis/ApisBackendSidebar.vue';
import ApisBackendReceiverList from '../component/apis/ApisBackendReceiverList.vue';
import ApisCreate from '../component/apis/ApisCreate.vue';
import ApisSearch from '../component/apis/ApisSearch.vue';
import ApisSettings from '../component/apis/ApisSettings.vue';
import ChangePassword from '../component/frame/ChangePassword.vue';
import Profile from '../component/frame/Profile.vue';
import Settings from '../component/frame/Settings.vue';
import DeleteAccount from '../component/frame/DeleteAccount.vue';
import BoxFrontend from '../component/box/BoxFrontend.vue';
import BoxDownloadMode from '../component/box/BoxDownloadMode.vue';
//import BoxAdminSidebar from '../component/box/admin/boxAdminSidebar.vue';
import NextcloudAdminSidebar from '../component/box/admin/NextcloudAdminSidebar.vue';
import Nextcloud from '../component/box/Nextcloud.vue';
import NextcloudDownloadMode from '../component/box/NextcloudDownloadMode.vue';
import NewsCreate from '../component/news/NewsCreate.vue';
import News from '../component/news/News.vue';
import IsValidUsername from '../component/service/IsValidUsername.vue';
import CompanySuggestion from '../component/service/CompanySuggestion.vue';
import PwdGenerator from '../component/service/PwdGenerator.vue';
import Workflows from '../component/accessmanagment/Workflows.vue';
import Archive from '../component/accessmanagment/Archive.vue';
import AccountList from '../component/accessmanagment/AccountList.vue';
import Information from '../component/accessmanagment/Information.vue';
import CreateAbukonfis from '../component/accessmanagment/CreateAbukonfis.vue';
import DeleteAbukonfis from '../component/accessmanagment/DeleteAbukonfis.vue';
import Bookings from '../component/Appointment/Bookings.vue';
import BookingsFilter from '../component/Appointment/BookingsFilter.vue';

import de from '@translations/across/across.de_DE.json';
import en from '@translations/across/across.en_GB.json';
import fr from '@translations/across/across.fr_FR.json';
import es from '@translations/across/across.es_ES.json';

const i18n = new VueI18n({
  locale: document.documentElement.lang || 'de',
  fallbackLocale: 'de',
  messages: { de, en, fr, es },
});

Vue.use(Vuex, VueI18n, BootstrapVue);

Vue.directive('tooltip', VTooltip);
Vue.directive('close-popper', VClosePopper);

Vue.component('VDropdown', Dropdown);
Vue.component('VTooltip', Tooltip);
Vue.component('VMenu', Menu);

new Vue({
  el: '#appController',
  store,
  i18n,
  delimiters: ['<%', '%>'],
  components: {
    'vue-modal': Modal,
    'vue-modal-store': ModalStoreDriven,
    ModalButton,
    Toast,
    UserMode,
    ReportProblem,
    Dashboard,
    ActiveUsers,
    ApisFrontend,
    ApisBackend,
    ApisBackendSidebar,
    ApisBackendReceiverList,
    ApisCreate,
    ApisSearch,
    ApisSettings,
    ChangePassword,
    Profile,
    Settings,
    DeleteAccount,
    BoxFrontend,
    // BoxSearch,
    BoxDownloadMode,
    // BoxAdminSidebar,
    NextcloudAdminSidebar,
    Nextcloud,
    NextcloudDownloadMode,
    NewsCreate,
    News,
    ListUsers,
    SortTable,
    IsValidUsername,
    CompanySuggestion,
    PwdGenerator,
    Workflows,
    Archive,
    AccountList,
    Information,
    CreateAbukonfis,
    DeleteAbukonfis,
    Bookings,
    BookingsFilter,
  },
});

const iframe = document.getElementById('frame-iframe');
if (iframe && iframe.getAttribute('data-src')) {
  document.addEventListener(
    'DOMContentLoaded',
    function () {
      iframe!.setAttribute('src', iframe!.getAttribute('data-src')!);
      iframe!.removeAttribute('data-src');
    },
    false,
  );
}
