import base64 from 'base-64';
import Vue from 'vue';
import Axios from 'axios';

import ModalEventBus from 'EventBuses/modal';

import de from 'Translations/across/across.de_DE.json';
import en from 'Translations/across/across.en_GB.json';
import fr from 'Translations/across/across.fr_FR.json';
import es from 'Translations/across/across.es_ES.json';

import VueI18n from 'vue-i18n';
Vue.use(VueI18n);

const i18n = new VueI18n({
  locale: document.getElementsByTagName('html')[0].getAttribute('lang') as string,
  messages: { de, en, fr, es },
});

const state = {
  downloadMode: false,
  selectedForDownload: {} as Record<string, any>,
  downloadCount: 0,
  downloadSize: 0,
  openFolder: '',
};

const mutations = {
  setDownloadMode: (state, payload: boolean) => {
    state.downloadMode = payload;
    if (payload === false) {
      state.selectedForDownload = {};
      state.downloadSize = 0;
      state.downloadCount = 0;
    }
  },

  addDownload: (state, payload) => {
    state.downloadSize += Number(payload.size);
    state.selectedForDownload[payload.fileid] = payload;
    state.downloadCount++;
  },

  removeDownload: (state, payload) => {
    state.downloadSize -= Number(payload.size);
    delete state.selectedForDownload[payload.fileid];
    state.downloadCount--;
  },

  openFolder: (state, payload: string) => {
    const pathParts = base64
      .decode(payload)
      .split('/')
      .map((part) =>
        encodeURIComponent(part)
          .replace(/!/g, '%21')
          .replace(/'/g, '%27')
          .replace(/\(/g, '%28')
          .replace(/\)/g, '%29')
          .replace(/\*/g, '%2A'),
      );
    state.openFolder = pathParts.join('/');
  },

  createTagTask: (_state) => {
    const url = '/taskrunner/create';
    const form = new URLSearchParams();
    form.set('bundleName', 'ABUSBox');
    form.set('task', 'nextcloudTags');

    Axios.post(url, form, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    })
      .then(() => {
        ModalEventBus.$emit('showModal', {
          type: 'abus-blue-dark',
          title: i18n.t('box/admin/tags'),
          message: i18n.t('box/admin/tags_modal_text'),
        });
      })
      .catch((error) => {
        ModalEventBus.$emit('showModal', { type: 'error', message: error?.response?.data ?? String(error) });
      });
  },

  createIndexTask: (_state, payload) => {
    const url = '/taskrunner/create';
    const form = new URLSearchParams();
    form.set('bundleName', 'ABUSBox');
    form.set('task', 'nextcloudIndex');
    // If payload is an object, you may need to stringify it depending on your backend expectations:
    // form.set('parameters', JSON.stringify(payload));
    // If backend expects nested form fields (e.g. parameters[foo]=bar), set them individually instead.
    form.set('parameters', typeof payload === 'string' ? payload : JSON.stringify(payload));

    Axios.post(url, form, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    })
      .then(() => {
        ModalEventBus.$emit('showModal', {
          type: 'abus-blue-dark',
          title: i18n.t('box/admin/index'),
          message: i18n.t('box/admin/index_modal_text'),
        });
      })
      .catch((error) => {
        ModalEventBus.$emit('showModal', { type: 'error', message: error?.response?.data ?? String(error) });
      });
  },
};

const actions = {
  setDownloadMode: ({ commit }, payload: boolean) => commit('setDownloadMode', payload),
  toggleDownloadMode: ({ commit }) => commit('setDownloadMode', !state.downloadMode),
  addDownload: ({ commit }, payload) => commit('addDownload', payload),
  removeDownload: ({ commit }, payload) => commit('removeDownload', payload),
  openFolder: ({ commit }, payload) => commit('openFolder', payload),
  createTagTask: ({ commit }) => commit('createTagTask'),
  createIndexTask: ({ commit }, payload) => commit('createIndexTask', payload),
};

const getters = {
  getDownloadMode: (state) => state.downloadMode,
  getDownloadSize: (state) => (state.downloadSize / 1048576).toFixed(2),
  getSelectedForDownload: (state) => state.selectedForDownload,
  getSelectedForDownloadCount: (state) => state.downloadCount,
  isSelectedForDownload: (state) => (fileid: string) =>
    Object.prototype.hasOwnProperty.call(state.selectedForDownload, fileid),
  getOpenFolder: (state) => state.openFolder,
};

export default {
  state,
  mutations,
  actions,
  getters,
  namespaced: true,
};
