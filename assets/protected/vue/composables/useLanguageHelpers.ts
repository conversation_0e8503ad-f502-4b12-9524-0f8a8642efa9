export function useLanguageHelpers() {
  const languageToLocale = (key: string): string => {
    switch (key.toLowerCase()) {
      case 'de':
      case 'ger':
        return 'de_DE';
      case 'es':
      case 'esl':
        return 'es_ES';
      case 'fr':
      case 'fre':
        return 'fr_FR';
      case 'gb':
      case 'eng':
        return 'en_GB';
      default:
        return '';
    }
  };

  /**
   * Konvertiert ein Array von Sprachcodes zu HTML-String mit Flaggen-Bildern
   * @param languages Array von Sprachcodes (z.B. ['de', 'en', 'fr'])
   * @returns HTML-String mit img-Tags für Flaggen
   */
  const languagesToFlags = (languages: string[] | null): string => {
    if (!languages || languages.length === 0) return '';

    let flags = '';
    for (const language of languages) {
      flags += `<img src="${getLanguageFlagPath(language)}" class="flag">`;
    }

    return flags;
  };

  /**
   * Erst<PERSON><PERSON> den Pfad zu einem Flaggen-Bild für einen Sprachcode
   * @param language Sprachcode (z.B. 'de', 'en', 'fr')
   * @returns Pfad zum Flaggen-Bild
   */
  const getLanguageFlagPath = (language: string): string => {
    const flagCode = language
      .toLowerCase()
      .replace('ger', 'de')
      .replace('esl', 'es')
      .replace('fre', 'fr')
      .replace('eng', 'gb')
      .replace('en', 'gb')
      .replace('eu', 'aq');
    return `/build/images/public/node_modules/svg-country-flags/png100px/${flagCode}.png`;
  };

  return {
    languageToLocale,
    languagesToFlags,
    getLanguageFlagPath,
  };
}
