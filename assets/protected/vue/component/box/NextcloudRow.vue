<template>
  <div>
    <div
      :id="id"
      class="col-md-12 boxRow"
      :class="{
        isDir: folder.isDir,
        isFile: folder.isFile,
        highlight: highlight,
        selectedForDownload: getDownloadMode && selected,
      }">
      <div class="row clickable" @click="loadContent('', true)">
        <div class="col-md-9 folderFileName" :style="'padding-left: ' + 10 * recursionLoop + 'px'">
          <div>
            <font-awesome-icon
              v-if="folder.isDir"
              :icon="content === false ? 'folder' : 'folder-open'"></font-awesome-icon>

            <div class="checkboxContainer">
              <div v-if="getDownloadMode && folder.isFile" class="custom-control custom-checkbox">
                <input
                  :id="'downloadMode_' + id"
                  class="custom-control-input"
                  type="checkbox"
                  :value="true"
                  :checked="isSelectedForDownload(folder.fileid)"
                  @click.stop.prevent />
                <label class="custom-control-label" :for="'downloadMode_' + id"></label>
              </div>
            </div>

            <span v-if="folder.isDir" v-html="folder.filenameURLDecoded"></span>
            <span v-if="folder.isFile" v-html="folder.filenameURLDecoded + '.' + folder.extension"></span>
          </div>
          <Spinner v-show="loading" class="loadingIndicator" size="small"></Spinner>
        </div>

        <div class="col-md-3">
          <div v-if="recursionLoop > 1 || showFlagsOnRoot" class="flags">
            <img
              v-for="language in folder.languages"
              :key="language"
              :src="getLanguageFlagPath(language)"
              :alt="language" />
          </div>

          <div v-if="folder.isFile" class="date">
            <span v-html="formatDate(folder.lastmodified)"></span>
          </div>
        </div>
      </div>
    </div>

    <nextcloud-row
      v-for="folder in content.childs"
      :key="folder.fileid"
      :folder="folder"
      :is-admin="isAdmin"
      :downloadMode="getDownloadMode"
      :recursionLoop="recursionLoop + 1"></nextcloud-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import axios from 'axios';
import base64 from 'base-64';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import Spinner from '@assets/protected/vue/component/helper/Spinner.vue';
import ModalEventBus from 'EventBuses/modal';
import { useScroll } from '@assets/protected/vue/composables/useScroll';
import { useDateHelpers } from '@assets/protected/vue/composables/useDateHelpers';
import { store } from '@assets/protected/vue/store';
import { useLanguageHelpers } from '@assets/protected/vue/composables/useLanguageHelpers';

interface Props {
  folder: any;
  isAdmin?: boolean;
  recursionLoop?: number;
  showFlagsOnRoot?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isAdmin: false,
  recursionLoop: 1,
  showFlagsOnRoot: false,
});

const { getLanguageFlagPath } = useLanguageHelpers();
const scroll = useScroll({ offset: -150 });
const { formatDate } = useDateHelpers();

const getDownloadMode = computed<boolean>(() => store.getters['box/getDownloadMode']);
const isSelectedForDownload = store.getters['box/isSelectedForDownload'] as (code: string | number) => boolean;
const getOpenFolder = computed<string>(() => store.getters['box/getOpenFolder']);

const addDownload = (payload: unknown) => store.dispatch('box/addDownload', payload);
const removeDownload = (payload: unknown) => store.dispatch('box/removeDownload', payload);

const loading = ref(false);
const content = ref<any | false>(false);
const highlight = ref(false);
const id = ref('');
const selected = ref(false);

watch(getOpenFolder, () => {
  onGetOpenFolderChanged();
});

function onGetOpenFolderChanged(): void {
  let pathParts = base64.decode(props.folder.fullpathBase64encoded).split('/');

  for (let i in pathParts) {
    // Muss rawurlencode von PHP nachstellen
    pathParts[i] = pathParts[i]
      .replace(/!/g, '%21')
      .replace(/'/g, '%27')
      .replace(/\(/g, '%28')
      .replace(/\)/g, '%29')
      .replace(/\*/g, '%2A');
  }

  let path = pathParts.join('/');

  let regExString = '^' + path.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g, '\\$&').toLowerCase();
  let regexp = new RegExp(regExString);

  if (getOpenFolder.value.toLowerCase().match(regexp)) {
    loadContent(getOpenFolder.value, false);
    scroll.scrollToSelector('#id' + props.folder.fileid);
  }

  if (getOpenFolder.value.toLowerCase() === path.toLowerCase()) {
    highlight.value = true;
  }
}

/**
 * Lädt den Content in einem Folder
 *
 * @param {string} path
 * @param openFile
 */
function loadContent(path: string = '', openFile: boolean = false): void {
  // Offenen Content wieder zuklappen
  if (path === '' && content.value !== false) {
    content.value = false;
  } else if (content.value === false) {
    // Content laden

    // Ordnerinhalt laden
    if (props.folder.isDir) {
      loading.value = true;

      let url = '/box/nextcloud/get' + props.folder.fullpath;

      url = url.replace(/\/$/, '');

      axios
        .get(url)
        .then((response: any) => {
          content.value = response.data;
          loading.value = false;
        })
        .catch((error: any) => {
          loading.value = false;
          ModalEventBus.$emit('showModal', { type: 'error', message: error.response.data.message });
        });
    } else {
      if (getDownloadMode.value === false) {
        // Extensions die in einem neuen Fenster geöffnet werden können
        let extensions = ['pdf', 'jpg', 'jpeg', 'gif', 'png', 'txt'];
        let target = '_self';

        if (extensions.indexOf(props.folder.extension) !== -1) {
          target = '_blank';
        }

        if (openFile) {
          window.open('/box/nextcloud/download' + props.folder.fullpath, target);
        }
      } else {
        isSelectedForDownload(props.folder.fileid) ? removeDownload(props.folder) : addDownload(props.folder);
        selected.value = isSelectedForDownload(props.folder.fileid);
      }
    }
  }
}

function languageFlag(localeParam: string): string {
  let localeCode = localeParam.toLowerCase();

  if (localeCode === 'en') localeCode = 'gb';

  return `/Flags/png100px/${localeCode}.png`;
}

onMounted(() => {
  id.value = 'id' + props.folder.fileid;
  selected.value = isSelectedForDownload(props.folder.fileid);
  onGetOpenFolderChanged();
});
</script>

<style lang="scss" scoped>
@import '@assets/public/vendor/dynamicTheme/css/bootstrapCustomScss/custom-variables.scss';

.boxRow {
  font-size: 0.9rem;
  border-bottom: 1px solid #e3e3e3;

  &.isDir {
    background-color: #ededed;

    &:hover {
      background-color: #f5f5f5;
    }
  }

  &.isFile {
    background-color: #ffffff;
  }

  &.isPublic {
    background-color: #dff0d8;

    &:hover {
      background-color: #e7efe3;
    }
  }

  &.notAccessible {
    background-color: #f2dede;

    &:hover {
      background-color: #efe8e8;
    }
  }

  &.highlight {
    background-color: $abus-blue-light;
    color: $white;

    .downloads {
      color: $white !important;
    }
  }

  &.selectedForDownload {
    background-color: $abus-yellow-light;
  }

  .clickable {
    cursor: pointer;
  }

  .folderFileName {
    line-height: 40px;

    i {
      margin-right: 5px;
    }

    div {
      float: left;
    }

    .loadingIndicator {
      margin-left: 10px;
      top: 50%;
      position: relative;
      transform: translateY(-50%);
    }

    .downloads {
      font-size: 0.6em;
      padding-left: 10px;
      color: #797979;
      vertical-align: middle;
    }
  }

  .date,
  .flags {
    line-height: 40px;
    float: right;
    margin-right: 10px;
  }

  /*.externalLink,*/
  /*.adminButton {*/
  /*width: 15px;*/
  /*min-height: 1px;*/
  /*}*/

  .actions {
    float: right;
    top: 2px;
  }

  .flags {
    line-height: 38px;

    img {
      padding: 0 4px;
      height: 13px;
      width: 26px;
    }
  }

  .date {
    font-size: 0.8rem;
  }

  div.checkboxContainer {
    height: 40px;
    top: 10px;
    position: relative;
  }
}
</style>
