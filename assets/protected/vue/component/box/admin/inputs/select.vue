<template>
  <div class="row" :class="{ disabled: !internalEnabled }">
    <div class="label col-sm-3">
      <toggle-button :value="internalEnabled" :sync="true" :labels="false" @change="toggle" color="#75c791" />
      {{ $t(label) }}
    </div>

    <div class="list col-sm-4">
      <select class="form-control">
        <option value="AK">Alaska</option>
        <option value="HI">Hawaii</option>
      </select>
    </div>

    <div class="description col-sm-5" v-html="$t(description)"></div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue';
import Component from 'vue-class-component';
import { Prop } from 'vue-property-decorator';

import ToggleButton from 'vue-js-toggle-button';
Vue.use(ToggleButton);

import de from 'Translations/across/across.de_DE.json';
import en from 'Translations/across/across.en_GB.json';
import fr from 'Translations/across/across.fr_FR.json';
import es from 'Translations/across/across.es_ES.json';

import VueI18n from 'vue-i18n';
Vue.use(VueI18n);

const i18n = new VueI18n({
  locale: <string>document.getElementsByTagName('html')[0].getAttribute('lang'),
  messages: { de, en, fr, es },
});

@Component({
  name: 'SelectComponent',
  i18n,
})
export default class Select extends Vue {
  @Prop({ default: false }) enabled: boolean;
  @Prop({}) label: string;
  @Prop({}) description: string;
  @Prop({}) value: Array<string>;

  internalEnabled: boolean = false;
  internalValue: Array<string>;

  toggle(data: any) {
    this.internalEnabled = data.value;
  }

  created() {
    this.internalEnabled = this.enabled;
    this.internalValue = this.value;
  }
}
</script>

<style lang="scss" scoped></style>
