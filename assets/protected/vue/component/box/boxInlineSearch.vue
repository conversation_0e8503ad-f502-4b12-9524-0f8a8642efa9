<template>
  <div>
    <div id="inlineSearch">
      <div class="row">
        <div class="col-6">
          <typeahead-input
            :src="'/box/api/search?folder=' + folderPath + '&language=' + languageTld"
            query-param-name="search"
            :placeholder="$t('box/search/placeholder')"
            :responseAttr="['cleanName', 'extension', 'flags', 'cleanPath']"
            :disableReset="true"
            :limit="50"
            :limitText="$t('box/search/limited')"
            :nothingFoundText="$t('box/search/nothing_found')"
            @hit="onSelectFile"
            cssStyle="box"></typeahead-input>
        </div>

        <div class="col-3">
          <div class="dropdown">
            <button
              id="dropdownLanguage"
              class="btn btn-sm btn-block btn-light dropdown-toggle"
              data-toggle="dropdown"
              type="button"
              aria-haspopup="true"
              aria-expanded="false"
              v-html="languageFlag + language"></button>
            <div class="dropdown-menu" aria-labelledby="dropdownLanguage">
              <a
                class="dropdown-item"
                href="#"
                style="font-weight: bold"
                @click="selectLanguage({ name: $t('box/search/all_languages'), tld: null })">
                {{ $t('box/search/all_languages') }}
              </a>
              <a v-for="language in languages" class="dropdown-item" href="#" @click="selectLanguage(language)">
                <img
                  class="flag"
                  :src="
                    require('Flags/png100px/' + language.tld.replace('en', 'gb').replace('eu', 'aq') + '.png')
                  " />&nbsp;&nbsp;{{ language.name }}
              </a>
            </div>
          </div>
        </div>

        <div class="col-3">
          <div class="dropdown">
            <button
              id="dropdownFolder"
              class="btn btn-sm btn-block btn-light dropdown-toggle"
              data-toggle="dropdown"
              type="button"
              aria-haspopup="true"
              aria-expanded="false"
              v-html="folder"></button>
            <div class="dropdown-menu" aria-labelledby="dropdownFolder">
              <a
                class="dropdown-item"
                href="#"
                style="font-weight: bold"
                @click="selectFolder({ name: $t('box/search/all_folders'), urlpath: '/' })">
                {{ $t('box/search/all_folders') }}
              </a>
              <a
                v-for="folder in folders"
                class="dropdown-item"
                href="#"
                @click="selectFolder(folder)"
                v-html="folder.name"></a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!--<div v-if="choosenFile !== null" id="file">-->
    <!--<div class="filename" v-html="choosenFile.cleanName + '.' + choosenFile.extension + choosenFile.flags">-->

    <!--</div>-->
    <!--<div class="buttons">-->
    <!--<button class="btn btn-success btn-sm">Datei herunterladen</button>-->
    <!--<button class="btn btn-info btn-sm">Datei azeigen</button>-->
    <!--<button class="btn btn-warning btn-sm" @click="openFolder(choosenFile.path)">Ordner öffnen</button>-->
    <!--</div>-->
    <!--</div>-->
  </div>
</template>

<script lang="ts">
import Vue from 'vue';
import Component from 'vue-class-component';
import { Prop } from 'vue-property-decorator';
import { Action } from 'vuex-class';

import TypeaheadInput from '@assets/protected/vue/component/helper/TypeaheadInput.vue';

import de from 'Translations/across/across.de_DE.json';
import en from 'Translations/across/across.en_GB.json';
import fr from 'Translations/across/across.fr_FR.json';
import es from 'Translations/across/across.es_ES.json';

// import BoxEventBus from 'EventBuses/box';

import VueI18n from 'vue-i18n';
Vue.use(VueI18n);

const i18n = new VueI18n({
  locale: <string>document.getElementsByTagName('html')[0].getAttribute('lang'),
  messages: { de, en, fr, es },
});

// en: {
//     searchPlaceholder: 'What are you looking for?',
//         allFolders: 'All folders',
//         allLanguages: 'All languages',
//         limited: 'Due to the high number of results, the total number of results was restricted to 50.',
//         nothingFound: 'No search results found.',
// },

@Component({
  name: 'BoxInlineSearchComponent',
  i18n,
  components: {
    TypeaheadInput,
  },
})
export default class BoxInlineSearch extends Vue {
  @Prop({ default: null }) startFolder: string;
  @Prop({
    default: function () {
      return [];
    },
  })
  folders: Array<string>;
  @Prop({
    default: function () {
      return [];
    },
  })
  languages: Array<string>;

  @Action('box/openFolder') openFolder: any;

  query: string = '';
  folder: string = '';
  folderPath: string = '';
  language: string = '';
  languageTld: string = '';
  languageFlag: string = '';

  // choosenFile: any = null;

  /**
   * Callback Funktion die aufgerufen wird, wenn auf ein Suchergebnis geklickt wird
   *
   * @param file
   */
  private onSelectFile(file: any): void {
    this.openFolder(file.path);
    // BoxEventBus.$emit('open', file.path);
  }

  setQuery(query: string): void {
    this.query = query;
  }

  public selectLanguage(language: any): void {
    this.language = language.name;
    this.languageTld = language.tld;
    this.languageFlag = language.tld !== null ? '<img src="/dist/images/flags/' + language.tld + '.png">' : '';
  }

  public selectFolder(folder: any): void {
    this.folder = folder.name;
    this.folderPath = folder.urlpath;
  }

  created(): void {
    this.folderPath = this.startFolder !== null ? this.startFolder : '/';

    this.folder = <string>i18n.t('box/search/all_folders');
    this.language = <string>i18n.t('box/search/all_languages');
  }
}
</script>

<style lang="scss" scoped>
@import '@assets/public/vendor/dynamicTheme/css/bootstrapCustomScss/custom-variables.scss';

#inlineSearch {
  padding: 15px 10px;
  background-color: $abus-grey-7;
  color: $white;
  border-top-left-radius: 0.3em;
  border-top-right-radius: 0.3em;

  .dropdown {
    button {
      height: 37px;
    }
    img {
      margin-top: -5px;
    }
  }
}

#file {
  padding: 15px 10px;
  border-top: #dfdfdf 1px solid;
  background-color: $abus-blue-light;
  color: $white;
  display: flow-root;

  div.filename {
    float: left;
    margin-right: 20px;
    line-height: 30px;
  }

  div.buttons {
    float: right;
  }

  button {
    margin-left: 15px;
  }
}
</style>

<style lang="scss">
div#inlineSearch .dropdown button img {
  margin-top: -3px;
  margin-right: 10px;
}

div#inlineSearch .flag {
  width: 20px;
  height: 13px;
}
</style>
