<template>
  <div>
    <div class="row">
      <div class="col-md-8">
        <div class="row">
          <div class="col-md-2">
            <div class="input-group">
              <div class="input-group-prepend">
                <div class="input-group-text" v-html="sectionprefix"></div>
              </div>
              <input v-model="number" class="form-control" type="text" disabled />
            </div>
          </div>
          <div class="col-md-10">
            <input
              v-model="$title"
              class="form-control"
              type="text"
              :placeholder="$t('apis/admin/title')"
              @blur="update()"
              @keyup="synchronize()" />
          </div>
        </div>
        <div class="row">
          <div class="col-md-12 mt10">
            <Editor :id="props.id" v-model="internalContent" preset="full" @blur="update" @input="synchronize" />
          </div>
        </div>
      </div>
      <div class="col-md-4">
        <div class="row">
          <div class="col-md-12 attachmentContainer">
            <Card card-class="header-bg-abus-blue-light header-color-white body-bg-abus-grey-1 body-color-black">
              <template v-slot:title>
                <FontAwesomeIcon icon="file-alt" style="margin-right: 10px" />
                {{ $t('apis/admin/attachmentsAndImages') }}
              </template>
              <template v-slot:body>
                <!-- TODO: Replace with Vueuse dropzone: https://vueuse.org/core/useDropZone/ -->
                <dropzone :uploader="uploader" :multiple="true">
                  <div class="headline">
                    <FontAwesomeIcon class="fa-inverse" :icon="['fab', 'dropbox']" />
                    {{ $t('apis/admin/dropzone') }}
                  </div>
                  <div class="extensions">
                    (.pdf, .jpg, .jpeg, .png, .gif, .txt, .doc, .docx, .xsl, .xslx, .ppt, .pptx)
                  </div>
                  <div class="size" v-html="$t('apis/admin/maxSize') + ' 5 MB'"></div>
                </dropzone>

                <div v-for="attachment in internalAttachments" class="rowContainer">
                  <div class="row">
                    <div class="col-md-9" v-html="attachment.filename"></div>
                    <div
                      class="col-md-3"
                      style="float: right; text-align: right"
                      v-html="(attachment.filesize / 1048576).toFixed(2) + ' MB'"></div>
                  </div>

                  <div class="row">
                    <div class="col-md-5">
                      <a
                        v-if="attachment.mimetype.indexOf('image/') === -1"
                        :href="'/apis/' + section + '/' + number + '/imageobjects/' + attachment.name"
                        target="blank">
                        <img
                          class="icon"
                          :src="require('Assets/public/images/fileIcons/32px/' + attachment.extension + '.png')" />
                      </a>
                      <a
                        v-if="attachment.mimetype.indexOf('image/') !== -1"
                        data-lightbox="images"
                        :href="'/apis/' + section + '/' + number + '/imageobjects/' + attachment.name">
                        <img :src="'/apis/' + section + '/' + number + '/imageobjects/' + attachment.name" />
                      </a>
                    </div>

                    <div class="col-md-7 buttons">
                      <button
                        v-if="attachment.type === 'image'"
                        class="btn btn-xs btn-primary"
                        @click="flipImageAttachment(attachment)"
                        :title="$t('apis/admin/makeAttachment')"
                        v-html="$t('apis/admin/image')"></button>
                      <button
                        v-if="attachment.type === 'attachment' && isImage(attachment.mimetype)"
                        class="btn btn-xs btn-primary"
                        @click="flipImageAttachment(attachment)"
                        :title="$t('apis/admin/makeImage')"
                        v-html="$t('apis/admin/attachment')"></button>
                      <button
                        v-if="attachment.type === 'attachment' && false === isImage(attachment.mimetype)"
                        class="btn btn-xs btn-default notClickable"
                        v-html="$t('apis/admin/attachment')"></button>
                      <button
                        v-if="attachment.language.length < 4 && attachment.language.indexOf(lang) !== -1"
                        class="btn btn-xs btn-success"
                        @click="addGlobalAttachment(attachment)"
                        :title="$t('apis/admin/makeGlobal')"
                        v-html="$t('apis/admin/local')"></button>
                      <button
                        v-if="attachment.language.length === 4"
                        class="btn btn-xs btn-default notClickable"
                        :title="$t('apis/admin/isGlobal')"
                        v-html="$t('apis/admin/global')"></button>
                      <button
                        class="btn btn-xs btn-danger"
                        @click="removeAttachment(attachment)"
                        :title="$t('apis/admin/removeAttachment')">
                        <font-awesome-icon icon="trash"></font-awesome-icon>
                        {{ $t('apis/admin/delete') }}
                      </button>
                    </div>
                  </div>
                </div>
              </template>
            </Card>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import axios from 'axios';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';

import APISEventBus from '@assets/protected/vue/eventBuses/apis';
import ModalEventBus from '@assets/protected/vue/eventBuses/modal';

import Editor from '@assets/protected/vue/component/helper/Editor.vue';
import Card from '@assets/protected/vue/component/helper/Card.vue';

// TODO: Replace lightbox
const lightbox2 = require('lightbox2/dist/js/lightbox');
const lightbox2Css = require('lightbox2/dist/css/lightbox.css');

import Dropzone from 'vue-fineuploader/dropzone.vue';
import FineUploaderTraditional from 'fine-uploader-wrappers';

// Define props
interface Props {
  id: string;
  number: number;
  section: string;
  sectionprefix: string;
  lang: string;
  title: string;
  content: string;
  attachments: any;
}

const props = defineProps<Props>();

const $title = ref<string>('');
const internalContent = ref<string>('');
const internalAttachments = ref<Record<string, any>>({});
const language = ref<string>('');

/**
 * Speichert Änderungen an dem Block
 */
const update = (): void => {
  APISEventBus.$emit('save');
};

/**
 * Löst ein Event aus um den CreateBlock mit dem Parent zu synchronisieren
 */
const synchronize = (): void => {
  const params: Record<string, string> = {};
  params[props.lang + '_title'] = $title.value;
  params[props.lang + '_content'] = internalContent.value;

  APISEventBus.$emit('synchronizeContentBlock', params);
};

/**
 * Löst ein Event aus, das ein Attachment in allen "apisCreateBlock" hinzufügt
 *
 * @param attachment
 */
const addGlobalAttachment = (attachment: any): void => {
  const url = `/apisadmin/global/attachment/${props.section}/${props.number}/${attachment.name}`;

  axios
    .patch(url)
    .then((response: any) => {
      APISEventBus.$emit('addGlobalAttachment', response.data);
    })
    .catch((error: any) => {
      ModalEventBus.$emit('showModal', { type: 'error', message: error.response.data });
    });
};

/**
 * Löscht einen Anhang vom Server
 *
 * @param attachment
 */
const removeAttachment = (attachment: any): void => {
  const url = `/apisadmin/delete/attachment/${props.section}/${props.number}/${attachment.name}`;

  axios
    .delete(url)
    .then((response: any) => {
      delete internalAttachments.value[response.data.file];

      if (attachment.language.length === 4) {
        APISEventBus.$emit('removeGlobalAttachment', response.data.file);
      }
    })
    .catch((error: any) => {
      ModalEventBus.$emit('showModal', { type: 'error', message: error.response.data });
    });
};

/**
 * Fügt einen hochgeladenen Anhang dem AttachmentsArray hinzu
 *
 * @param response
 */
const addAttachment = (response: any): void => {
  response.file.type = isImage(response.file.mimetype) ? 'image' : 'attachment';
  internalAttachments.value[response.file.name] = response.file;
};

const flipImageAttachment = (attachment: any): void => {
  const url = `/apisadmin/flip/attachment/${props.section}/${props.number}/${attachment.name}`;

  axios
    .post(url)
    .then((response: any) => {
      attachment.type = response.data.type;
      internalAttachments.value[attachment.name] = attachment;
    })
    .catch((error: any) => {
      ModalEventBus.$emit('showModal', { type: 'error', message: error.response.data });
    });
};

const isImage = (mimetype: string): boolean => {
  return mimetype.indexOf('image/') === 0;
};

const uploader = new FineUploaderTraditional({
  options: {
    request: {
      endpoint: `/apisadmin/upload/attachment/${props.lang}/${props.section}/${props.number}`,
    },
    callbacks: {
      onComplete: (id: any, name: any, response: any) => {
        if (response.success === true) {
          addAttachment(response);
        } else {
          ModalEventBus.$emit('showModal', { type: 'error', message: response.message });
        }
      },
    },
  },
});

// Event handlers
const handleAddGlobalAttachment = (attachment: any) => {
  internalAttachments.value[attachment.name] = attachment;
};

const handleRemoveGlobalAttachment = (file: string) => {
  delete internalAttachments.value[file];
};

onMounted(() => {
  // Per Prop() übergebene Werte in interne Variablen speichern, sonst meckert Vue (Warning)
  $title.value = props.title;
  internalContent.value = props.content;
  internalAttachments.value = props.attachments !== undefined ? props.attachments : {};

  language.value = document.getElementsByTagName('html')[0].getAttribute('lang') || '';

  APISEventBus.$on('addGlobalAttachment', handleAddGlobalAttachment);
  APISEventBus.$on('removeGlobalAttachment', handleRemoveGlobalAttachment);
});

onUnmounted(() => {
  APISEventBus.$off('addGlobalAttachment', handleAddGlobalAttachment);
  APISEventBus.$off('removeGlobalAttachment', handleRemoveGlobalAttachment);
});
</script>

<style lang="scss">
.attachmentContainer {
  .panel-body {
    background-color: #d2e4e8;
    padding-bottom: 0;
  }
}
</style>

<style lang="scss" scoped>
@import '@assets/public/vendor/dynamicTheme/css/bootstrapCustomScss/custom-variables.scss';

.fine-uploader-dropzone-container {
  background-color: $abus-yellow-dark;
  width: 100%;
  height: 85px;
  font-size: 18px;
  color: #ffffff;

  text-align: center;
  border-radius: calc(0.25rem - 1px) calc(0.25rem - 1px) 0 0;
  border: 1px solid darken($abus-yellow-dark, 5%);
  margin-bottom: 20px;

  -webkit-box-shadow: 5px 5px 15px 0px rgba(0, 0, 0, 0.75);
  -moz-box-shadow: 5px 5px 15px 0px rgba(0, 0, 0, 0.75);
  box-shadow: 5px 5px 15px 0px rgba(0, 0, 0, 0.75);

  .headline {
    margin-top: 5px;
    line-height: 30px;
  }

  .extensions,
  .size {
    font-size: 13px;
    line-height: 23px;
  }

  &.vue-fine-uploader-dropzone-active {
    cursor: pointer;
    background-color: #0a98bb;
  }
}

.attachmentContainer,
.embeddedImagesContainer {
  .card-body {
    .rowContainer {
      &:nth-child(2) {
        margin-top: -10px;
      }

      &:nth-child(odd) {
        background-color: #bddfe6;
      }

      .row:last-child {
        margin-bottom: 10px;
      }

      .row {
        margin-top: 5px;
        margin-left: 0;
        margin-right: 0;
        line-height: 30px;

        img {
          max-height: 32px;
          max-width: 100%;
          margin-bottom: 10px;

          &:hover {
            cursor: pointer;
          }
        }

        div.buttons {
          float: right;
          text-align: right;

          button {
            margin-left: 5px;

            &.notClickable {
              cursor: default;

              &:hover {
                background-color: #e8e8e8;
              }
            }
          }
        }
      }
    }
  }
}
</style>
