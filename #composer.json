{"name": "abus/portal", "type": "project", "license": "proprietary", "description": "ABUS Kransysteme GmbH Portal", "require": {"php": "^7.4.1", "ext-json": "*", "ext-pdo": "*", "ext-simplexml": "*", "doctrine/annotations": "^1.0", "doctrine/doctrine-bundle": "^1.12", "doctrine/doctrine-migrations-bundle": "^3.2", "doctrine/orm": "^2.7", "firebase/php-jwt": "^5.2", "graylog2/gelf-php": "^1.5", "guzzlehttp/guzzle": "^6.3", "knplabs/knp-snappy": "^1.0", "league/html-to-markdown": "^4.6", "mobiledetect/mobiledetectlib": "^2.8", "phpdocumentor/reflection-docblock": "^5.3", "sabre/dav": "^3.2", "sensio/framework-extra-bundle": "^5.1", "sensiolabs/security-checker": "^4.1", "smalot/pdfparser": "^0.11.0", "symfony/asset": "^4.0", "symfony/browser-kit": "^4.0", "symfony/config": "^4.0", "symfony/console": "^4.0", "symfony/expression-language": "^4.0", "symfony/form": "^4.0", "symfony/framework-bundle": "^4.0", "symfony/http-client": "4.4.*", "symfony/intl": "^4.0", "symfony/ldap": "^4.0", "symfony/monolog-bundle": "^3.1", "symfony/property-access": "4.4.*", "symfony/property-info": "4.4.*", "symfony/routing": "^4.0", "symfony/security-bundle": "^4.0", "symfony/serializer": "4.4.*", "symfony/stopwatch": "^4.0", "symfony/swiftmailer-bundle": "^3.1", "symfony/translation": "^4.0", "symfony/twig-bundle": "^4.0", "symfony/web-profiler-bundle": "^4.0", "symfony/webpack-encore-bundle": "^1.7", "symfony/workflow": "^4.0", "symfony/yaml": "^4.0", "twig/extensions": "^1.5"}, "require-dev": {"doctrine/instantiator": "^1.0", "myclabs/deep-copy": "^1.7", "sebastian/code-unit-reverse-lookup": "^1.0", "sebastian/comparator": "^2.1", "sebastian/diff": "^2.0", "sebastian/environment": "^3.1", "sebastian/exporter": "^3.0", "sebastian/global-state": "^2.0", "sebastian/object-enumerator": "^3.0", "sebastian/recursion-context": "^3.0", "sebastian/resource-operations": "^1.0", "sebastian/version": "^2.0", "symfony/dotenv": "^4.0", "symfony/flex": "^1.0", "symfony/maker-bundle": "^1.5", "symfony/phpunit-bridge": "4.1"}, "config": {"preferred-install": {"*": "dist"}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd", "security-checker security:check": "script"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"id": "01C0V90CFGXEFWTE8BYGSPSY3X", "allow-contrib": false, "require": "4.4.*", "docker": false}}}