parameters:
    storage:
        baseDirectory: /var/www/data/storage
        excludeFromIndexingRegex:
            - /^\./
            - /^(.*\.(yml)$)[^.]*$/i
            - /^(.*\.(php)$)[^.]*$/i
        whitelistedWordsForSearch:
            - zlk
            - elk
            - edl
            - ekl
            - esb
            - zsb
            - ehb
            - zhb
            - ls
            - lsx
            - vs
            - lw
            - lwx
            - vw
            - dqa
            - lpk
            - gmc
            - gm2
            - gm4
            - gm6
            - gm8

        sortPattern: /#[a-z\d]{1,4}#/
        versionPattern: /#ver_[a-zA-ZäöüÄÖÜß .:\d]{0,20}#/

#        permission:
#            public: false
#            clear_groups: false
#            allowed_groups: false
#            clear_users: false
#            allowed_users: false
#
#        language:
#            languages: false
#            default: false
#            only: false
#
#        search:
#            searchBox: true             # Suchfeld anzeigen
#            searchable: true            # Kann der Ordner durchsucht werden?
#            searchFileExtension: true   # Soll bei den Suchergebnissen die Dateiendung angezeigt werden?
#            tags: false
#
#        show:
#            hideFromList: false         # Soll der Ordner/Datei in der Auflistung auftauchen
#            listContent: true           # Soll der Inhalt des Ordner überhaupt aufgelistet werden können
#            folderHeader: true          # Kopfbereich mit Ordnernamen und Download Mode anzeigen
#            downloadMode: true          # Download Mode aktivieren
#            downloadable: true          # Kann heruntergeladen werden
#            copyLink: false             # Button zum Kopieren des Links anzeigen
#            fileExtension: true         # Dateierweiterung bei Datei anzeigen (.pdf)
#            fileDate: false             # Letztes Modifikationsdatum der Datei anzeigen
#            filenameVersion: true       # Aus dem Dateinamen generierte Version anzeigen
#            metaVersion: true           # Aus den Metadaten generierte Version anzeigen
#            MP4Video: false             # Als MP4 Video einbetten
#            from: false                 # Erst ab einem bestimmten Datum anzeigen (dd.mm.YYYY 15.01.2001)
#            until: false                # Bis zu einem bestimmten Datum anzeigen (dd.mm.YYYY 15.01.2001)
#            isArchived: false           # Ist der Inhalt archiviert, im Sinne von nicht mehr die aktuellste Version
#
#        meta:
#            headlineHeader: false
#            descriptionHeaderDE: false
#            descriptionHeaderEN: false
#            descriptionHeaderFR: false
#            descriptionHeaderES: false
#            folderNameDE: false
#            folderNameEN: false
#            folderNameFR: false
#            folderNameES: false
#            version: false
#
    storageDescription:
        permission:
            public: "Ordner öffentlich (ohne Login) zugänglich machen. Diese Einstellung wird überschrieben, wenn <i>allowed_groups</i> oder <i>allowed_users</i> angegeben werden.<br><br>Gültige Werte: true, false"
            clear_groups: "Löscht alle Berechtigungsgruppen in diesem Ordner, die durch die Vererbung aus den Oberordner kommen<br><br>Gültige Werte: true, false"
            allowed_groups: "Ordner für ActiveDirectory Gruppen zugänglich machen.<br><br>Gültige Werte: false, [WEB_GRUPPE_1, WEB_GRUPP_2, etc.]"
            allowed_users: "Ordner für Benutzer zugänglich machen.<br><br>Über eine Angabe in Form von <i>'*@abus-kransysteme.de'</i> kann man alle User mit dieser E-Mail Endung freigeben. Hochkommata bei der Eingabe nicht vergessen!<br><br><b>Der Benutzer muss ein ABUS Portal Zugang haben.</b><br><br>Gültige Werte: false, ['*@abus-kransysteme.de', <EMAIL>, <EMAIL>, etc.]"

        language:
            languages: "Welche Sprachen enthält der Ordner?<br><br>Gültige Werte: false, ['DE', 'EN', 'BR', etc]"
            default: "Für einen Ordner eine Sprache festlegen. Ein öffentlicher Ordner wird dann in der Sprache dargestellt.<br><br>Gültige Werte ausschließlich: 'DE', 'EN', 'FR', 'ES'"
            only: "Den Ordner nur in bestimmten Portal Sprachen anzeigen.<br><br>Gültige Werte: false, ['DE', 'EN', etc]"
            folderNameDE: "Ordner oder Datei Name in der angegebenen Sprache ändern.<br><br>Gültige Werte: false, 'Ordnername'"
            folderNameEN: "Siehe folderNameDE"
            folderNameFR: "Siehe folderNameDE"
            folderNameES: "Siehe folderNameDE"

        search:
            searchBox: "Suchfeld anzeigen, wenn der Ordner per URL aufgerufen wird?<br><br>Gültige Werte: true, false"
            searchable: "Können Dateien oder Dateien im Ordner über die Suche gefunden werden?<br><br>Gültige Werte: true, false"
            searchFileExtension: "Die Suchergebnisse mit Dateiendung (z.B. .pdf oder .doc) anzeigen?<br><br>Gültige Werte: true, false"
            tags: "Zusätzliche Suchbegriffe.<br><br>Gültige Werte: false, ['TAG1', 'TAG2', 'TAG3', etc]"

        show:
            hideFromList: "Ordner oder Datei in der Ordnerstruktur des Benutzers ausblenden?<br><br>Ist dieser Wert <i>true</i> wird der Ordner oder die Datei nicht mehr in der Ordnerstruktur des Benutzers angezeigt.<br><br><b>Ein direkter Zugriff, auf Ordner und Dateien im Ordner, über die entsprechenden URLs ist weiterhin möglich!</b><br><br>Gültige Werte: true, false"
            listContent: "Ordner und Inhalt des Ordners beim direkten Aufruf über die URL anzeigen?<br><br>Ist dieser Wert <i>false</i> kann man diesen Ordner nicht mehr direkt über die URL aufrufen, sondern nur noch einzelne Dateien, über die entsprechenden URLs, in diesem Ordner.><br><br><b>Ein direkter Zugriff auf Dateien im Ordner, über die entsprechenden URLs, ist weiterhin möglich!</b><br><br>Gültige Werte: true, false"
            folderHeader: "Kopfbereich mit Überschrift und Schaltfläche für Download Modus anzeigen, wenn der Ordner per URL aufgerufen wird?<br><br>Gültige Werte: true, false"
            downloadMode: "Download Modus anzeigen, wenn der Ordner per URL aufgerufen wird?<br><br>Gültige Werte: true, false"
            downloadable: "Kann die Datei über den Downloadmodus heruntergeladen werden?<br><br>Gültige Werte: true, false"
            copyLink: "Direktlink anzeigen für Datei oder der Dateien im Ordner <b>für jeden</b> erlauben.<br><br>Gültige Werte: true, false"
            fileExtension: "Dateiwerweiterungen (z.B. .pdf oder .doc) der Datei oder der Dateien im Ordner anzeigen?<br><br>Gültige Werte: true, false"
            fileDate: "Letztes Modifikationsdatum der Datei oder der Dateien im Ordner anzeigen?<br><br>Gültige Werte: true, false"
            filenameVersion: "Eine gefundene Version im Dateinamen der Datei oder des Ordners anzeigen?<br><br>Gültige Werte: true, false"
            metaVersion: "Eine über <i>meta:version</i> angegebene Version anzeigen?<br><br>Gültige Werte: true, false"
            MP4Video: "Eine .mp4 Videodatei direkt in ABUS Box abspielen<br><br>Gültige Werte: true, false"
            from: "Ab wann soll ein Ordner oder eine Datei angezeigt werden?<br><br>Gültige Werte: false, '10.01.2015'"
            until: "Bis wann soll ein Ordner oder eine Datei angezeigt werden?<br><br>Gültige Werte: false, '24.01.2015'"
            isArchived: "Ist der Inhalt archiviert, im Sinne von nicht mehr die aktuellste Version?<br><br>Gültige Werte: true, false"

        meta:
#            headlineHeader: "Überschrift des Ordners ändern. Diese wird angezeigt, wenn der Ordner per URL aufgerufen wird. Standardmässig wird der Ordnername im Dateisystem verwendet.<br><br>Gültige Werte: false, 'Neue Überschrift'"
#            descriptionHeaderDE: "Ordnerbeschreibung angeben. Diese wird angezeigt, wenn der Ordner per URL aufgerufen wird.<br><br>Gültige Werte: false, 'Dies ist eine Ordnerbeschreibung'"
#            descriptionHeaderEN: "Siehe descriptionHeaderDE"
#            descriptionHeaderFR: "Siehe descriptionHeaderDE"
#            descriptionHeaderES: "Siehe descriptionHeaderDE"
            version: "Ordner oder Dateiversion angeben.<br><br>Gültige Werte: false, 'Version: 1.0', '2.1', '20.03.2015'"
