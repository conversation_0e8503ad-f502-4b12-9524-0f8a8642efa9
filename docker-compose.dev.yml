version: '2'

services:
  nextcloud_db:
    extends:
      file: common.yml
      service: nextcloud_db
    restart: always
    ports:
      - "62004:3306"

  nextcloud_fpm:
    extends:
      file: common.yml
      service: nextcloud_fpm
    restart: always
    links:
      - nextcloud_db
    depends_on:
      - nextcloud_db
    build:
      args:
        development: 1
        environment: ${ENVIRONMENT}
    extra_hosts:
      - "app-2-dmz.abus-vpn.de:************"

  nextcloud_nginx:
    extends:
      file: common.yml
      service: nextcloud_nginx
    restart: always
    build:
      args:
        environment: ${ENVIRONMENT}
    links:
      - nextcloud_fpm
    depends_on:
      - nextcloud_fpm
    volumes_from:
      - nextcloud_fpm
    labels:
      - "traefik.http.routers.${NAMEPREFIX}-secure.rule=${HOSTS}"

  nextcloud_redis:
    extends:
      file: common.yml
      service: nextcloud_redis
    restart: always


#  nextcloud_webdav:
#    extends:
#      file: common.yml
#      service: nextcloud_webdav
#    restart: always
#    build:
#      args:
#        environment: ${ENVIRONMENT}
#    extra_hosts:
#      - "app-2-dmz.abus-vpn.de:************"

networks:
  nextcloud-net:
  portal-net:
    external: true
  traefik-net:
    external: true
  database-net:
    external: true
  elasticsearch-net:
    external: true
