# Set default behaviour, in case users don't have core.autocrlf set.
* text eol=lf

# Explicitly declare text files we want to always be normalized and converted
# to native line endings on checkout.

# Declare files that will always have CRLF line endings on checkout.

# Denote all files that are truly binary and should not be modified.
*.png binary
*.jpg binary
*.gif binary
*.zip binary
*.jar binary
*.doc binary
*.docx binary
*.dot binary
*.xls binary
*.otf binary
*.eot binary
*.ttf binary
*.ttf binary
*.woff binary
