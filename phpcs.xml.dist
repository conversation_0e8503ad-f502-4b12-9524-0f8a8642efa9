<?xml version="1.0" encoding="UTF-8"?>

<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="PHP_CodeSniffer" xsi:noNamespaceSchemaLocation="phpcs.xsd">
    <description>ABUS Kransysteme GmbH coding standard</description>

    <arg name="basepath" value="."/>
    <arg name="cache" value=".phpcs-cache"/>
    <arg name="colors"/>
    <arg name="extensions" value="php"/>

    <file>src/</file>
    <file>tests/</file>

    <rule ref="PSR12">
        <exclude name="PEAR.Functions.FunctionCallSignature"/>
        <exclude name="PEAR.Functions.FunctionCallSignature.SpaceAfterCloseBracket"/>
        <exclude name="Generic.Files.LineLength"/>
    </rule>

</ruleset>
