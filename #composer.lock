{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "c47e535726f0d6b593a509bfbeaaa7ca", "packages": [{"name": "composer/ca-bundle", "version": "1.3.3", "source": {"type": "git", "url": "https://github.com/composer/ca-bundle.git", "reference": "30897edbfb15e784fe55587b4f73ceefd3c4d98c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/ca-bundle/zipball/30897edbfb15e784fe55587b4f73ceefd3c4d98c", "reference": "30897edbfb15e784fe55587b4f73ceefd3c4d98c", "shasum": ""}, "require": {"ext-openssl": "*", "ext-pcre": "*", "php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.55", "psr/log": "^1.0", "symfony/phpunit-bridge": "^4.2 || ^5", "symfony/process": "^2.5 || ^3.0 || ^4.0 || ^5.0 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\CaBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Lets you find a path to the system CA bundle, and includes a fallback to the Mozilla CA bundle.", "keywords": ["cabundle", "cacert", "certificate", "ssl", "tls"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/ca-bundle/issues", "source": "https://github.com/composer/ca-bundle/tree/1.3.3"}, "time": "2022-07-20T07:14:26+00:00"}, {"name": "composer/package-versions-deprecated", "version": "*********", "source": {"type": "git", "url": "https://github.com/composer/package-versions-deprecated.git", "reference": "b4f54f74ef3453349c24a845d22392cd31e65f1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/package-versions-deprecated/zipball/b4f54f74ef3453349c24a845d22392cd31e65f1d", "reference": "b4f54f74ef3453349c24a845d22392cd31e65f1d", "shasum": ""}, "require": {"composer-plugin-api": "^1.1.0 || ^2.0", "php": "^7 || ^8"}, "replace": {"ocramius/package-versions": "1.11.99"}, "require-dev": {"composer/composer": "^1.9.3 || ^2.0@dev", "ext-zip": "^1.13", "phpunit/phpunit": "^6.5 || ^7"}, "type": "composer-plugin", "extra": {"class": "PackageVersions\\Installer", "branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"PackageVersions\\": "src/PackageVersions"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be"}], "description": "Composer plugin that provides efficient querying for installed package versions (no runtime IO)", "support": {"issues": "https://github.com/composer/package-versions-deprecated/issues", "source": "https://github.com/composer/package-versions-deprecated/tree/*********"}, "time": "2022-01-17T14:14:24+00:00"}, {"name": "doctrine/annotations", "version": "1.13.3", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "648b0343343565c4a056bfc8392201385e8d89f0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/648b0343343565c4a056bfc8392201385e8d89f0", "reference": "648b0343343565c4a056bfc8392201385e8d89f0", "shasum": ""}, "require": {"doctrine/lexer": "1.*", "ext-tokenizer": "*", "php": "^7.1 || ^8.0", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^1.11 || ^2.0", "doctrine/coding-standard": "^6.0 || ^8.1", "phpstan/phpstan": "^1.4.10 || ^1.8.0", "phpunit/phpunit": "^7.5 || ^8.0 || ^9.1.5", "symfony/cache": "^4.4 || ^5.2", "vimeo/psalm": "^4.10"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.13.3"}, "time": "2022-07-02T10:48:51+00:00"}, {"name": "doctrine/cache", "version": "1.13.0", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "56cd022adb5514472cb144c087393c1821911d09"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/56cd022adb5514472cb144c087393c1821911d09", "reference": "56cd022adb5514472cb144c087393c1821911d09", "shasum": ""}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "cache/integration-tests": "dev-master", "doctrine/coding-standard": "^9", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "predis/predis": "~1.0", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/cache": "^4.4 || ^5.4 || ^6", "symfony/var-exporter": "^4.4 || ^5.4 || ^6"}, "suggest": {"alcaeus/mongo-php-adapter": "Required to use legacy MongoDB driver"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/1.13.0"}, "time": "2022-05-20T20:06:54+00:00"}, {"name": "doctrine/collections", "version": "1.7.0", "source": {"type": "git", "url": "https://github.com/doctrine/collections.git", "reference": "07d15c8a766e664ec271ae84e5dfc597aeeb03b1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/collections/zipball/07d15c8a766e664ec271ae84e5dfc597aeeb03b1", "reference": "07d15c8a766e664ec271ae84e5dfc597aeeb03b1", "shasum": ""}, "require": {"doctrine/deprecations": "^0.5.3 || ^1", "php": "^7.1.3 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0", "phpstan/phpstan": "^1.4.8", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.1.5", "vimeo/psalm": "^4.22"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Collections\\": "lib/Doctrine/Common/Collections"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Collections library that adds additional functionality on top of PHP arrays.", "homepage": "https://www.doctrine-project.org/projects/collections.html", "keywords": ["array", "collections", "iterators", "php"], "support": {"issues": "https://github.com/doctrine/collections/issues", "source": "https://github.com/doctrine/collections/tree/1.7.0"}, "time": "2022-08-18T05:44:45+00:00"}, {"name": "doctrine/common", "version": "2.13.3", "source": {"type": "git", "url": "https://github.com/doctrine/common.git", "reference": "f3812c026e557892c34ef37f6ab808a6b567da7f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/common/zipball/f3812c026e557892c34ef37f6ab808a6b567da7f", "reference": "f3812c026e557892c34ef37f6ab808a6b567da7f", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "doctrine/inflector": "^1.0", "doctrine/lexer": "^1.0", "doctrine/persistence": "^1.3.3", "doctrine/reflection": "^1.0", "php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^1.0", "phpstan/phpstan": "^0.11", "phpstan/phpstan-phpunit": "^0.11", "phpunit/phpunit": "^7.0", "squizlabs/php_codesniffer": "^3.0", "symfony/phpunit-bridge": "^4.0.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.11.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Doctrine Common project is a library that provides additional functionality that other Doctrine projects depend on such as better reflection support, persistence interfaces, proxies, event system and much more.", "homepage": "https://www.doctrine-project.org/projects/common.html", "keywords": ["common", "doctrine", "php"], "support": {"issues": "https://github.com/doctrine/common/issues", "source": "https://github.com/doctrine/common/tree/2.13.x"}, "time": "2020-06-05T16:46:05+00:00"}, {"name": "doctrine/dbal", "version": "2.13.9", "source": {"type": "git", "url": "https://github.com/doctrine/dbal.git", "reference": "c480849ca3ad6706a39c970cdfe6888fa8a058b8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/dbal/zipball/c480849ca3ad6706a39c970cdfe6888fa8a058b8", "reference": "c480849ca3ad6706a39c970cdfe6888fa8a058b8", "shasum": ""}, "require": {"doctrine/cache": "^1.0|^2.0", "doctrine/deprecations": "^0.5.3|^1", "doctrine/event-manager": "^1.0", "ext-pdo": "*", "php": "^7.1 || ^8"}, "require-dev": {"doctrine/coding-standard": "9.0.0", "jetbrains/phpstorm-stubs": "2021.1", "phpstan/phpstan": "1.4.6", "phpunit/phpunit": "^7.5.20|^8.5|9.5.16", "psalm/plugin-phpunit": "0.16.1", "squizlabs/php_codesniffer": "3.6.2", "symfony/cache": "^4.4", "symfony/console": "^2.0.5|^3.0|^4.0|^5.0", "vimeo/psalm": "4.22.0"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "bin": ["bin/doctrine-dbal"], "type": "library", "autoload": {"psr-4": {"Doctrine\\DBAL\\": "lib/Doctrine/DBAL"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "homepage": "https://www.doctrine-project.org/projects/dbal.html", "keywords": ["abstraction", "database", "db2", "dbal", "ma<PERSON>b", "mssql", "mysql", "oci8", "oracle", "pdo", "pgsql", "postgresql", "queryobject", "sasql", "sql", "sqlanywhere", "sqlite", "sqlserver", "sqlsrv"], "support": {"issues": "https://github.com/doctrine/dbal/issues", "source": "https://github.com/doctrine/dbal/tree/2.13.9"}, "time": "2022-05-02T20:28:55+00:00"}, {"name": "doctrine/deprecations", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "0e2a4f1f8cdfc7a92ec3b01c9334898c806b30de"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/0e2a4f1f8cdfc7a92ec3b01c9334898c806b30de", "reference": "0e2a4f1f8cdfc7a92ec3b01c9334898c806b30de", "shasum": ""}, "require": {"php": "^7.1|^8.0"}, "require-dev": {"doctrine/coding-standard": "^9", "phpunit/phpunit": "^7.5|^8.5|^9.5", "psr/log": "^1|^2|^3"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "lib/Doctrine/Deprecations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/v1.0.0"}, "time": "2022-05-02T15:47:09+00:00"}, {"name": "doctrine/doctrine-bundle", "version": "1.12.13", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineBundle.git", "reference": "85460b85edd8f61a16ad311e7ffc5d255d3c937c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineBundle/zipball/85460b85edd8f61a16ad311e7ffc5d255d3c937c", "reference": "85460b85edd8f61a16ad311e7ffc5d255d3c937c", "shasum": ""}, "require": {"doctrine/dbal": "^2.5.12|^3.0", "doctrine/doctrine-cache-bundle": "~1.2", "doctrine/persistence": "^1.3.3", "jdorn/sql-formatter": "^1.2.16", "php": "^7.1 || ^8.0", "symfony/cache": "^3.4.30|^4.3.3", "symfony/config": "^3.4.30|^4.3.3", "symfony/console": "^3.4.30|^4.3.3", "symfony/dependency-injection": "^3.4.30|^4.3.3", "symfony/doctrine-bridge": "^3.4.30|^4.3.3", "symfony/framework-bundle": "^3.4.30|^4.3.3", "symfony/service-contracts": "^1.1.1|^2.0"}, "conflict": {"doctrine/orm": "<2.6", "twig/twig": "<1.34|>=2.0,<2.4"}, "require-dev": {"doctrine/coding-standard": "^6.0", "doctrine/orm": "^2.6", "ocramius/proxy-manager": "^2.1", "php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^7.5", "symfony/phpunit-bridge": "^4.2", "symfony/property-info": "^3.4.30|^4.3.3", "symfony/proxy-manager-bridge": "^3.4|^4|^5", "symfony/twig-bridge": "^3.4|^4.1", "symfony/validator": "^3.4.30|^4.3.3", "symfony/web-profiler-bundle": "^3.4.30|^4.3.3", "symfony/yaml": "^3.4.30|^4.3.3", "twig/twig": "^1.34|^2.12"}, "suggest": {"doctrine/orm": "The Doctrine ORM integration is optional in the bundle.", "symfony/web-profiler-bundle": "To use the data collector."}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "2.3.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Bundle\\DoctrineBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "Doctrine Project", "homepage": "http://www.doctrine-project.org/"}], "description": "Symfony DoctrineBundle", "homepage": "http://www.doctrine-project.org", "keywords": ["database", "dbal", "orm", "persistence"], "support": {"issues": "https://github.com/doctrine/DoctrineBundle/issues", "source": "https://github.com/doctrine/DoctrineBundle/tree/1.12.13"}, "time": "2020-11-14T13:38:44+00:00"}, {"name": "doctrine/doctrine-cache-bundle", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineCacheBundle.git", "reference": "6bee2f9b339847e8a984427353670bad4e7bdccb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineCacheBundle/zipball/6bee2f9b339847e8a984427353670bad4e7bdccb", "reference": "6bee2f9b339847e8a984427353670bad4e7bdccb", "shasum": ""}, "require": {"doctrine/cache": "^1.4.2", "doctrine/inflector": "^1.0", "php": "^7.1", "symfony/doctrine-bridge": "^3.4|^4.0"}, "require-dev": {"instaclick/coding-standard": "~1.1", "instaclick/object-calisthenics-sniffs": "dev-master", "instaclick/symfony2-coding-standard": "dev-remaster", "phpunit/phpunit": "^7.0", "predis/predis": "~0.8", "satooshi/php-coveralls": "^1.0", "squizlabs/php_codesniffer": "~1.5", "symfony/console": "^3.4|^4.0", "symfony/finder": "^3.4|^4.0", "symfony/framework-bundle": "^3.4|^4.0", "symfony/phpunit-bridge": "^3.4|^4.0", "symfony/security-acl": "^2.8", "symfony/validator": "^3.4|^4.0", "symfony/yaml": "^3.4|^4.0"}, "suggest": {"symfony/security-acl": "For using this bundle to cache ACLs"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Bundle\\DoctrineCacheBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "g<PERSON><PERSON><PERSON><PERSON><EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}, {"name": "Doctrine Project", "homepage": "http://www.doctrine-project.org/"}], "description": "Symfony Bundle for Doctrine Cache", "homepage": "https://www.doctrine-project.org", "keywords": ["cache", "caching"], "abandoned": true, "time": "2019-11-29T11:22:01+00:00"}, {"name": "doctrine/doctrine-migrations-bundle", "version": "3.1.1", "source": {"type": "git", "url": "https://github.com/doctrine/DoctrineMigrationsBundle.git", "reference": "91f0a5e2356029575f3038432cc188b12f9d5da5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/DoctrineMigrationsBundle/zipball/91f0a5e2356029575f3038432cc188b12f9d5da5", "reference": "91f0a5e2356029575f3038432cc188b12f9d5da5", "shasum": ""}, "require": {"doctrine/doctrine-bundle": "~1.0|~2.0", "doctrine/migrations": "^3.1", "php": "^7.2|^8.0", "symfony/framework-bundle": "~3.4|~4.0|~5.0"}, "require-dev": {"doctrine/coding-standard": "^8.0", "doctrine/orm": "^2.6", "doctrine/persistence": "^1.3||^2.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-deprecation-rules": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^7.0|^8.0|^9.0"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Doctrine\\Bundle\\MigrationsBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Doctrine Project", "homepage": "http://www.doctrine-project.org"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "description": "Symfony DoctrineMigrationsBundle", "homepage": "https://www.doctrine-project.org", "keywords": ["dbal", "migrations", "schema"], "support": {"issues": "https://github.com/doctrine/DoctrineMigrationsBundle/issues", "source": "https://github.com/doctrine/DoctrineMigrationsBundle/tree/3.1.1"}, "time": "2021-04-10T16:48:53+00:00"}, {"name": "doctrine/event-manager", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "eb2ecf80e3093e8f3c2769ac838e27d8ede8e683"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/eb2ecf80e3093e8f3c2769ac838e27d8ede8e683", "reference": "eb2ecf80e3093e8f3c2769ac838e27d8ede8e683", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^9", "phpstan/phpstan": "~1.4.10 || ^1.5.4", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.22"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/1.1.2"}, "time": "2022-07-27T22:18:11+00:00"}, {"name": "doctrine/inflector", "version": "1.4.4", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9", "reference": "4bd5c1cdfcd00e9e2d8c484f79150f67e5d355d9", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^8.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector", "Doctrine\\Common\\Inflector\\": "lib/Doctrine/Common/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/1.4.4"}, "time": "2021-04-16T17:34:40+00:00"}, {"name": "doctrine/instantiator", "version": "1.4.1", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "10dcfce151b967d20fde1b34ae6640712c3891bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/10dcfce151b967d20fde1b34ae6640712c3891bc", "reference": "10dcfce151b967d20fde1b34ae6640712c3891bc", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.16 || ^1", "phpstan/phpstan": "^1.4", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.22"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.4.1"}, "time": "2022-03-03T08:28:38+00:00"}, {"name": "doctrine/lexer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/c268e882d4dbdd85e36e4ad69e02dc284f89d229", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.11"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/1.2.3"}, "time": "2022-02-28T11:07:21+00:00"}, {"name": "doctrine/migrations", "version": "3.1.5", "source": {"type": "git", "url": "https://github.com/doctrine/migrations.git", "reference": "537a847ec5525f50f927702fa01b97bacdc2c636"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/migrations/zipball/537a847ec5525f50f927702fa01b97bacdc2c636", "reference": "537a847ec5525f50f927702fa01b97bacdc2c636", "shasum": ""}, "require": {"composer/package-versions-deprecated": "^1.8", "doctrine/dbal": "^2.11", "doctrine/event-manager": "^1.0", "friendsofphp/proxy-manager-lts": "^1.0", "php": "^7.2 || ^8.0", "psr/log": "^1.1.3", "symfony/console": "^3.4 || ^4.4.16 || ^5.0", "symfony/stopwatch": "^3.4 || ^4.0 || ^5.0"}, "require-dev": {"doctrine/coding-standard": "^8.0", "doctrine/orm": "^2.6", "doctrine/persistence": "^1.3 || ^2.0", "doctrine/sql-formatter": "^1.0", "ergebnis/composer-normalize": "^2.9", "ext-pdo_sqlite": "*", "phpstan/phpstan": "^0.12", "phpstan/phpstan-deprecation-rules": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpstan/phpstan-symfony": "^0.12", "phpunit/phpunit": "^8.5 || ^9.4", "symfony/cache": "^5.3", "symfony/process": "^3.4 || ^4.0 || ^5.0", "symfony/yaml": "^3.4 || ^4.0 || ^5.0"}, "suggest": {"doctrine/sql-formatter": "Allows to generate formatted SQL with the diff command.", "symfony/yaml": "Allows the use of yaml for migration configuration files."}, "bin": ["bin/doctrine-migrations"], "type": "library", "extra": {"composer-normalize": {"indent-size": 4, "indent-style": "space"}}, "autoload": {"psr-4": {"Doctrine\\Migrations\\": "lib/Doctrine/Migrations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Doctrine Migrations project offer additional functionality on top of the database abstraction layer (DBAL) for versioning your database schema and easily deploying changes to it. It is a very easy to use and a powerful tool.", "homepage": "https://www.doctrine-project.org/projects/migrations.html", "keywords": ["database", "dbal", "migrations"], "support": {"issues": "https://github.com/doctrine/migrations/issues", "source": "https://github.com/doctrine/migrations/tree/3.1.5"}, "time": "2021-07-03T20:49:03+00:00"}, {"name": "doctrine/orm", "version": "2.7.5", "source": {"type": "git", "url": "https://github.com/doctrine/orm.git", "reference": "01187c9260cd085529ddd1273665217cae659640"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/orm/zipball/01187c9260cd085529ddd1273665217cae659640", "reference": "01187c9260cd085529ddd1273665217cae659640", "shasum": ""}, "require": {"composer/package-versions-deprecated": "^1.8", "doctrine/annotations": "^1.11.1", "doctrine/cache": "^1.9.1", "doctrine/collections": "^1.5", "doctrine/common": "^2.11 || ^3.0", "doctrine/dbal": "^2.9.3", "doctrine/event-manager": "^1.1", "doctrine/inflector": "^1.0", "doctrine/instantiator": "^1.3", "doctrine/lexer": "^1.0", "doctrine/persistence": "^1.3.3 || ^2.0", "ext-pdo": "*", "php": "^7.1", "symfony/console": "^3.0|^4.0|^5.0"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpstan/phpstan": "^0.12.18", "phpunit/phpunit": "^8.0", "symfony/yaml": "^3.4|^4.0|^5.0", "vimeo/psalm": "^3.11"}, "suggest": {"symfony/yaml": "If you want to use YAML Metadata Mapping Driver"}, "bin": ["bin/doctrine"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.7.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\ORM\\": "lib/Doctrine/ORM"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Object-Relational-Mapper for PHP", "homepage": "https://www.doctrine-project.org/projects/orm.html", "keywords": ["database", "orm"], "support": {"issues": "https://github.com/doctrine/orm/issues", "source": "https://github.com/doctrine/orm/tree/2.7.5"}, "time": "2020-12-03T08:52:14+00:00"}, {"name": "doctrine/persistence", "version": "1.3.8", "source": {"type": "git", "url": "https://github.com/doctrine/persistence.git", "reference": "7a6eac9fb6f61bba91328f15aa7547f4806ca288"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/persistence/zipball/7a6eac9fb6f61bba91328f15aa7547f4806ca288", "reference": "7a6eac9fb6f61bba91328f15aa7547f4806ca288", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "doctrine/cache": "^1.0", "doctrine/collections": "^1.0", "doctrine/event-manager": "^1.0", "doctrine/reflection": "^1.2", "php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.10@dev"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpstan/phpstan": "^0.11", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "vimeo/psalm": "^3.11"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common", "Doctrine\\Persistence\\": "lib/Doctrine/Persistence"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Persistence project is a set of shared interfaces and functionality that the different Doctrine object mappers share.", "homepage": "https://doctrine-project.org/projects/persistence.html", "keywords": ["mapper", "object", "odm", "orm", "persistence"], "support": {"issues": "https://github.com/doctrine/persistence/issues", "source": "https://github.com/doctrine/persistence/tree/1.3.x"}, "time": "2020-06-20T12:56:16+00:00"}, {"name": "doctrine/reflection", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/doctrine/reflection.git", "reference": "1034e5e71f89978b80f9c1570e7226f6c3b9b6fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/reflection/zipball/1034e5e71f89978b80f9c1570e7226f6c3b9b6fb", "reference": "1034e5e71f89978b80f9c1570e7226f6c3b9b6fb", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "ext-tokenizer": "*", "php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^9", "doctrine/common": "^3.3", "phpstan/phpstan": "^1.4.10", "phpstan/phpstan-phpunit": "^1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Reflection project is a simple library used by the various Doctrine projects which adds some additional functionality on top of the reflection functionality that comes with PHP. It allows you to get the reflection information about classes, methods and properties statically.", "homepage": "https://www.doctrine-project.org/projects/reflection.html", "keywords": ["reflection", "static"], "support": {"issues": "https://github.com/doctrine/reflection/issues", "source": "https://github.com/doctrine/reflection/tree/1.2.3"}, "abandoned": "roave/better-reflection", "time": "2022-05-31T18:46:25+00:00"}, {"name": "egulias/email-validator", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "f88dcf4b14af14a98ad96b14b2b317969eab6715"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/f88dcf4b14af14a98ad96b14b2b317969eab6715", "reference": "f88dcf4b14af14a98ad96b14b2b317969eab6715", "shasum": ""}, "require": {"doctrine/lexer": "^1.2", "php": ">=7.2", "symfony/polyfill-intl-idn": "^1.15"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^8.5.8|^9.3.3", "vimeo/psalm": "^4"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/3.2.1"}, "time": "2022-06-18T20:57:19+00:00"}, {"name": "firebase/php-jwt", "version": "v5.5.1", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "83b609028194aa042ea33b5af2d41a7427de80e6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/83b609028194aa042ea33b5af2d41a7427de80e6", "reference": "83b609028194aa042ea33b5af2d41a7427de80e6", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": ">=4.8 <=9"}, "suggest": {"paragonie/sodium_compat": "Support EdDSA (Ed25519) signatures when libsodium is not present"}, "type": "library", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "support": {"issues": "https://github.com/firebase/php-jwt/issues", "source": "https://github.com/firebase/php-jwt/tree/v5.5.1"}, "time": "2021-11-08T20:18:51+00:00"}, {"name": "friendsofphp/proxy-manager-lts", "version": "v1.0.12", "source": {"type": "git", "url": "https://github.com/FriendsOfPHP/proxy-manager-lts.git", "reference": "8419f0158715b30d4b99a5bd37c6a39671994ad7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FriendsOfPHP/proxy-manager-lts/zipball/8419f0158715b30d4b99a5bd37c6a39671994ad7", "reference": "8419f0158715b30d4b99a5bd37c6a39671994ad7", "shasum": ""}, "require": {"laminas/laminas-code": "~3.4.1|^4.0", "php": ">=7.1", "symfony/filesystem": "^4.4.17|^5.0|^6.0"}, "conflict": {"laminas/laminas-stdlib": "<3.2.1", "zendframework/zend-stdlib": "<3.2.1"}, "replace": {"ocramius/proxy-manager": "^2.1"}, "require-dev": {"ext-phar": "*", "symfony/phpunit-bridge": "^5.4|^6.0"}, "type": "library", "extra": {"thanks": {"name": "ocramius/proxy-manager", "url": "https://github.com/Ocramius/ProxyManager"}}, "autoload": {"psr-4": {"ProxyManager\\": "src/ProxyManager"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Adding support for a wider range of PHP versions to ocramius/proxy-manager", "homepage": "https://github.com/FriendsOfPHP/proxy-manager-lts", "keywords": ["aop", "lazy loading", "proxy", "proxy pattern", "service proxies"], "support": {"issues": "https://github.com/FriendsOfPHP/proxy-manager-lts/issues", "source": "https://github.com/FriendsOfPHP/proxy-manager-lts/tree/v1.0.12"}, "time": "2022-05-05T09:31:05+00:00"}, {"name": "graylog2/gelf-php", "version": "1.7.1", "source": {"type": "git", "url": "https://github.com/bzikarsky/gelf-php.git", "reference": "8dceab86227c184725479cc36ab5cae4da940f6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bzi<PERSON>sky/gelf-php/zipball/8dceab86227c184725479cc36ab5cae4da940f6e", "reference": "8dceab86227c184725479cc36ab5cae4da940f6e", "shasum": ""}, "require": {"paragonie/constant_time_encoding": "^1|^2", "php": ">=5.6", "psr/log": "^1.0|^2.0"}, "provide": {"psr/log-implementation": "~1.0"}, "require-dev": {"phpunit/phpunit": "^5.7|^6.5|^7.5", "squizlabs/php_codesniffer": "^3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-4": {"Gelf\\": "src/Gelf"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "gelf-php contributors", "homepage": "https://github.com/bzi<PERSON>sky/gelf-php/contributors"}], "description": "A php implementation to send log-messages to a GELF compatible backend like Graylog2.", "support": {"issues": "https://github.com/bzikarsky/gelf-php/issues", "source": "https://github.com/bzikarsky/gelf-php/tree/1.7.1"}, "time": "2021-08-20T09:39:08+00:00"}, {"name": "guzzlehttp/guzzle", "version": "6.5.8", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "a52f0440530b54fa079ce76e8c5d196a42cad981"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/a52f0440530b54fa079ce76e8c5d196a42cad981", "reference": "a52f0440530b54fa079ce76e8c5d196a42cad981", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.9", "php": ">=5.5", "symfony/polyfill-intl-idn": "^1.17"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "psr/log": "^1.1"}, "suggest": {"psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.5-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/6.5.8"}, "time": "2022-06-20T22:16:07+00:00"}, {"name": "guzzlehttp/promises", "version": "1.5.1", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "fe752aedc9fd8fcca3fe7ad05d419d32998a06da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/fe752aedc9fd8fcca3fe7ad05d419d32998a06da", "reference": "fe752aedc9fd8fcca3fe7ad05d419d32998a06da", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/1.5.1"}, "time": "2021-10-22T20:56:57+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.9.0", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "e98e3e6d4f86621a9b75f623996e6bbdeb4b9318"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/e98e3e6d4f86621a9b75f623996e6bbdeb4b9318", "reference": "e98e3e6d4f86621a9b75f623996e6bbdeb4b9318", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5 || ^3.0.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"ext-zlib": "*", "phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.8 || ^9.3.10"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/1.9.0"}, "time": "2022-06-20T21:43:03+00:00"}, {"name": "jdorn/sql-formatter", "version": "v1.2.17", "source": {"type": "git", "url": "https://github.com/jdorn/sql-formatter.git", "reference": "64990d96e0959dff8e059dfcdc1af130728d92bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jdorn/sql-formatter/zipball/64990d96e0959dff8e059dfcdc1af130728d92bc", "reference": "64990d96e0959dff8e059dfcdc1af130728d92bc", "shasum": ""}, "require": {"php": ">=5.2.4"}, "require-dev": {"phpunit/phpunit": "3.7.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"classmap": ["lib"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://jeremydorn.com/"}], "description": "a PHP SQL highlighting library", "homepage": "https://github.com/jdorn/sql-formatter/", "keywords": ["highlight", "sql"], "time": "2014-01-12T16:20:24+00:00"}, {"name": "knplabs/knp-snappy", "version": "v1.4.1", "source": {"type": "git", "url": "https://github.com/KnpLabs/snappy.git", "reference": "5126fb5b335ec929a226314d40cd8dad497c3d67"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/KnpLabs/snappy/zipball/5126fb5b335ec929a226314d40cd8dad497c3d67", "reference": "5126fb5b335ec929a226314d40cd8dad497c3d67", "shasum": ""}, "require": {"php": ">=7.1", "psr/log": "^1.0||^2.0||^3.0", "symfony/process": "~3.4||~4.3||~5.0||~6.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.16||^3.0", "pedrotroller/php-cs-custom-fixer": "^2.19", "phpstan/phpstan": "^0.12.7", "phpstan/phpstan-phpunit": "^0.12.6", "phpunit/phpunit": "~7.4||~8.5"}, "suggest": {"h4cc/wkhtmltoimage-amd64": "Provides wkhtmltoimage-amd64 binary for Linux-compatible machines, use version `~0.12` as dependency", "h4cc/wkhtmltoimage-i386": "Provides wkhtmltoimage-i386 binary for Linux-compatible machines, use version `~0.12` as dependency", "h4cc/wkhtmltopdf-amd64": "Provides wkhtmltopdf-amd64 binary for Linux-compatible machines, use version `~0.12` as dependency", "h4cc/wkhtmltopdf-i386": "Provides wkhtmltopdf-i386 binary for Linux-compatible machines, use version `~0.12` as dependency", "wemersonjanuario/wkhtmltopdf-windows": "Provides wkhtmltopdf executable for Windows, use version `~0.12` as dependency"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Knp\\Snappy\\": "src/Knp/Snappy"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "KNP Labs Team", "homepage": "http://knplabs.com"}, {"name": "Symfony Community", "homepage": "http://github.com/KnpLabs/snappy/contributors"}], "description": "PHP library allowing thumbnail, snapshot or PDF generation from a url or a html page. Wrapper for wkhtmltopdf/wkhtmltoimage.", "homepage": "http://github.com/KnpLabs/snappy", "keywords": ["knp", "knplabs", "pdf", "snapshot", "thumbnail", "wkhtmltopdf"], "support": {"issues": "https://github.com/KnpLabs/snappy/issues", "source": "https://github.com/KnpLabs/snappy/tree/v1.4.1"}, "time": "2022-01-07T13:03:38+00:00"}, {"name": "laminas/laminas-code", "version": "4.6.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-code.git", "reference": "16ec7577ff315d53ac2e1b1f03a344d8fe680a6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-code/zipball/16ec7577ff315d53ac2e1b1f03a344d8fe680a6e", "reference": "16ec7577ff315d53ac2e1b1f03a344d8fe680a6e", "shasum": ""}, "require": {"php": ">=7.4, <8.2"}, "require-dev": {"doctrine/annotations": "^1.13.2", "ext-phar": "*", "laminas/laminas-coding-standard": "^2.3.0", "laminas/laminas-stdlib": "^3.6.1", "phpunit/phpunit": "^9.5.10", "psalm/plugin-phpunit": "^0.17.0", "vimeo/psalm": "^4.13.1"}, "suggest": {"doctrine/annotations": "Doctrine\\Common\\Annotations >=1.0 for annotation features", "laminas/laminas-stdlib": "Laminas\\Stdlib component"}, "type": "library", "autoload": {"files": ["polyfill/ReflectionEnumPolyfill.php"], "psr-4": {"Laminas\\Code\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Extensions to the PHP Reflection API, static code scanning, and code generation", "homepage": "https://laminas.dev", "keywords": ["code", "laminas", "laminasframework"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-code/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-code/issues", "rss": "https://github.com/laminas/laminas-code/releases.atom", "source": "https://github.com/laminas/laminas-code"}, "time": "2022-07-28T22:46:52+00:00"}, {"name": "league/html-to-markdown", "version": "4.10.0", "source": {"type": "git", "url": "https://github.com/thephpleague/html-to-markdown.git", "reference": "0868ae7a552e809e5cd8f93ba022071640408e88"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/html-to-markdown/zipball/0868ae7a552e809e5cd8f93ba022071640408e88", "reference": "0868ae7a552e809e5cd8f93ba022071640408e88", "shasum": ""}, "require": {"ext-dom": "*", "ext-xml": "*", "php": ">=5.3.3"}, "require-dev": {"mikehaertl/php-shellcommand": "~1.1.0", "phpunit/phpunit": "^4.8|^5.7", "scrutinizer/ocular": "~1.1"}, "bin": ["bin/html-to-markdown"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.10-dev"}}, "autoload": {"psr-4": {"League\\HTMLToMarkdown\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://modernnerd.net", "role": "Original Author"}], "description": "An HTML-to-markdown conversion helper for PHP", "homepage": "https://github.com/thephpleague/html-to-markdown", "keywords": ["html", "markdown"], "support": {"issues": "https://github.com/thephpleague/html-to-markdown/issues", "source": "https://github.com/thephpleague/html-to-markdown/tree/4.10.0"}, "time": "2020-07-01T00:34:03+00:00"}, {"name": "maxmind-db/reader", "version": "v1.11.0", "source": {"type": "git", "url": "https://github.com/maxmind/MaxMind-DB-Reader-php.git", "reference": "b1f3c0699525336d09cc5161a2861268d9f2ae5b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maxmind/MaxMind-DB-Reader-php/zipball/b1f3c0699525336d09cc5161a2861268d9f2ae5b", "reference": "b1f3c0699525336d09cc5161a2861268d9f2ae5b", "shasum": ""}, "require": {"php": ">=7.2"}, "conflict": {"ext-maxminddb": "<1.10.1,>=2.0.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "3.*", "php-coveralls/php-coveralls": "^2.1", "phpstan/phpstan": "*", "phpunit/phpcov": ">=6.0.0", "phpunit/phpunit": ">=8.0.0,<10.0.0", "squizlabs/php_codesniffer": "3.*"}, "suggest": {"ext-bcmath": "bcmath or gmp is required for decoding larger integers with the pure PHP decoder", "ext-gmp": "bcmath or gmp is required for decoding larger integers with the pure PHP decoder", "ext-maxminddb": "A C-based database decoder that provides significantly faster lookups"}, "type": "library", "autoload": {"psr-4": {"MaxMind\\Db\\": "src/MaxMind/Db"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.maxmind.com/"}], "description": "MaxMind DB Reader API", "homepage": "https://github.com/maxmind/MaxMind-DB-Reader-php", "keywords": ["database", "geoip", "geoip2", "geolocation", "maxmind"], "time": "2021-10-18T15:23:10+00:00"}, {"name": "mobiledetect/mobiledetectlib", "version": "2.8.39", "source": {"type": "git", "url": "https://github.com/serbanghita/Mobile-Detect.git", "reference": "0fd6753003fc870f6e229bae869cc1337c99bc45"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/serbanghita/Mobile-Detect/zipball/0fd6753003fc870f6e229bae869cc1337c99bc45", "reference": "0fd6753003fc870f6e229bae869cc1337c99bc45", "shasum": ""}, "require": {"php": ">=5.0.0"}, "require-dev": {"phpunit/phpunit": "~4.8.35||~5.7"}, "type": "library", "autoload": {"psr-0": {"Detection": "namespaced/"}, "classmap": ["Mobile_Detect.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "serban<PERSON><PERSON>@gmail.com", "homepage": "http://mobiledetect.net", "role": "Developer"}], "description": "Mobile_Detect is a lightweight PHP class for detecting mobile devices. It uses the User-Agent string combined with specific HTTP headers to detect the mobile environment.", "homepage": "https://github.com/serbanghita/Mobile-Detect", "keywords": ["detect mobile devices", "mobile", "mobile detect", "mobile detector", "php mobile detect"], "support": {"issues": "https://github.com/serbanghita/Mobile-Detect/issues", "source": "https://github.com/serbanghita/Mobile-Detect/tree/2.8.39"}, "time": "2022-02-17T19:24:25+00:00"}, {"name": "monolog/monolog", "version": "1.27.1", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "904713c5929655dc9b97288b69cfeedad610c9a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/904713c5929655dc9b97288b69cfeedad610c9a1", "reference": "904713c5929655dc9b97288b69cfeedad610c9a1", "shasum": ""}, "require": {"php": ">=5.3.0", "psr/log": "~1.0"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "graylog2/gelf-php": "~1.0", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "phpstan/phpstan": "^0.12.59", "phpunit/phpunit": "~4.5", "ruflin/elastica": ">=0.90 <3.0", "sentry/sentry": "^0.13", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server via PHP Driver", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "sentry/sentry": "Allow sending log messages to a Sentry server"}, "type": "library", "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "http://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.27.1"}, "time": "2022-06-09T08:53:42+00:00"}, {"name": "paragonie/constant_time_encoding", "version": "v2.6.3", "source": {"type": "git", "url": "https://github.com/paragonie/constant_time_encoding.git", "reference": "58c3f47f650c94ec05a151692652a868995d2938"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/58c3f47f650c94ec05a151692652a868995d2938", "reference": "58c3f47f650c94ec05a151692652a868995d2938", "shasum": ""}, "require": {"php": "^7|^8"}, "require-dev": {"phpunit/phpunit": "^6|^7|^8|^9", "vimeo/psalm": "^1|^2|^3|^4"}, "type": "library", "autoload": {"psr-4": {"ParagonIE\\ConstantTime\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com", "role": "Maintainer"}, {"name": "<PERSON> 'Sc00bz' <PERSON>", "email": "<EMAIL>", "homepage": "https://www.tobtu.com", "role": "Original Developer"}], "description": "Constant-time Implementations of RFC 4648 Encoding (Base-64, Base-32, Base-16)", "keywords": ["base16", "base32", "base32_decode", "base32_encode", "base64", "base64_decode", "base64_encode", "bin2hex", "encoding", "hex", "hex2bin", "rfc4648"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/constant_time_encoding/issues", "source": "https://github.com/paragonie/constant_time_encoding"}, "time": "2022-06-14T06:56:20+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "support": {"issues": "https://github.com/phpDocumentor/ReflectionCommon/issues", "source": "https://github.com/phpDocumentor/ReflectionCommon/tree/2.x"}, "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "5.3.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "622548b623e81ca6d78b721c5e029f4ce664f170"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/622548b623e81ca6d78b721c5e029f4ce664f170", "reference": "622548b623e81ca6d78b721c5e029f4ce664f170", "shasum": ""}, "require": {"ext-filter": "*", "php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.2", "phpdocumentor/type-resolver": "^1.3", "webmozart/assert": "^1.9.1"}, "require-dev": {"mockery/mockery": "~1.3.2", "psalm/phar": "^4.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.3.0"}, "time": "2021-10-19T17:43:47+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.6.1", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "77a32518733312af16a44300404e945338981de3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/77a32518733312af16a44300404e945338981de3", "reference": "77a32518733312af16a44300404e945338981de3", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.0"}, "require-dev": {"ext-tokenizer": "*", "psalm/phar": "^4.8"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.6.1"}, "time": "2022-03-15T21:29:03+00:00"}, {"name": "phpstan/phpdoc-parser", "version": "1.7.0", "source": {"type": "git", "url": "https://github.com/phpstan/phpdoc-parser.git", "reference": "367a8d9d5f7da2a0136422d27ce8840583926955"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/367a8d9d5f7da2a0136422d27ce8840583926955", "reference": "367a8d9d5f7da2a0136422d27ce8840583926955", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.5", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.0", "phpunit/phpunit": "^9.5", "symfony/process": "^5.2"}, "type": "library", "autoload": {"psr-4": {"PHPStan\\PhpDocParser\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPDoc parser with support for nullable, intersection and generic types", "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.7.0"}, "time": "2022-08-09T12:23:23+00:00"}, {"name": "psr/cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "time": "2016-08-06T20:24:11+00:00"}, {"name": "psr/container", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/513e0666f7216c7459170d56df27dfcefe1689ea", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/1.1.2"}, "time": "2021-11-05T16:50:12+00:00"}, {"name": "psr/http-message", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "time": "2016-08-06T14:39:51+00:00"}, {"name": "psr/log", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.4"}, "time": "2021-05-03T11:20:27+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "time": "2019-03-08T08:55:37+00:00"}, {"name": "sabre/dav", "version": "3.2.3", "source": {"type": "git", "url": "https://github.com/sabre-io/dav.git", "reference": "a9780ce4f35560ecbd0af524ad32d9d2c8954b80"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sabre-io/dav/zipball/a9780ce4f35560ecbd0af524ad32d9d2c8954b80", "reference": "a9780ce4f35560ecbd0af524ad32d9d2c8954b80", "shasum": ""}, "require": {"ext-ctype": "*", "ext-date": "*", "ext-dom": "*", "ext-iconv": "*", "ext-mbstring": "*", "ext-pcre": "*", "ext-simplexml": "*", "ext-spl": "*", "lib-libxml": ">=2.7.0", "php": ">=5.5.0", "psr/log": "^1.0", "sabre/event": ">=2.0.0, <4.0.0", "sabre/http": "^4.2.1", "sabre/uri": "^1.0.1", "sabre/vobject": "^4.1.0", "sabre/xml": "^1.4.0"}, "require-dev": {"evert/phpdoc-md": "~0.1.0", "monolog/monolog": "^1.18", "phpunit/phpunit": "> 4.8, <6.0.0", "sabre/cs": "^1.0.0"}, "suggest": {"ext-curl": "*", "ext-pdo": "*"}, "bin": ["bin/sabredav", "bin/naturalselection"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.1.0-dev"}}, "autoload": {"psr-4": {"Sabre\\DAV\\": "lib/DAV/", "Sabre\\DAVACL\\": "lib/DAVACL/", "Sabre\\CalDAV\\": "lib/CalDAV/", "Sabre\\CardDAV\\": "lib/CardDAV/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://evertpot.com/", "role": "Developer"}], "description": "WebDAV Framework for PHP", "homepage": "http://sabre.io/", "keywords": ["CalDAV", "CardDAV", "WebDAV", "framework", "iCalendar"], "time": "2018-10-19T09:58:27+00:00"}, {"name": "sabre/event", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/sabre-io/event.git", "reference": "831d586f5a442dceacdcf5e9c4c36a4db99a3534"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sabre-io/event/zipball/831d586f5a442dceacdcf5e9c4c36a4db99a3534", "reference": "831d586f5a442dceacdcf5e9c4c36a4db99a3534", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "*", "sabre/cs": "~0.0.4"}, "type": "library", "autoload": {"psr-4": {"Sabre\\Event\\": "lib/"}, "files": ["lib/coroutine.php", "lib/Loop/functions.php", "lib/Promise/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://evertpot.com/", "role": "Developer"}], "description": "sabre/event is a library for lightweight event-based programming", "homepage": "http://sabre.io/event/", "keywords": ["EventEmitter", "async", "events", "hooks", "plugin", "promise", "signal"], "time": "2015-11-05T20:14:39+00:00"}, {"name": "sabre/http", "version": "v4.2.4", "source": {"type": "git", "url": "https://github.com/sabre-io/http.git", "reference": "acccec4ba863959b2d10c1fa0fb902736c5c8956"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sabre-io/http/zipball/acccec4ba863959b2d10c1fa0fb902736c5c8956", "reference": "acccec4ba863959b2d10c1fa0fb902736c5c8956", "shasum": ""}, "require": {"ext-ctype": "*", "ext-mbstring": "*", "php": ">=5.4", "sabre/event": ">=1.0.0,<4.0.0", "sabre/uri": "~1.0"}, "require-dev": {"phpunit/phpunit": "~4.3", "sabre/cs": "~0.0.1"}, "suggest": {"ext-curl": " to make http requests with the Client class"}, "type": "library", "autoload": {"files": ["lib/functions.php"], "psr-4": {"Sabre\\HTTP\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://evertpot.com/", "role": "Developer"}], "description": "The sabre/http library provides utilities for dealing with http requests and responses. ", "homepage": "https://github.com/fruux/sabre-http", "keywords": ["http"], "time": "2018-02-23T11:10:29+00:00"}, {"name": "sabre/uri", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/sabre-io/uri.git", "reference": "ada354d83579565949d80b2e15593c2371225e61"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sabre-io/uri/zipball/ada354d83579565949d80b2e15593c2371225e61", "reference": "ada354d83579565949d80b2e15593c2371225e61", "shasum": ""}, "require": {"php": ">=5.4.7"}, "require-dev": {"phpunit/phpunit": ">=4.0,<6.0", "sabre/cs": "~1.0.0"}, "type": "library", "autoload": {"files": ["lib/functions.php"], "psr-4": {"Sabre\\Uri\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://evertpot.com/", "role": "Developer"}], "description": "Functions for making sense out of URIs.", "homepage": "http://sabre.io/uri/", "keywords": ["rfc3986", "uri", "url"], "time": "2017-02-20T19:59:28+00:00"}, {"name": "sabre/vobject", "version": "4.2.2", "source": {"type": "git", "url": "https://github.com/sabre-io/vobject.git", "reference": "449616b2d45b95c8973975de23f34a3d14f63b4b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sabre-io/vobject/zipball/449616b2d45b95c8973975de23f34a3d14f63b4b", "reference": "449616b2d45b95c8973975de23f34a3d14f63b4b", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.5", "sabre/xml": ">=1.5 <3.0"}, "require-dev": {"phpunit/phpunit": "> 4.8.35, <6.0.0"}, "suggest": {"hoa/bench": "If you would like to run the benchmark scripts"}, "bin": ["bin/vobject", "bin/generate_vcards"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"psr-4": {"Sabre\\VObject\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://evertpot.com/", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://tobschall.de/", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://mnt.io/", "role": "Developer"}], "description": "The VObject library for PHP allows you to easily parse and manipulate iCalendar and vCard objects", "homepage": "http://sabre.io/vobject/", "keywords": ["availability", "freebusy", "iCalendar", "ical", "ics", "jCal", "jCard", "recurrence", "rfc2425", "rfc2426", "rfc2739", "rfc4770", "rfc5545", "rfc5546", "rfc6321", "rfc6350", "rfc6351", "rfc6474", "rfc6638", "rfc6715", "rfc6868", "vCalendar", "vCard", "vcf", "xCal", "xCard"], "time": "2020-01-14T10:18:45+00:00"}, {"name": "sabre/xml", "version": "1.5.1", "source": {"type": "git", "url": "https://github.com/sabre-io/xml.git", "reference": "a367665f1df614c3b8fefc30a54de7cd295e444e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sabre-io/xml/zipball/a367665f1df614c3b8fefc30a54de7cd295e444e", "reference": "a367665f1df614c3b8fefc30a54de7cd295e444e", "shasum": ""}, "require": {"ext-dom": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "lib-libxml": ">=2.6.20", "php": ">=5.5.5", "sabre/uri": ">=1.0,<3.0.0"}, "require-dev": {"phpunit/phpunit": "~4.8|~5.7", "sabre/cs": "~1.0.0"}, "type": "library", "autoload": {"psr-4": {"Sabre\\Xml\\": "lib/"}, "files": ["lib/Deserializer/functions.php", "lib/Serializer/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://evertpot.com/", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "sabre/xml is an XML library that you may not hate.", "homepage": "https://sabre.io/xml/", "keywords": ["XMLReader", "XMLWriter", "dom", "xml"], "time": "2019-01-09T13:51:57+00:00"}, {"name": "sensio/framework-extra-bundle", "version": "v5.6.1", "source": {"type": "git", "url": "https://github.com/sensiolabs/SensioFrameworkExtraBundle.git", "reference": "430d14c01836b77c28092883d195a43ce413ee32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sensiolabs/SensioFrameworkExtraBundle/zipball/430d14c01836b77c28092883d195a43ce413ee32", "reference": "430d14c01836b77c28092883d195a43ce413ee32", "shasum": ""}, "require": {"doctrine/annotations": "^1.0", "php": ">=7.2.5", "symfony/config": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/framework-bundle": "^4.4|^5.0", "symfony/http-kernel": "^4.4|^5.0"}, "conflict": {"doctrine/doctrine-cache-bundle": "<1.3.1", "doctrine/persistence": "<1.3"}, "require-dev": {"doctrine/dbal": "^2.10|^3.0", "doctrine/doctrine-bundle": "^1.11|^2.0", "doctrine/orm": "^2.5", "nyholm/psr7": "^1.1", "symfony/browser-kit": "^4.4|^5.0", "symfony/doctrine-bridge": "^4.4|^5.0", "symfony/dom-crawler": "^4.4|^5.0", "symfony/expression-language": "^4.4|^5.0", "symfony/finder": "^4.4|^5.0", "symfony/monolog-bridge": "^4.0|^5.0", "symfony/monolog-bundle": "^3.2", "symfony/phpunit-bridge": "^4.4.9|^5.0.9", "symfony/psr-http-message-bridge": "^1.1", "symfony/security-bundle": "^4.4|^5.0", "symfony/twig-bundle": "^4.4|^5.0", "symfony/yaml": "^4.4|^5.0", "twig/twig": "^1.34|^2.4|^3.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "5.6.x-dev"}}, "autoload": {"psr-4": {"Sensio\\Bundle\\FrameworkExtraBundle\\": "src/"}, "exclude-from-classmap": ["/tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "This bundle provides a way to configure your controllers with annotations", "keywords": ["annotations", "controllers"], "support": {"issues": "https://github.com/sensiolabs/SensioFrameworkExtraBundle/issues", "source": "https://github.com/sensiolabs/SensioFrameworkExtraBundle/tree/v5.6.1"}, "abandoned": "Symfony", "time": "2020-08-25T19:10:18+00:00"}, {"name": "sensiolabs/security-checker", "version": "v4.1.8", "source": {"type": "git", "url": "https://github.com/sensiolabs/security-checker.git", "reference": "dc270d5fec418cc6ac983671dba5d80ffaffb142"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sensiolabs/security-checker/zipball/dc270d5fec418cc6ac983671dba5d80ffaffb142", "reference": "dc270d5fec418cc6ac983671dba5d80ffaffb142", "shasum": ""}, "require": {"composer/ca-bundle": "^1.0", "symfony/console": "~2.7|~3.0|~4.0"}, "bin": ["security-checker"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.1-dev"}}, "autoload": {"psr-0": {"SensioLabs\\Security": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A security checker for your composer.lock", "abandoned": "https://github.com/fabpot/local-php-security-checker", "time": "2018-02-28T22:10:01+00:00"}, {"name": "smalot/pdfparser", "version": "v0.11", "source": {"type": "git", "url": "https://github.com/smalot/pdfparser.git", "reference": "40462c1a662d5fbc8f48d30626695a06f047e0cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/smalot/pdfparser/zipball/40462c1a662d5fbc8f48d30626695a06f047e0cf", "reference": "40462c1a662d5fbc8f48d30626695a06f047e0cf", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=5.3.0", "tecnickcom/tcpdf": "~6.0"}, "require-dev": {"atoum/atoum": "^2.8 | ^3.0"}, "type": "library", "autoload": {"psr-0": {"Smalot\\PdfParser\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPLv3"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Pdf parser library. Can read and extract information from pdf file.", "homepage": "http://www.pdfparser.org", "keywords": ["extract", "parse", "parser", "pdf", "text"], "time": "2017-09-14T12:26:16+00:00"}, {"name": "swiftmailer/swiftmailer", "version": "v6.3.0", "source": {"type": "git", "url": "https://github.com/swiftmailer/swiftmailer.git", "reference": "8a5d5072dca8f48460fce2f4131fcc495eec654c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swiftmailer/swiftmailer/zipball/8a5d5072dca8f48460fce2f4131fcc495eec654c", "reference": "8a5d5072dca8f48460fce2f4131fcc495eec654c", "shasum": ""}, "require": {"egulias/email-validator": "^2.0|^3.1", "php": ">=7.0.0", "symfony/polyfill-iconv": "^1.0", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"mockery/mockery": "^1.0", "symfony/phpunit-bridge": "^4.4|^5.4"}, "suggest": {"ext-intl": "Needed to support internationalized email addresses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.2-dev"}}, "autoload": {"files": ["lib/swift_required.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Swiftmailer, free feature-rich PHP mailer", "homepage": "https://swiftmailer.symfony.com", "keywords": ["email", "mail", "mailer"], "support": {"issues": "https://github.com/swiftmailer/swiftmailer/issues", "source": "https://github.com/swiftmailer/swiftmailer/tree/v6.3.0"}, "abandoned": "symfony/mailer", "time": "2021-10-18T15:26:12+00:00"}, {"name": "symfony/asset", "version": "v4.4.40", "source": {"type": "git", "url": "https://github.com/symfony/asset.git", "reference": "4dee0d02664f2d06005c56b8e43612b7a372e47d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/asset/zipball/4dee0d02664f2d06005c56b8e43612b7a372e47d", "reference": "4dee0d02664f2d06005c56b8e43612b7a372e47d", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/http-kernel": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/http-foundation": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Asset\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Manages URL generation and versioning of web assets such as CSS stylesheets, JavaScript files and image files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/asset/tree/v4.4.40"}, "time": "2022-03-07T18:38:28+00:00"}, {"name": "symfony/browser-kit", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/browser-kit.git", "reference": "2a1ff40723ef6b29c8229a860a9c8f815ad7dbbb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/browser-kit/zipball/2a1ff40723ef6b29c8229a860a9c8f815ad7dbbb", "reference": "2a1ff40723ef6b29c8229a860a9c8f815ad7dbbb", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/dom-crawler": "^3.4|^4.0|^5.0", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"symfony/css-selector": "^3.4|^4.0|^5.0", "symfony/http-client": "^4.3|^5.0", "symfony/mime": "^4.3|^5.0", "symfony/process": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/process": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\BrowserKit\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Simulates the behavior of a web browser, allowing you to make requests, click on links and submit forms programmatically", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/browser-kit/tree/v4.4.44"}, "time": "2022-07-25T12:56:14+00:00"}, {"name": "symfony/cache", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/cache.git", "reference": "db611fb1b86e2223d406c5bf3d813536c95de7ce"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache/zipball/db611fb1b86e2223d406c5bf3d813536c95de7ce", "reference": "db611fb1b86e2223d406c5bf3d813536c95de7ce", "shasum": ""}, "require": {"php": ">=7.1.3", "psr/cache": "^1.0|^2.0", "psr/log": "^1|^2|^3", "symfony/cache-contracts": "^1.1.7|^2", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2", "symfony/var-exporter": "^4.2|^5.0"}, "conflict": {"doctrine/dbal": "<2.7", "symfony/dependency-injection": "<3.4", "symfony/http-kernel": "<4.4|>=5.0", "symfony/var-dumper": "<4.4"}, "provide": {"psr/cache-implementation": "1.0|2.0", "psr/simple-cache-implementation": "1.0|2.0", "symfony/cache-implementation": "1.0|2.0"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/cache": "^1.6|^2.0", "doctrine/dbal": "^2.7|^3.0", "predis/predis": "^1.1", "psr/simple-cache": "^1.0|^2.0", "symfony/config": "^4.2|^5.0", "symfony/dependency-injection": "^3.4|^4.1|^5.0", "symfony/filesystem": "^4.4|^5.0", "symfony/http-kernel": "^4.4", "symfony/var-dumper": "^4.4|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Cache\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an extended PSR-6, PSR-16 (and tags) implementation", "homepage": "https://symfony.com", "keywords": ["caching", "psr6"], "support": {"source": "https://github.com/symfony/cache/tree/v4.4.44"}, "time": "2022-07-20T09:59:04+00:00"}, {"name": "symfony/cache-contracts", "version": "v2.5.2", "source": {"type": "git", "url": "https://github.com/symfony/cache-contracts.git", "reference": "64be4a7acb83b6f2bf6de9a02cee6dad41277ebc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache-contracts/zipball/64be4a7acb83b6f2bf6de9a02cee6dad41277ebc", "reference": "64be4a7acb83b6f2bf6de9a02cee6dad41277ebc", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/cache": "^1.0|^2.0|^3.0"}, "suggest": {"symfony/cache-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Cache\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to caching", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/cache-contracts/tree/v2.5.2"}, "time": "2022-01-02T09:53:40+00:00"}, {"name": "symfony/config", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "ed42f8f9da528d2c6cae36fe1f380b0c1d8f0658"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/ed42f8f9da528d2c6cae36fe1f380b0c1d8f0658", "reference": "ed42f8f9da528d2c6cae36fe1f380b0c1d8f0658", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/filesystem": "^3.4|^4.0|^5.0", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-php80": "^1.16", "symfony/polyfill-php81": "^1.22"}, "conflict": {"symfony/finder": "<3.4"}, "require-dev": {"symfony/event-dispatcher": "^3.4|^4.0|^5.0", "symfony/finder": "^3.4|^4.0|^5.0", "symfony/messenger": "^4.1|^5.0", "symfony/service-contracts": "^1.1|^2", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/yaml": "To use the yaml reference dumper"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps you find, load, combine, autofill and validate configuration values of any kind", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/config/tree/v4.4.44"}, "time": "2022-07-20T09:59:04+00:00"}, {"name": "symfony/console", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "c35fafd7f12ebd6f9e29c95a370df7f1fb171a40"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/c35fafd7f12ebd6f9e29c95a370df7f1fb171a40", "reference": "c35fafd7f12ebd6f9e29c95a370df7f1fb171a40", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.8", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2"}, "conflict": {"psr/log": ">=3", "symfony/dependency-injection": "<3.4", "symfony/event-dispatcher": "<4.3|>=5", "symfony/lock": "<4.4", "symfony/process": "<3.3"}, "provide": {"psr/log-implementation": "1.0|2.0"}, "require-dev": {"psr/log": "^1|^2", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/event-dispatcher": "^4.3", "symfony/lock": "^4.4|^5.0", "symfony/process": "^3.4|^4.0|^5.0", "symfony/var-dumper": "^4.3|^5.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/console/tree/v4.4.44"}, "time": "2022-07-20T09:59:04+00:00"}, {"name": "symfony/debug", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/debug.git", "reference": "1a692492190773c5310bc7877cb590c04c2f05be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/debug/zipball/1a692492190773c5310bc7877cb590c04c2f05be", "reference": "1a692492190773c5310bc7877cb590c04c2f05be", "shasum": ""}, "require": {"php": ">=7.1.3", "psr/log": "^1|^2|^3"}, "conflict": {"symfony/http-kernel": "<3.4"}, "require-dev": {"symfony/http-kernel": "^3.4|^4.0|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Debug\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to ease debugging PHP code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/debug/tree/v4.4.44"}, "abandoned": "symfony/error-handler", "time": "2022-07-28T16:29:46+00:00"}, {"name": "symfony/dependency-injection", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/dependency-injection.git", "reference": "25502a57182ba1e15da0afd64c975cae4d0a1471"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dependency-injection/zipball/25502a57182ba1e15da0afd64c975cae4d0a1471", "reference": "25502a57182ba1e15da0afd64c975cae4d0a1471", "shasum": ""}, "require": {"php": ">=7.1.3", "psr/container": "^1.0", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1.6|^2"}, "conflict": {"symfony/config": "<4.3|>=5.0", "symfony/finder": "<3.4", "symfony/proxy-manager-bridge": "<3.4", "symfony/yaml": "<4.4.26"}, "provide": {"psr/container-implementation": "1.0", "symfony/service-implementation": "1.0|2.0"}, "require-dev": {"symfony/config": "^4.3", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/yaml": "^4.4.26|^5.0"}, "suggest": {"symfony/config": "", "symfony/expression-language": "For using expressions in service container configuration", "symfony/finder": "For using double-star glob patterns or when GLOB_BRACE portability is required", "symfony/proxy-manager-bridge": "Generate service proxies to lazy load them", "symfony/yaml": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DependencyInjection\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows you to standardize and centralize the way objects are constructed in your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dependency-injection/tree/v4.4.44"}, "time": "2022-07-20T09:59:04+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v2.5.2", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "e8b495ea28c1d97b5e0c121748d6f9b53d075c66"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/e8b495ea28c1d97b5e0c121748d6f9b53d075c66", "reference": "e8b495ea28c1d97b5e0c121748d6f9b53d075c66", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.5.2"}, "time": "2022-01-02T09:53:40+00:00"}, {"name": "symfony/doctrine-bridge", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/doctrine-bridge.git", "reference": "5d5a797a5bb03b68bdb62d6d16e7d599769f9bcb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/doctrine-bridge/zipball/5d5a797a5bb03b68bdb62d6d16e7d599769f9bcb", "reference": "5d5a797a5bb03b68bdb62d6d16e7d599769f9bcb", "shasum": ""}, "require": {"doctrine/event-manager": "~1.0", "doctrine/persistence": "^1.3|^2|^3", "php": ">=7.1.3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2"}, "conflict": {"doctrine/dbal": "<2.7", "doctrine/lexer": "<1.1", "doctrine/orm": "<2.6.3", "phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/dependency-injection": "<3.4", "symfony/form": "<4.4", "symfony/http-kernel": "<4.3.7", "symfony/messenger": "<4.3", "symfony/proxy-manager-bridge": "<4.4.19", "symfony/security-core": "<4.4", "symfony/validator": "<4.4.2|<5.0.2,>=5.0"}, "require-dev": {"composer/package-versions-deprecated": "^1.8", "doctrine/annotations": "^1.10.4", "doctrine/collections": "~1.0", "doctrine/data-fixtures": "^1.1", "doctrine/dbal": "^2.7|^3.0", "doctrine/orm": "^2.6.3", "symfony/config": "^4.2|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/form": "^4.4.41|^5.0.11", "symfony/http-kernel": "^4.3.7", "symfony/messenger": "^4.4|^5.0", "symfony/property-access": "^3.4|^4.0|^5.0", "symfony/property-info": "^3.4|^4.0|^5.0", "symfony/proxy-manager-bridge": "^3.4|^4.0|^5.0", "symfony/security-core": "^4.4|^5.0", "symfony/stopwatch": "^3.4|^4.0|^5.0", "symfony/translation": "^3.4|^4.0|^5.0", "symfony/validator": "^4.4.2|^5.0.2", "symfony/var-dumper": "^3.4|^4.0|^5.0"}, "suggest": {"doctrine/data-fixtures": "", "doctrine/dbal": "", "doctrine/orm": "", "symfony/form": "", "symfony/property-info": "", "symfony/validator": ""}, "type": "symfony-bridge", "autoload": {"psr-4": {"Symfony\\Bridge\\Doctrine\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides integration for Doctrine with various Symfony components", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/doctrine-bridge/tree/v4.4.44"}, "time": "2022-07-20T09:59:04+00:00"}, {"name": "symfony/dom-crawler", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/dom-crawler.git", "reference": "53cee1108a9748682b1268bc1a76a3d6a665ede2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dom-crawler/zipball/53cee1108a9748682b1268bc1a76a3d6a665ede2", "reference": "53cee1108a9748682b1268bc1a76a3d6a665ede2", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16"}, "conflict": {"masterminds/html5": "<2.6"}, "require-dev": {"masterminds/html5": "^2.6", "symfony/css-selector": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/css-selector": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DomCrawler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases DOM navigation for HTML and XML documents", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dom-crawler/tree/v4.4.44"}, "time": "2022-06-27T13:16:42+00:00"}, {"name": "symfony/error-handler", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/error-handler.git", "reference": "be731658121ef2d8be88f3a1ec938148a9237291"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/error-handler/zipball/be731658121ef2d8be88f3a1ec938148a9237291", "reference": "be731658121ef2d8be88f3a1ec938148a9237291", "shasum": ""}, "require": {"php": ">=7.1.3", "psr/log": "^1|^2|^3", "symfony/debug": "^4.4.5", "symfony/var-dumper": "^4.4|^5.0"}, "require-dev": {"symfony/http-kernel": "^4.4|^5.0", "symfony/serializer": "^4.4|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ErrorHandler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to manage errors and ease debugging PHP code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/error-handler/tree/v4.4.44"}, "time": "2022-07-28T16:29:46+00:00"}, {"name": "symfony/event-dispatcher", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "1e866e9e5c1b22168e0ce5f0b467f19bba61266a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/1e866e9e5c1b22168e0ce5f0b467f19bba61266a", "reference": "1e866e9e5c1b22168e0ce5f0b467f19bba61266a", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/event-dispatcher-contracts": "^1.1", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/dependency-injection": "<3.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "1.1"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/error-handler": "~3.4|~4.4", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1|^2", "symfony/stopwatch": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.4.44"}, "time": "2022-07-20T09:59:04+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v1.1.13", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "1d5cd762abaa6b2a4169d3e77610193a7157129e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/1d5cd762abaa6b2a4169d3e77610193a7157129e", "reference": "1d5cd762abaa6b2a4169d3e77610193a7157129e", "shasum": ""}, "require": {"php": ">=7.1.3"}, "suggest": {"psr/event-dispatcher": "", "symfony/event-dispatcher-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.1-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v1.1.13"}, "time": "2022-01-02T09:41:36+00:00"}, {"name": "symfony/expression-language", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/expression-language.git", "reference": "13f0e1fd96066299eb39c87473446805fcf57c41"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/expression-language/zipball/13f0e1fd96066299eb39c87473446805fcf57c41", "reference": "13f0e1fd96066299eb39c87473446805fcf57c41", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/cache": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1|^2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ExpressionLanguage\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an engine that can compile and evaluate expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/expression-language/tree/v4.4.44"}, "time": "2022-07-20T09:59:04+00:00"}, {"name": "symfony/filesystem", "version": "v4.4.42", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "815412ee8971209bd4c1eecd5f4f481eacd44bf5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/815412ee8971209bd4c1eecd5f4f481eacd44bf5", "reference": "815412ee8971209bd4c1eecd5f4f481eacd44bf5", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v4.4.42"}, "time": "2022-05-20T08:49:14+00:00"}, {"name": "symfony/finder", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "66bd787edb5e42ff59d3523f623895af05043e4f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/66bd787edb5e42ff59d3523f623895af05043e4f", "reference": "66bd787edb5e42ff59d3523f623895af05043e4f", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v4.4.44"}, "time": "2022-07-29T07:35:46+00:00"}, {"name": "symfony/form", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/form.git", "reference": "db35f2e2c237c41baa8036d256f1b913a2430d04"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/form/zipball/db35f2e2c237c41baa8036d256f1b913a2430d04", "reference": "db35f2e2c237c41baa8036d256f1b913a2430d04", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/event-dispatcher": "^4.3", "symfony/intl": "^4.4|^5.0", "symfony/options-resolver": "~4.3|^5.0", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16", "symfony/property-access": "^3.4.40|^4.4.8|^5.0.8", "symfony/service-contracts": "^1.1|^2"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/console": "<4.3", "symfony/dependency-injection": "<3.4", "symfony/doctrine-bridge": "<3.4", "symfony/framework-bundle": "<3.4", "symfony/http-kernel": "<4.4", "symfony/intl": "<4.3", "symfony/translation": "<4.2", "symfony/twig-bridge": "<3.4.5|<4.0.5,>=4.0"}, "require-dev": {"doctrine/collections": "~1.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/console": "^4.3|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/http-kernel": "^4.4", "symfony/security-csrf": "^3.4|^4.0|^5.0", "symfony/translation": "^4.2|^5.0", "symfony/validator": "^4.4.17|^5.1.9", "symfony/var-dumper": "^4.3|^5.0"}, "suggest": {"symfony/security-csrf": "For protecting forms against CSRF attacks.", "symfony/twig-bridge": "For templating with <PERSON><PERSON>.", "symfony/validator": "For form validation."}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Form\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows to easily create, process and reuse HTML forms", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/form/tree/v4.4.44"}, "time": "2022-07-20T09:59:04+00:00"}, {"name": "symfony/framework-bundle", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/framework-bundle.git", "reference": "3cbca8f6b93681102cab0fe0e3c23d16e4d0ded3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/framework-bundle/zipball/3cbca8f6b93681102cab0fe0e3c23d16e4d0ded3", "reference": "3cbca8f6b93681102cab0fe0e3c23d16e4d0ded3", "shasum": ""}, "require": {"ext-xml": "*", "php": ">=7.1.3", "symfony/cache": "^4.4|^5.0", "symfony/config": "^4.4.11|~5.0.11|^5.1.3", "symfony/dependency-injection": "^4.4.38|^5.0.1", "symfony/error-handler": "^4.4.1|^5.0.1", "symfony/filesystem": "^3.4|^4.0|^5.0", "symfony/finder": "^3.4|^4.0|^5.0", "symfony/http-foundation": "^4.4|^5.0", "symfony/http-kernel": "^4.4", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16", "symfony/routing": "^4.4.12|^5.1.4"}, "conflict": {"doctrine/persistence": "<1.3", "phpdocumentor/reflection-docblock": "<3.0|>=3.2.0,<3.2.2", "phpdocumentor/type-resolver": "<0.3.0|1.3.*", "phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/asset": "<3.4", "symfony/browser-kit": "<4.3", "symfony/console": "<4.4.21", "symfony/dom-crawler": "<4.3", "symfony/dotenv": "<4.3.6", "symfony/form": "<4.3.5", "symfony/http-client": "<4.4", "symfony/lock": "<4.4", "symfony/mailer": "<4.4", "symfony/messenger": "<4.4", "symfony/mime": "<4.4", "symfony/property-info": "<3.4", "symfony/security-bundle": "<4.4", "symfony/serializer": "<4.4", "symfony/stopwatch": "<3.4", "symfony/translation": "<4.4", "symfony/twig-bridge": "<4.1.1", "symfony/twig-bundle": "<4.4", "symfony/validator": "<4.4", "symfony/web-profiler-bundle": "<4.4", "symfony/workflow": "<4.3.6"}, "require-dev": {"doctrine/annotations": "^1.10.4", "doctrine/cache": "^1.0|^2.0", "doctrine/persistence": "^1.3|^2|^3", "paragonie/sodium_compat": "^1.8", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/asset": "^3.4|^4.0|^5.0", "symfony/browser-kit": "^4.3|^5.0", "symfony/console": "^4.4.42|^5.4.9", "symfony/css-selector": "^3.4|^4.0|^5.0", "symfony/dom-crawler": "^4.4.30|^5.3.7", "symfony/dotenv": "^4.3.6|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/form": "^4.3.5|^5.0", "symfony/http-client": "^4.4|^5.0", "symfony/lock": "^4.4|^5.0", "symfony/mailer": "^4.4|^5.0", "symfony/messenger": "^4.4|^5.0", "symfony/mime": "^4.4|^5.0", "symfony/polyfill-intl-icu": "~1.0", "symfony/process": "^3.4|^4.0|^5.0", "symfony/property-info": "^3.4|^4.0|^5.0", "symfony/security-core": "^3.4|^4.4|^5.2", "symfony/security-csrf": "^3.4|^4.0|^5.0", "symfony/security-http": "^3.4|^4.0|^5.0", "symfony/serializer": "^4.4|^5.0", "symfony/stopwatch": "^3.4|^4.0|^5.0", "symfony/templating": "^3.4|^4.0|^5.0", "symfony/translation": "^4.4|^5.0", "symfony/twig-bundle": "^4.4|^5.0", "symfony/validator": "^4.4|^5.0", "symfony/web-link": "^4.4|^5.0", "symfony/workflow": "^4.3.6|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0", "twig/twig": "^1.43|^2.13|^3.0.4"}, "suggest": {"ext-apcu": "For best performance of the system caches", "symfony/console": "For using the console commands", "symfony/form": "For using forms", "symfony/property-info": "For using the property_info service", "symfony/serializer": "For using the serializer service", "symfony/validator": "For using validation", "symfony/web-link": "For using web links, features such as preloading, prefetching or prerendering", "symfony/yaml": "For using the debug:config and lint:yaml commands"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\FrameworkBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a tight integration between Symfony components and the Symfony full-stack framework", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/framework-bundle/tree/v4.4.44"}, "time": "2022-07-20T09:59:04+00:00"}, {"name": "symfony/http-client", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/http-client.git", "reference": "900632460c2ed5fa656b13cb911ff8f702918618"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client/zipball/900632460c2ed5fa656b13cb911ff8f702918618", "reference": "900632460c2ed5fa656b13cb911ff8f702918618", "shasum": ""}, "require": {"php": ">=7.1.3", "psr/log": "^1|^2|^3", "symfony/http-client-contracts": "^1.1.10|^2", "symfony/polyfill-php73": "^1.11", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.0|^2"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "1.0", "symfony/http-client-implementation": "1.1|2.0"}, "require-dev": {"guzzlehttp/promises": "^1.4", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^4.3|^5.0", "symfony/http-kernel": "^4.4.13", "symfony/process": "^4.2|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpClient\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides powerful methods to fetch HTTP resources synchronously or asynchronously", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-client/tree/v4.4.44"}, "time": "2022-07-27T17:16:03+00:00"}, {"name": "symfony/http-client-contracts", "version": "v2.5.2", "source": {"type": "git", "url": "https://github.com/symfony/http-client-contracts.git", "reference": "ba6a9f0e8f3edd190520ee3b9a958596b6ca2e70"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/ba6a9f0e8f3edd190520ee3b9a958596b6ca2e70", "reference": "ba6a9f0e8f3edd190520ee3b9a958596b6ca2e70", "shasum": ""}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/http-client-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\HttpClient\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to HTTP clients", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v2.5.2"}, "time": "2022-04-12T15:48:08+00:00"}, {"name": "symfony/http-foundation", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "9bc83c2f78949fc64503134a3139a7b055890d06"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/9bc83c2f78949fc64503134a3139a7b055890d06", "reference": "9bc83c2f78949fc64503134a3139a7b055890d06", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/mime": "^4.3|^5.0", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"predis/predis": "~1.0", "symfony/expression-language": "^3.4|^4.0|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v4.4.44"}, "time": "2022-07-20T09:59:04+00:00"}, {"name": "symfony/http-kernel", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "9e444442334fae9637ef3209bc2abddfef49e714"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/9e444442334fae9637ef3209bc2abddfef49e714", "reference": "9e444442334fae9637ef3209bc2abddfef49e714", "shasum": ""}, "require": {"php": ">=7.1.3", "psr/log": "^1|^2", "symfony/error-handler": "^4.4", "symfony/event-dispatcher": "^4.4", "symfony/http-client-contracts": "^1.1|^2", "symfony/http-foundation": "^4.4.30|^5.3.7", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/browser-kit": "<4.3", "symfony/config": "<3.4", "symfony/console": ">=5", "symfony/dependency-injection": "<4.3", "symfony/translation": "<4.2", "twig/twig": "<1.43|<2.13,>=2"}, "provide": {"psr/log-implementation": "1.0|2.0"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/browser-kit": "^4.3|^5.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/console": "^3.4|^4.0", "symfony/css-selector": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^4.3|^5.0", "symfony/dom-crawler": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/finder": "^3.4|^4.0|^5.0", "symfony/process": "^3.4|^4.0|^5.0", "symfony/routing": "^3.4|^4.0|^5.0", "symfony/stopwatch": "^3.4|^4.0|^5.0", "symfony/templating": "^3.4|^4.0|^5.0", "symfony/translation": "^4.2|^5.0", "symfony/translation-contracts": "^1.1|^2", "twig/twig": "^1.43|^2.13|^3.0.4"}, "suggest": {"symfony/browser-kit": "", "symfony/config": "", "symfony/console": "", "symfony/dependency-injection": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a structured process for converting a Request into a Response", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-kernel/tree/v4.4.44"}, "time": "2022-07-29T12:23:38+00:00"}, {"name": "symfony/inflector", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/inflector.git", "reference": "66185be61805b1e44a5c4000929e700228d426cc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/inflector/zipball/66185be61805b1e44a5c4000929e700228d426cc", "reference": "66185be61805b1e44a5c4000929e700228d426cc", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Inflector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts words between their singular and plural forms (English only)", "homepage": "https://symfony.com", "keywords": ["inflection", "pluralize", "singularize", "string", "symfony", "words"], "support": {"source": "https://github.com/symfony/inflector/tree/v4.4.44"}, "abandoned": "EnglishInflector from the String component", "time": "2022-07-20T09:59:04+00:00"}, {"name": "symfony/intl", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/intl.git", "reference": "834ba2af906d8e3531afcad9c13f4c43c5935592"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/intl/zipball/834ba2af906d8e3531afcad9c13f4c43c5935592", "reference": "834ba2af906d8e3531afcad9c13f4c43c5935592", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-intl-icu": "~1.0", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"symfony/filesystem": "^3.4|^4.0|^5.0"}, "suggest": {"ext-intl": "to use the component with locales other than \"en\""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Intl\\": ""}, "classmap": ["Resources/stubs"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a PHP replacement layer for the C intl extension that includes additional data from the ICU library", "homepage": "https://symfony.com", "keywords": ["i18n", "icu", "internationalization", "intl", "l10n", "localization"], "support": {"source": "https://github.com/symfony/intl/tree/v4.4.44"}, "time": "2022-07-20T09:59:04+00:00"}, {"name": "symfony/ldap", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/ldap.git", "reference": "445c0e5f284cff33d7f449a0b078c8d732122ceb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/ldap/zipball/445c0e5f284cff33d7f449a0b078c8d732122ceb", "reference": "445c0e5f284cff33d7f449a0b078c8d732122ceb", "shasum": ""}, "require": {"ext-ldap": "*", "php": ">=7.1.3", "symfony/options-resolver": "^4.2|^5.0"}, "conflict": {"symfony/options-resolver": "<4.2"}, "require-dev": {"symfony/security-core": "^4.4"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Ldap\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a LDAP client for PHP on top of PHP's ldap extension", "homepage": "https://symfony.com", "keywords": ["active directory", "ldap"], "support": {"source": "https://github.com/symfony/ldap/tree/v4.4.44"}, "time": "2022-06-27T13:16:42+00:00"}, {"name": "symfony/mime", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "72748976b0cc82b44fcae1d66c9df7b598b543c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/72748976b0cc82b44fcae1d66c9df7b598b543c4", "reference": "72748976b0cc82b44fcae1d66c9df7b598b543c4", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.16"}, "conflict": {"egulias/email-validator": "~3.0.0", "symfony/mailer": "<4.4"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1", "symfony/dependency-injection": "^3.4|^4.1|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v4.4.44"}, "time": "2022-07-20T09:59:04+00:00"}, {"name": "symfony/monolog-bridge", "version": "v4.4.43", "source": {"type": "git", "url": "https://github.com/symfony/monolog-bridge.git", "reference": "ad09c9980b912e757c4ecd8363cebf3039d1d471"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/monolog-bridge/zipball/ad09c9980b912e757c4ecd8363cebf3039d1d471", "reference": "ad09c9980b912e757c4ecd8363cebf3039d1d471", "shasum": ""}, "require": {"monolog/monolog": "^1.25.1", "php": ">=7.1.3", "symfony/http-kernel": "^4.3", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2"}, "conflict": {"symfony/console": "<3.4", "symfony/http-foundation": "<3.4"}, "require-dev": {"symfony/console": "^3.4|^4.0|^5.0", "symfony/http-client": "^4.4|^5.0", "symfony/security-core": "^3.4|^4.0|^5.0", "symfony/var-dumper": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/console": "For the possibility to show log messages in console commands depending on verbosity settings.", "symfony/http-kernel": "For using the debugging handlers together with the response life cycle of the HTTP kernel.", "symfony/var-dumper": "For using the debugging handlers like the console handler or the log server handler."}, "type": "symfony-bridge", "autoload": {"psr-4": {"Symfony\\Bridge\\Monolog\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides integration for Monolog with various Symfony components", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/monolog-bridge/tree/v4.4.43"}, "time": "2022-06-16T12:12:11+00:00"}, {"name": "symfony/monolog-bundle", "version": "v3.8.0", "source": {"type": "git", "url": "https://github.com/symfony/monolog-bundle.git", "reference": "a41bbcdc1105603b6d73a7d9a43a3788f8e0fb7d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/monolog-bundle/zipball/a41bbcdc1105603b6d73a7d9a43a3788f8e0fb7d", "reference": "a41bbcdc1105603b6d73a7d9a43a3788f8e0fb7d", "shasum": ""}, "require": {"monolog/monolog": "^1.22 || ^2.0 || ^3.0", "php": ">=7.1.3", "symfony/config": "~4.4 || ^5.0 || ^6.0", "symfony/dependency-injection": "^4.4 || ^5.0 || ^6.0", "symfony/http-kernel": "~4.4 || ^5.0 || ^6.0", "symfony/monolog-bridge": "~4.4 || ^5.0 || ^6.0"}, "require-dev": {"symfony/console": "~4.4 || ^5.0 || ^6.0", "symfony/phpunit-bridge": "^5.2 || ^6.0", "symfony/yaml": "~4.4 || ^5.0 || ^6.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\MonologBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony MonologBundle", "homepage": "https://symfony.com", "keywords": ["log", "logging"], "support": {"issues": "https://github.com/symfony/monolog-bundle/issues", "source": "https://github.com/symfony/monolog-bundle/tree/v3.8.0"}, "time": "2022-05-10T14:24:36+00:00"}, {"name": "symfony/options-resolver", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "583f56160f716dd435f1cd721fd14b548f4bb510"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/583f56160f716dd435f1cd721fd14b548f4bb510", "reference": "583f56160f716dd435f1cd721fd14b548f4bb510", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an improved replacement for the array_replace PHP function", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "support": {"source": "https://github.com/symfony/options-resolver/tree/v4.4.44"}, "time": "2022-07-20T09:59:04+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "6fd1b9a79f6e3cf65f9e679b23af304cd9e010d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/6fd1b9a79f6e3cf65f9e679b23af304cd9e010d4", "reference": "6fd1b9a79f6e3cf65f9e679b23af304cd9e010d4", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.26.0"}, "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/polyfill-iconv", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-iconv.git", "reference": "143f1881e655bebca1312722af8068de235ae5dc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-iconv/zipball/143f1881e655bebca1312722af8068de235ae5dc", "reference": "143f1881e655bebca1312722af8068de235ae5dc", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-iconv": "*"}, "suggest": {"ext-iconv": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Iconv\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Iconv extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "iconv", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-iconv/tree/v1.26.0"}, "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/polyfill-intl-icu", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-icu.git", "reference": "e407643d610e5f2c8a4b14189150f68934bf5e48"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-icu/zipball/e407643d610e5f2c8a4b14189150f68934bf5e48", "reference": "e407643d610e5f2c8a4b14189150f68934bf5e48", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance and support of other locales than \"en\""}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Icu\\": ""}, "classmap": ["Resources/stubs"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's ICU-related data and classes", "homepage": "https://symfony.com", "keywords": ["compatibility", "icu", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-icu/tree/v1.26.0"}, "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "59a8d271f00dd0e4c2e518104cc7963f655a1aa8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/59a8d271f00dd0e4c2e518104cc7963f655a1aa8", "reference": "59a8d271f00dd0e4c2e518104cc7963f655a1aa8", "shasum": ""}, "require": {"php": ">=7.1", "symfony/polyfill-intl-normalizer": "^1.10", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.26.0"}, "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "219aa369ceff116e673852dce47c3a41794c14bd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/219aa369ceff116e673852dce47c3a41794c14bd", "reference": "219aa369ceff116e673852dce47c3a41794c14bd", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.26.0"}, "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "9344f9cb97f3b19424af1a21a3b0e75b0a7d8d7e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/9344f9cb97f3b19424af1a21a3b0e75b0a7d8d7e", "reference": "9344f9cb97f3b19424af1a21a3b0e75b0a7d8d7e", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.26.0"}, "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "bf44a9fd41feaac72b074de600314a93e2ae78e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/bf44a9fd41feaac72b074de600314a93e2ae78e2", "reference": "bf44a9fd41feaac72b074de600314a93e2ae78e2", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php72\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php72/tree/v1.26.0"}, "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "e440d35fa0286f77fb45b79a03fedbeda9307e85"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/e440d35fa0286f77fb45b79a03fedbeda9307e85", "reference": "e440d35fa0286f77fb45b79a03fedbeda9307e85", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.26.0"}, "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "cfa0ae98841b9e461207c13ab093d76b0fa7bace"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/cfa0ae98841b9e461207c13ab093d76b0fa7bace", "reference": "cfa0ae98841b9e461207c13ab093d76b0fa7bace", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.26.0"}, "time": "2022-05-10T07:21:04+00:00"}, {"name": "symfony/polyfill-php81", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php81.git", "reference": "13f6d1271c663dc5ae9fb843a8f16521db7687a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php81/zipball/13f6d1271c663dc5ae9fb843a8f16521db7687a1", "reference": "13f6d1271c663dc5ae9fb843a8f16521db7687a1", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php81\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php81/tree/v1.26.0"}, "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/process", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "5cee9cdc4f7805e2699d9fd66991a0e6df8252a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/5cee9cdc4f7805e2699d9fd66991a0e6df8252a2", "reference": "5cee9cdc4f7805e2699d9fd66991a0e6df8252a2", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v4.4.44"}, "time": "2022-06-27T13:16:42+00:00"}, {"name": "symfony/property-access", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/property-access.git", "reference": "d49682f6f0764df725c95128213a38f7e0a9f358"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-access/zipball/d49682f6f0764df725c95128213a38f7e0a9f358", "reference": "d49682f6f0764df725c95128213a38f7e0a9f358", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/inflector": "^3.4|^4.0|^5.0", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"symfony/cache": "^3.4|^4.0|^5.0"}, "suggest": {"psr/cache-implementation": "To cache access methods."}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyAccess\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides functions to read and write from/to an object or array using a simple string notation", "homepage": "https://symfony.com", "keywords": ["access", "array", "extraction", "index", "injection", "object", "property", "property path", "reflection"], "support": {"source": "https://github.com/symfony/property-access/tree/v4.4.44"}, "time": "2022-06-27T13:16:42+00:00"}, {"name": "symfony/property-info", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/property-info.git", "reference": "442d69db54b0549ef7478a85275bea603230070f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-info/zipball/442d69db54b0549ef7478a85275bea603230070f", "reference": "442d69db54b0549ef7478a85275bea603230070f", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/inflector": "^3.4|^4.0|^5.0", "symfony/polyfill-php80": "^1.16"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.0|>=3.2.0,<3.2.2", "phpdocumentor/type-resolver": "<0.3.0|1.3.*", "symfony/dependency-injection": "<3.4"}, "require-dev": {"doctrine/annotations": "^1.10.4", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/cache": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/serializer": "^3.4|^4.0|^5.0"}, "suggest": {"phpdocumentor/reflection-docblock": "To use the PHPDoc", "psr/cache-implementation": "To cache results", "symfony/doctrine-bridge": "To use Doctrine metadata", "symfony/serializer": "To use Serializer metadata"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyInfo\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Extracts information about PHP class' properties using metadata of popular sources", "homepage": "https://symfony.com", "keywords": ["doctrine", "phpdoc", "property", "symfony", "type", "validator"], "support": {"source": "https://github.com/symfony/property-info/tree/v4.4.44"}, "time": "2022-07-18T08:30:40+00:00"}, {"name": "symfony/routing", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "f7751fd8b60a07f3f349947a309b5bdfce22d6ae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/f7751fd8b60a07f3f349947a309b5bdfce22d6ae", "reference": "f7751fd8b60a07f3f349947a309b5bdfce22d6ae", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/config": "<4.2", "symfony/dependency-injection": "<3.4", "symfony/yaml": "<3.4"}, "require-dev": {"doctrine/annotations": "^1.10.4", "psr/log": "^1|^2|^3", "symfony/config": "^4.2|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"doctrine/annotations": "For using the annotation loader", "symfony/config": "For using the all-in-one router or any loader", "symfony/expression-language": "For using expression matching", "symfony/http-foundation": "For using a Symfony Request object", "symfony/yaml": "For using the YAML loader"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Maps an HTTP request to a set of configuration variables", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "support": {"source": "https://github.com/symfony/routing/tree/v4.4.44"}, "time": "2022-07-20T09:59:04+00:00"}, {"name": "symfony/security-bundle", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/security-bundle.git", "reference": "d2a6bf4aeb75e75d3faacf9bec286e0de58394b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-bundle/zipball/d2a6bf4aeb75e75d3faacf9bec286e0de58394b6", "reference": "d2a6bf4aeb75e75d3faacf9bec286e0de58394b6", "shasum": ""}, "require": {"ext-xml": "*", "php": ">=7.1.3", "symfony/config": "^4.2|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/http-kernel": "^4.4", "symfony/polyfill-php80": "^1.16", "symfony/security-core": "^4.4", "symfony/security-csrf": "^4.2|^5.0", "symfony/security-guard": "^4.2|^5.0", "symfony/security-http": "^4.4.5"}, "conflict": {"symfony/browser-kit": "<4.2", "symfony/console": "<3.4", "symfony/framework-bundle": "<4.4", "symfony/ldap": "<4.4", "symfony/twig-bundle": "<4.4"}, "require-dev": {"doctrine/annotations": "^1.10.4", "symfony/asset": "^3.4|^4.0|^5.0", "symfony/browser-kit": "^4.2|^5.0", "symfony/console": "^3.4|^4.0|^5.0", "symfony/css-selector": "^3.4|^4.0|^5.0", "symfony/dom-crawler": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/form": "^3.4|^4.0|^5.0", "symfony/framework-bundle": "^4.4|^5.0", "symfony/process": "^3.4|^4.0|^5.0", "symfony/serializer": "^4.4|^5.0", "symfony/translation": "^3.4|^4.0|^5.0", "symfony/twig-bridge": "^3.4|^4.0|^5.0", "symfony/twig-bundle": "^4.4|^5.0", "symfony/validator": "^3.4|^4.0|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0", "twig/twig": "^1.43|^2.13|^3.0.4"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\SecurityBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a tight integration of the Security component into the Symfony full-stack framework", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security-bundle/tree/v4.4.44"}, "time": "2022-07-20T09:59:04+00:00"}, {"name": "symfony/security-core", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/security-core.git", "reference": "e9a969b5e391d0983d7766503e22b481cb4c7338"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-core/zipball/e9a969b5e391d0983d7766503e22b481cb4c7338", "reference": "e9a969b5e391d0983d7766503e22b481cb4c7338", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/event-dispatcher-contracts": "^1.1|^2", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1.6|^2"}, "conflict": {"symfony/event-dispatcher": "<4.3|>=5", "symfony/ldap": "<4.4", "symfony/security-guard": "<4.3"}, "require-dev": {"psr/container": "^1.0|^2.0", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^4.3", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/ldap": "^4.4|^5.0", "symfony/translation": "^4.4|^5.0", "symfony/validator": "^3.4.31|^4.3.4|^5.0"}, "suggest": {"psr/container-implementation": "To instantiate the Security class", "symfony/event-dispatcher": "", "symfony/expression-language": "For using the expression voter", "symfony/http-foundation": "", "symfony/ldap": "For using LDAP integration", "symfony/validator": "For using the user password constraint"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Security\\Core\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - Core Library", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security-core/tree/v4.4.44"}, "time": "2022-07-20T09:59:04+00:00"}, {"name": "symfony/security-csrf", "version": "v4.4.37", "source": {"type": "git", "url": "https://github.com/symfony/security-csrf.git", "reference": "45c956ef58135091f53732646a0acd28034f02c0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-csrf/zipball/45c956ef58135091f53732646a0acd28034f02c0", "reference": "45c956ef58135091f53732646a0acd28034f02c0", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-php80": "^1.16", "symfony/security-core": "^3.4|^4.0|^5.0"}, "conflict": {"symfony/http-foundation": "<3.4"}, "require-dev": {"symfony/http-foundation": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/http-foundation": "For using the class SessionTokenStorage."}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Security\\Csrf\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - CSRF Library", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security-csrf/tree/v4.4.37"}, "time": "2022-01-02T09:41:36+00:00"}, {"name": "symfony/security-guard", "version": "v4.4.37", "source": {"type": "git", "url": "https://github.com/symfony/security-guard.git", "reference": "cf8922b164e1659726c8852663eaaa593eef668c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-guard/zipball/cf8922b164e1659726c8852663eaaa593eef668c", "reference": "cf8922b164e1659726c8852663eaaa593eef668c", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/security-core": "^3.4.22|^4.2.3|^5.0", "symfony/security-http": "^4.4.1"}, "require-dev": {"psr/log": "^1|^2|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Security\\Guard\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - Guard", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security-guard/tree/v4.4.37"}, "time": "2022-01-02T09:41:36+00:00"}, {"name": "symfony/security-http", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/security-http.git", "reference": "edb73e18f50f91bfe325e0524b68d9cf99a58f22"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-http/zipball/edb73e18f50f91bfe325e0524b68d9cf99a58f22", "reference": "edb73e18f50f91bfe325e0524b68d9cf99a58f22", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/http-foundation": "^3.4.40|^4.4.7|^5.0.7", "symfony/http-kernel": "^4.4", "symfony/polyfill-php80": "^1.16", "symfony/property-access": "^3.4|^4.0|^5.0", "symfony/security-core": "^4.4.8"}, "conflict": {"symfony/event-dispatcher": ">=5", "symfony/security-csrf": "<3.4.11|~4.0,<4.0.11"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/routing": "^3.4|^4.0|^5.0", "symfony/security-csrf": "^3.4.11|^4.0.11|^5.0"}, "suggest": {"symfony/routing": "For using the HttpUtils class to create sub-requests, redirect the user, and match URLs", "symfony/security-csrf": "For using tokens to protect authentication/logout attempts"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Security\\Http\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - HTTP Integration", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security-http/tree/v4.4.44"}, "time": "2022-07-26T16:38:53+00:00"}, {"name": "symfony/serializer", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/serializer.git", "reference": "375509ca128d3e8b38df92af74814c765571911e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/serializer/zipball/375509ca128d3e8b38df92af74814c765571911e", "reference": "375509ca128d3e8b38df92af74814c765571911e", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-php80": "^1.16"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.0|>=3.2.0,<3.2.2", "phpdocumentor/type-resolver": "<0.3.0|1.3.*", "symfony/dependency-injection": "<3.4", "symfony/property-access": "<3.4", "symfony/property-info": "<3.4", "symfony/yaml": "<3.4"}, "require-dev": {"doctrine/annotations": "^1.10.4", "phpdocumentor/reflection-docblock": "^3.2|^4.0|^5.0", "symfony/cache": "^3.4|^4.0|^5.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/error-handler": "^4.4|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/mime": "^4.4|^5.0", "symfony/property-access": "^4.4.36|^5.3.13", "symfony/property-info": "^3.4.13|~4.0|^5.0", "symfony/validator": "^3.4|^4.0|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"doctrine/annotations": "For using the annotation mapping.", "psr/cache-implementation": "For using the metadata cache.", "symfony/config": "For using the XML mapping loader.", "symfony/http-foundation": "For using a MIME type guesser within the DataUriNormalizer.", "symfony/property-access": "For using the ObjectNormalizer.", "symfony/property-info": "To deserialize relations.", "symfony/yaml": "For using the default YAML mapping loader."}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Serializer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Handles serializing and deserializing data structures, including object graphs, into array structures or other formats like XML and JSON.", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/serializer/tree/v4.4.44"}, "time": "2022-07-28T12:55:20+00:00"}, {"name": "symfony/service-contracts", "version": "v2.5.2", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "4b426aac47d6427cc1a1d0f7e2ac724627f5966c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/4b426aac47d6427cc1a1d0f7e2ac724627f5966c", "reference": "4b426aac47d6427cc1a1d0f7e2ac724627f5966c", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/container": "^1.1", "symfony/deprecation-contracts": "^2.1|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "suggest": {"symfony/service-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v2.5.2"}, "time": "2022-05-30T19:17:29+00:00"}, {"name": "symfony/stopwatch", "version": "v4.4.38", "source": {"type": "git", "url": "https://github.com/symfony/stopwatch.git", "reference": "7f4f5a8122f7530d688cc9edf2f8c9261552fa2d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/stopwatch/zipball/7f4f5a8122f7530d688cc9edf2f8c9261552fa2d", "reference": "7f4f5a8122f7530d688cc9edf2f8c9261552fa2d", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/service-contracts": "^1.0|^2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Stopwatch\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a way to profile code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/stopwatch/tree/v4.4.38"}, "time": "2022-02-18T15:34:20+00:00"}, {"name": "symfony/swiftmailer-bundle", "version": "v3.5.4", "source": {"type": "git", "url": "https://github.com/symfony/swiftmailer-bundle.git", "reference": "9daab339f226ac958192bf89836cb3378cc0e652"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/swiftmailer-bundle/zipball/9daab339f226ac958192bf89836cb3378cc0e652", "reference": "9daab339f226ac958192bf89836cb3378cc0e652", "shasum": ""}, "require": {"php": ">=7.1", "swiftmailer/swiftmailer": "^6.1.3", "symfony/config": "^4.4|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/http-kernel": "^4.4|^5.0"}, "conflict": {"twig/twig": "<1.41|>=2.0,<2.10"}, "require-dev": {"symfony/console": "^4.4|^5.0", "symfony/framework-bundle": "^4.4|^5.0", "symfony/phpunit-bridge": "^4.4|^5.0", "symfony/yaml": "^4.4|^5.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-main": "3.5-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\SwiftmailerBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "http://symfony.com/contributors"}], "description": "Symfony SwiftmailerBundle", "homepage": "http://symfony.com", "support": {"issues": "https://github.com/symfony/swiftmailer-bundle/issues", "source": "https://github.com/symfony/swiftmailer-bundle/tree/v3.5.4"}, "abandoned": "symfony/mailer", "time": "2022-02-06T08:03:40+00:00"}, {"name": "symfony/translation", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "af947fefc306cec6ea5a1f6160c7e305a71f2493"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/af947fefc306cec6ea5a1f6160c7e305a71f2493", "reference": "af947fefc306cec6ea5a1f6160c7e305a71f2493", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation-contracts": "^1.1.6|^2"}, "conflict": {"symfony/config": "<3.4", "symfony/dependency-injection": "<3.4", "symfony/http-kernel": "<4.4", "symfony/yaml": "<3.4"}, "provide": {"symfony/translation-implementation": "1.0|2.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^3.4|^4.0|^5.0", "symfony/console": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/finder": "~2.8|~3.0|~4.0|^5.0", "symfony/http-kernel": "^4.4", "symfony/intl": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1.2|^2", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v4.4.44"}, "time": "2022-07-20T09:59:04+00:00"}, {"name": "symfony/translation-contracts", "version": "v2.5.2", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "136b19dd05cdf0709db6537d058bcab6dd6e2dbe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/136b19dd05cdf0709db6537d058bcab6dd6e2dbe", "reference": "136b19dd05cdf0709db6537d058bcab6dd6e2dbe", "shasum": ""}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/translation-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v2.5.2"}, "time": "2022-06-27T16:58:25+00:00"}, {"name": "symfony/twig-bridge", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/twig-bridge.git", "reference": "53e4f5ed93901d857ec07e2440cc113537c1a489"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/twig-bridge/zipball/53e4f5ed93901d857ec07e2440cc113537c1a489", "reference": "53e4f5ed93901d857ec07e2440cc113537c1a489", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-php80": "^1.16", "symfony/translation-contracts": "^1.1|^2", "twig/twig": "^1.43|^2.13|^3.0.4"}, "conflict": {"symfony/console": "<3.4", "symfony/form": "<4.4", "symfony/http-foundation": "<4.3", "symfony/translation": "<4.2", "symfony/workflow": "<4.3"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3", "symfony/asset": "^3.4|^4.0|^5.0", "symfony/console": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/error-handler": "^4.4|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/finder": "^3.4|^4.0|^5.0", "symfony/form": "^4.4.17", "symfony/http-foundation": "^4.3|^5.0", "symfony/http-kernel": "^4.4", "symfony/intl": "^4.4|^5.0", "symfony/mime": "^4.3|^5.0", "symfony/polyfill-intl-icu": "~1.0", "symfony/routing": "^3.4|^4.0|^5.0", "symfony/security-acl": "^2.8|^3.0", "symfony/security-core": "^3.0|^4.0|^5.0", "symfony/security-csrf": "^3.4|^4.0|^5.0", "symfony/security-http": "^3.4|^4.0|^5.0", "symfony/stopwatch": "^3.4|^4.0|^5.0", "symfony/templating": "^3.4|^4.0|^5.0", "symfony/translation": "^4.2.1|^5.0", "symfony/web-link": "^4.4|^5.0", "symfony/workflow": "^4.3|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0", "twig/cssinliner-extra": "^2.12|^3", "twig/inky-extra": "^2.12|^3", "twig/markdown-extra": "^2.12|^3"}, "suggest": {"symfony/asset": "For using the AssetExtension", "symfony/expression-language": "For using the ExpressionExtension", "symfony/finder": "", "symfony/form": "For using the FormExtension", "symfony/http-kernel": "For using the HttpKernelExtension", "symfony/routing": "For using the RoutingExtension", "symfony/security-core": "For using the SecurityExtension", "symfony/security-csrf": "For using the CsrfExtension", "symfony/security-http": "For using the LogoutUrlExtension", "symfony/stopwatch": "For using the StopwatchExtension", "symfony/templating": "For using the TwigEngine", "symfony/translation": "For using the TranslationExtension", "symfony/var-dumper": "For using the DumpExtension", "symfony/web-link": "For using the WebLinkExtension", "symfony/yaml": "For using the YamlExtension"}, "type": "symfony-bridge", "autoload": {"psr-4": {"Symfony\\Bridge\\Twig\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides integration for Twig with various Symfony components", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/twig-bridge/tree/v4.4.44"}, "time": "2022-07-20T09:59:04+00:00"}, {"name": "symfony/twig-bundle", "version": "v4.4.41", "source": {"type": "git", "url": "https://github.com/symfony/twig-bundle.git", "reference": "164c1edc69f2c7ee337323efc78a8a8a263f45ff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/twig-bundle/zipball/164c1edc69f2c7ee337323efc78a8a8a263f45ff", "reference": "164c1edc69f2c7ee337323efc78a8a8a263f45ff", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/http-foundation": "^4.3|^5.0", "symfony/http-kernel": "^4.4", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-php80": "^1.16", "symfony/twig-bridge": "^4.4|^5.0", "twig/twig": "^1.43|^2.13|^3.0.4"}, "conflict": {"symfony/dependency-injection": "<4.1", "symfony/framework-bundle": "<4.4", "symfony/translation": "<4.2"}, "require-dev": {"doctrine/annotations": "^1.10.4", "doctrine/cache": "^1.0|^2.0", "symfony/asset": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^4.2.5|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/finder": "^3.4|^4.0|^5.0", "symfony/form": "^3.4|^4.0|^5.0", "symfony/framework-bundle": "^4.4|^5.0", "symfony/routing": "^3.4|^4.0|^5.0", "symfony/stopwatch": "^3.4|^4.0|^5.0", "symfony/templating": "^3.4|^4.0|^5.0", "symfony/translation": "^4.2|^5.0", "symfony/web-link": "^3.4|^4.0|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\TwigBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a tight integration of Twig into the Symfony full-stack framework", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/twig-bundle/tree/v4.4.41"}, "time": "2022-04-12T15:19:55+00:00"}, {"name": "symfony/validator", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/validator.git", "reference": "4b566c8d15f3490b0e72b42dd33ea8d2b4857cb1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/validator/zipball/4b566c8d15f3490b0e72b42dd33ea8d2b4857cb1", "reference": "4b566c8d15f3490b0e72b42dd33ea8d2b4857cb1", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16", "symfony/translation-contracts": "^1.1|^2"}, "conflict": {"doctrine/lexer": "<1.1", "phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/dependency-injection": "<3.4", "symfony/http-kernel": "<4.4", "symfony/intl": "<4.3", "symfony/translation": ">=5.0", "symfony/yaml": "<3.4"}, "require-dev": {"doctrine/annotations": "^1.10.4", "doctrine/cache": "^1.0|^2.0", "egulias/email-validator": "^2.1.10|^3", "symfony/cache": "^3.4|^4.0|^5.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/http-client": "^4.3|^5.0", "symfony/http-foundation": "^4.1|^5.0", "symfony/http-kernel": "^4.4", "symfony/intl": "^4.3|^5.0", "symfony/mime": "^4.4|^5.0", "symfony/property-access": "^3.4|^4.0|^5.0", "symfony/property-info": "^3.4|^4.0|^5.0", "symfony/translation": "^4.2", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"doctrine/annotations": "For using the annotation mapping. You will also need doctrine/cache.", "doctrine/cache": "For using the default cached annotation reader.", "egulias/email-validator": "Strict (RFC compliant) email validation", "psr/cache-implementation": "For using the mapping cache.", "symfony/config": "", "symfony/expression-language": "For using the Expression validator", "symfony/http-foundation": "", "symfony/intl": "", "symfony/property-access": "For accessing properties within comparison constraints", "symfony/property-info": "To automatically add NotNull and Type constraints", "symfony/translation": "For translating validation errors.", "symfony/yaml": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Validator\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to validate values", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/validator/tree/v4.4.44"}, "time": "2022-07-20T09:59:04+00:00"}, {"name": "symfony/var-dumper", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "f19951007dae942cc79b979c1fe26bfdfbeb54ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/f19951007dae942cc79b979c1fe26bfdfbeb54ed", "reference": "f19951007dae942cc79b979c1fe26bfdfbeb54ed", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php72": "~1.5", "symfony/polyfill-php80": "^1.16"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/console": "<3.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^3.4|^4.0|^5.0", "symfony/process": "^4.4|^5.0", "twig/twig": "^1.43|^2.13|^3.0.4"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v4.4.44"}, "time": "2022-07-20T09:59:04+00:00"}, {"name": "symfony/var-exporter", "version": "v4.4.43", "source": {"type": "git", "url": "https://github.com/symfony/var-exporter.git", "reference": "4a7a3a3d55c471d396e6d28011368b7b83cb518b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-exporter/zipball/4a7a3a3d55c471d396e6d28011368b7b83cb518b", "reference": "4a7a3a3d55c471d396e6d28011368b7b83cb518b", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-php80": "^1.16"}, "require-dev": {"symfony/var-dumper": "^4.4.9|^5.0.9"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\VarExporter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows exporting any serializable PHP data structure to plain PHP code", "homepage": "https://symfony.com", "keywords": ["clone", "construct", "export", "hydrate", "instantiate", "serialize"], "support": {"source": "https://github.com/symfony/var-exporter/tree/v4.4.43"}, "time": "2022-05-27T11:44:32+00:00"}, {"name": "symfony/web-profiler-bundle", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/web-profiler-bundle.git", "reference": "346128217ae50ac62d32b2d42257b90598dc9fc2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/web-profiler-bundle/zipball/346128217ae50ac62d32b2d42257b90598dc9fc2", "reference": "346128217ae50ac62d32b2d42257b90598dc9fc2", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/config": "^4.2|^5.0", "symfony/framework-bundle": "^4.4|^5.0", "symfony/http-kernel": "^4.4", "symfony/polyfill-php80": "^1.16", "symfony/routing": "^4.3|^5.0", "symfony/twig-bundle": "^4.2|^5.0", "twig/twig": "^1.43|^2.13|^3.0.4"}, "conflict": {"symfony/form": "<4.3", "symfony/messenger": "<4.2"}, "require-dev": {"symfony/browser-kit": "^4.3|^5.0", "symfony/console": "^4.3|^5.0", "symfony/css-selector": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/stopwatch": "^3.4|^4.0|^5.0"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\WebProfilerBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a development tool that gives detailed information about the execution of any request", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/web-profiler-bundle/tree/v4.4.44"}, "time": "2022-07-20T09:59:04+00:00"}, {"name": "symfony/webpack-encore-bundle", "version": "v1.15.1", "source": {"type": "git", "url": "https://github.com/symfony/webpack-encore-bundle.git", "reference": "718673b1e758533614190ae74d07305a72bc66a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/webpack-encore-bundle/zipball/718673b1e758533614190ae74d07305a72bc66a9", "reference": "718673b1e758533614190ae74d07305a72bc66a9", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/asset": "^4.4 || ^5.0 || ^6.0", "symfony/config": "^4.4 || ^5.0 || ^6.0", "symfony/dependency-injection": "^4.4 || ^5.0 || ^6.0", "symfony/deprecation-contracts": "^2.1 || ^3.0", "symfony/http-kernel": "^4.4 || ^5.0 || ^6.0", "symfony/polyfill-php80": "^1.25.0", "symfony/service-contracts": "^1.0 || ^2.0 || ^3.0"}, "require-dev": {"symfony/framework-bundle": "^4.4 || ^5.0 || ^6.0", "symfony/phpunit-bridge": "^5.3 || ^6.0", "symfony/twig-bundle": "^4.4 || ^5.0 || ^6.0", "symfony/web-link": "^4.4 || ^5.0 || ^6.0"}, "type": "symfony-bundle", "extra": {"thanks": {"name": "symfony/webpack-encore", "url": "https://github.com/symfony/webpack-encore"}}, "autoload": {"psr-4": {"Symfony\\WebpackEncoreBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Integration with your Symfony app & Webpack Encore!", "support": {"issues": "https://github.com/symfony/webpack-encore-bundle/issues", "source": "https://github.com/symfony/webpack-encore-bundle/tree/v1.15.1"}, "time": "2022-07-13T17:07:35+00:00"}, {"name": "symfony/workflow", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/workflow.git", "reference": "19ab88556cf9d09d08a376bbb6152a4f8f40a449"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/workflow/zipball/19ab88556cf9d09d08a376bbb6152a4f8f40a449", "reference": "19ab88556cf9d09d08a376bbb6152a4f8f40a449", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/property-access": "^3.4|^4.3|^5.0"}, "conflict": {"symfony/event-dispatcher": "<4.3|>=5", "symfony/security-core": ">=5"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/event-dispatcher": "^4.3", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/security-core": "^3.4|^4.0", "symfony/validator": "^3.4|^4.0|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Workflow\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools for managing a workflow or finite state machine", "homepage": "https://symfony.com", "keywords": ["petrinet", "place", "state", "statemachine", "transition", "workflow"], "support": {"source": "https://github.com/symfony/workflow/tree/v4.4.44"}, "time": "2022-07-20T09:59:04+00:00"}, {"name": "symfony/yaml", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "c2b28c10fb3b7ac67bafa7b8f952cd83f35acde2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/c2b28c10fb3b7ac67bafa7b8f952cd83f35acde2", "reference": "c2b28c10fb3b7ac67bafa7b8f952cd83f35acde2", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/console": "<3.4"}, "require-dev": {"symfony/console": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v4.4.44"}, "time": "2022-06-27T13:16:42+00:00"}, {"name": "tecnickcom/tcpdf", "version": "6.5.0", "source": {"type": "git", "url": "https://github.com/tecnickcom/TCPDF.git", "reference": "cc54c1503685e618b23922f53635f46e87653662"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tecnickcom/TCPDF/zipball/cc54c1503685e618b23922f53635f46e87653662", "reference": "cc54c1503685e618b23922f53635f46e87653662", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "autoload": {"classmap": ["config", "include", "tcpdf.php", "tcpdf_parser.php", "tcpdf_import.php", "tcpdf_barcodes_1d.php", "tcpdf_barcodes_2d.php", "include/tcpdf_colors.php", "include/tcpdf_filters.php", "include/tcpdf_font_data.php", "include/tcpdf_fonts.php", "include/tcpdf_images.php", "include/tcpdf_static.php", "include/barcodes/datamatrix.php", "include/barcodes/pdf417.php", "include/barcodes/qrcode.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-only"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "TCPDF is a PHP class for generating PDF documents and barcodes.", "homepage": "http://www.tcpdf.org/", "keywords": ["PDFD32000-2008", "TCPDF", "barcodes", "datamatrix", "pdf", "pdf417", "qrcode"], "support": {"issues": "https://github.com/tecnickcom/TCPDF/issues", "source": "https://github.com/tecnickcom/TCPDF/tree/6.5.0"}, "time": "2022-08-12T07:50:54+00:00"}, {"name": "twig/extensions", "version": "v1.5.4", "source": {"type": "git", "url": "https://github.com/twigphp/Twig-extensions.git", "reference": "57873c8b0c1be51caa47df2cdb824490beb16202"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig-extensions/zipball/57873c8b0c1be51caa47df2cdb824490beb16202", "reference": "57873c8b0c1be51caa47df2cdb824490beb16202", "shasum": ""}, "require": {"twig/twig": "^1.27|^2.0"}, "require-dev": {"symfony/phpunit-bridge": "^3.4", "symfony/translation": "^2.7|^3.4"}, "suggest": {"symfony/translation": "Allow the time_diff output to be translated"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"psr-0": {"Twig_Extensions_": "lib/"}, "psr-4": {"Twig\\Extensions\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common additional features for Twig that do not directly belong in core", "keywords": ["i18n", "text"], "abandoned": true, "time": "2018-12-05T18:34:18+00:00"}, {"name": "twig/twig", "version": "v2.15.2", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "3e43405a9a8b578809426339cc3780e16fba0c52"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/3e43405a9a8b578809426339cc3780e16fba0c52", "reference": "3e43405a9a8b578809426339cc3780e16fba0c52", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3", "symfony/polyfill-php72": "^1.8"}, "require-dev": {"psr/container": "^1.0", "symfony/phpunit-bridge": "^4.4.9|^5.0.9|^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.15-dev"}}, "autoload": {"psr-0": {"Twig_": "lib/"}, "psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "support": {"issues": "https://github.com/twigphp/Twig/issues", "source": "https://github.com/twigphp/Twig/tree/v2.15.2"}, "time": "2022-08-12T06:43:37+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "time": "2022-06-03T18:03:27+00:00"}], "packages-dev": [{"name": "myclabs/deep-copy", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "14daed4296fae74d9e3201d2c4925d1acb7aa614"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/14daed4296fae74d9e3201d2c4925d1acb7aa614", "reference": "14daed4296fae74d9e3201d2c4925d1acb7aa614", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3,<3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.11.0"}, "time": "2022-03-03T13:19:32+00:00"}, {"name": "nikic/php-parser", "version": "v4.14.0", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "34bea19b6e03d8153165d8f30bba4c3be86184c1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/34bea19b6e03d8153165d8f30bba4c3be86184c1", "reference": "34bea19b6e03d8153165d8f30bba4c3be86184c1", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.0"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^6.5 || ^7.0 || ^8.0 || ^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v4.14.0"}, "time": "2022-05-31T20:59:12+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "1de8cd5c010cb153fcd68b8d0f64606f523f7619"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/1de8cd5c010cb153fcd68b8d0f64606f523f7619", "reference": "1de8cd5c010cb153fcd68b8d0f64606f523f7619", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/tree/1.0.2"}, "time": "2020-11-30T08:15:22+00:00"}, {"name": "sebastian/comparator", "version": "2.1.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "****************************************"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/****************************************", "reference": "****************************************", "shasum": ""}, "require": {"php": "^7.0", "sebastian/diff": "^2.0 || ^3.0", "sebastian/exporter": "^3.1"}, "require-dev": {"phpunit/phpunit": "^6.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "time": "2018-02-01T13:46:46+00:00"}, {"name": "sebastian/diff", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "347c1d8b49c5c3ee30c7040ea6fc446790e6bddd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/diff/zipball/347c1d8b49c5c3ee30c7040ea6fc446790e6bddd", "reference": "347c1d8b49c5c3ee30c7040ea6fc446790e6bddd", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff"], "time": "2017-08-03T08:09:46+00:00"}, {"name": "sebastian/environment", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "cd0871b3975fb7fc44d11314fd1ee20925fce4f5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/cd0871b3975fb7fc44d11314fd1ee20925fce4f5", "reference": "cd0871b3975fb7fc44d11314fd1ee20925fce4f5", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "time": "2017-07-01T08:51:00+00:00"}, {"name": "sebastian/exporter", "version": "3.1.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "0c32ea2e40dbf59de29f3b49bf375176ce7dd8db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/0c32ea2e40dbf59de29f3b49bf375176ce7dd8db", "reference": "0c32ea2e40dbf59de29f3b49bf375176ce7dd8db", "shasum": ""}, "require": {"php": ">=7.0", "sebastian/recursion-context": "^3.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "http://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "source": "https://github.com/sebastian<PERSON>mann/exporter/tree/3.1.4"}, "time": "2021-11-11T13:51:24+00:00"}, {"name": "sebastian/global-state", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "e8ba02eed7bbbb9e59e43dedd3dddeff4a56b0c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/global-state/zipball/e8ba02eed7bbbb9e59e43dedd3dddeff4a56b0c4", "reference": "e8ba02eed7bbbb9e59e43dedd3dddeff4a56b0c4", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "time": "2017-04-27T15:39:26+00:00"}, {"name": "sebastian/object-enumerator", "version": "3.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "e67f6d32ebd0c749cf9d1dbd9f226c727043cdf2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/object-enumerator/zipball/e67f6d32ebd0c749cf9d1dbd9f226c727043cdf2", "reference": "e67f6d32ebd0c749cf9d1dbd9f226c727043cdf2", "shasum": ""}, "require": {"php": ">=7.0", "sebastian/object-reflector": "^1.1.1", "sebastian/recursion-context": "^3.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/3.0.4"}, "time": "2020-11-30T07:40:27+00:00"}, {"name": "sebastian/object-reflector", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "9b8772b9cbd456ab45d4a598d2dd1a1bced6363d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/9b8772b9cbd456ab45d4a598d2dd1a1bced6363d", "reference": "9b8772b9cbd456ab45d4a598d2dd1a1bced6363d", "shasum": ""}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/1.1.2"}, "time": "2020-11-30T07:37:18+00:00"}, {"name": "sebastian/recursion-context", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "367dcba38d6e1977be014dc4b22f47a484dac7fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/367dcba38d6e1977be014dc4b22f47a484dac7fb", "reference": "367dcba38d6e1977be014dc4b22f47a484dac7fb", "shasum": ""}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/3.0.1"}, "time": "2020-11-30T07:34:24+00:00"}, {"name": "sebastian/resource-operations", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "ce990bb21759f94aeafd30209e8cfcdfa8bc3f52"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/resource-operations/zipball/ce990bb21759f94aeafd30209e8cfcdfa8bc3f52", "reference": "ce990bb21759f94aeafd30209e8cfcdfa8bc3f52", "shasum": ""}, "require": {"php": ">=5.6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "time": "2015-07-28T20:34:47+00:00"}, {"name": "sebastian/version", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/99732be0ddb3361e16ad77b68ba41efc8e979019", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019", "shasum": ""}, "require": {"php": ">=5.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "time": "2016-10-03T07:35:21+00:00"}, {"name": "symfony/dotenv", "version": "v4.4.37", "source": {"type": "git", "url": "https://github.com/symfony/dotenv.git", "reference": "fcedd6d382b3afc3e1e786aa4e4fc4cf06f564cf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dotenv/zipball/fcedd6d382b3afc3e1e786aa4e4fc4cf06f564cf", "reference": "fcedd6d382b3afc3e1e786aa4e4fc4cf06f564cf", "shasum": ""}, "require": {"php": ">=7.1.3"}, "require-dev": {"symfony/process": "^3.4.2|^4.0|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Dotenv\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Registers environment variables from a .env file", "homepage": "https://symfony.com", "keywords": ["dotenv", "env", "environment"], "support": {"source": "https://github.com/symfony/dotenv/tree/v4.4.37"}, "time": "2022-01-02T09:41:36+00:00"}, {"name": "symfony/flex", "version": "v1.19.3", "source": {"type": "git", "url": "https://github.com/symfony/flex.git", "reference": "ab0453b16029e131c112df1a76e59eb2a47e1f67"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/flex/zipball/ab0453b16029e131c112df1a76e59eb2a47e1f67", "reference": "ab0453b16029e131c112df1a76e59eb2a47e1f67", "shasum": ""}, "require": {"composer-plugin-api": "^1.0|^2.0", "php": ">=7.1"}, "require-dev": {"composer/composer": "^1.0.2|^2.0", "symfony/dotenv": "^4.4|^5.0|^6.0", "symfony/filesystem": "^4.4|^5.0|^6.0", "symfony/phpunit-bridge": "^4.4.12|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0"}, "type": "composer-plugin", "extra": {"class": "Symfony\\Flex\\Flex"}, "autoload": {"psr-4": {"Symfony\\Flex\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Composer plugin for Symfony", "support": {"issues": "https://github.com/symfony/flex/issues", "source": "https://github.com/symfony/flex/tree/v1.19.3"}, "time": "2022-08-07T09:39:08+00:00"}, {"name": "symfony/maker-bundle", "version": "v1.39.0", "source": {"type": "git", "url": "https://github.com/symfony/maker-bundle.git", "reference": "f2b99ba44e22a44fcf724affa53b5539b25fde90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/maker-bundle/zipball/f2b99ba44e22a44fcf724affa53b5539b25fde90", "reference": "f2b99ba44e22a44fcf724affa53b5539b25fde90", "shasum": ""}, "require": {"doctrine/inflector": "^1.2|^2.0", "nikic/php-parser": "^4.11", "php": ">=7.1.3", "symfony/config": "^4.4|^5.0|^6.0", "symfony/console": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/deprecation-contracts": "^2.2|^3", "symfony/filesystem": "^4.4|^5.0|^6.0", "symfony/finder": "^4.4|^5.0|^6.0", "symfony/framework-bundle": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^4.4|^5.0|^6.0"}, "require-dev": {"composer/semver": "^3.0", "doctrine/doctrine-bundle": "^1.12.3|^2.0", "doctrine/orm": "^2.3", "symfony/http-client": "^4.4|^5.0|^6.0", "symfony/phpunit-bridge": "^4.4|^5.0|^6.0", "symfony/polyfill-php80": "^1.16.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/security-core": "^4.4|^5.0|^6.0", "symfony/yaml": "^4.4|^5.0|^6.0", "twig/twig": "^2.0|^3.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-main": "1.0-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\MakerBundle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Maker helps you create empty commands, controllers, form classes, tests and more so you can forget about writing boilerplate code.", "homepage": "https://symfony.com/doc/current/bundles/SymfonyMakerBundle/index.html", "keywords": ["code generator", "generator", "scaffold", "scaffolding"], "support": {"issues": "https://github.com/symfony/maker-bundle/issues", "source": "https://github.com/symfony/maker-bundle/tree/v1.39.0"}, "time": "2022-04-21T18:16:11+00:00"}, {"name": "symfony/phpunit-bridge", "version": "v4.1.0", "source": {"type": "git", "url": "https://github.com/symfony/phpunit-bridge.git", "reference": "b3451fc442baaf9169e50491a059ef17582c4424"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/phpunit-bridge/zipball/b3451fc442baaf9169e50491a059ef17582c4424", "reference": "b3451fc442baaf9169e50491a059ef17582c4424", "shasum": ""}, "require": {"php": ">=5.3.3"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0"}, "suggest": {"ext-zip": "Zip support is required when using bin/simple-phpunit", "symfony/debug": "For tracking deprecated interfaces usages at runtime with DebugClassLoader"}, "bin": ["bin/simple-phpunit"], "type": "symfony-bridge", "extra": {"branch-alias": {"dev-master": "4.1-dev"}, "thanks": {"name": "phpunit/phpunit", "url": "https://github.com/sebastian<PERSON>mann/phpunit"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Bridge\\PhpUnit\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony PHPUnit Bridge", "homepage": "https://symfony.com", "time": "2018-05-25T14:55:38+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": {"php": "^7.4.1", "ext-json": "*", "ext-pdo": "*", "ext-simplexml": "*"}, "platform-dev": []}