{% extends 'ABUSApiBundle::email.html.twig' %}

{% block content %}

    {#TODO die Mail noch etwas schöner gestalten#}



    {{ "now"|date()|localizeddate('full', 'long', getLanguage()) }}
    </br>
    </br>

    <a href="{{ url('abus_frame_reset_password', {'subdomain': getSubdomain('portal'), 'domain': getDomain(), 'hash': hash}) }}">{{ 'api/password/reset_your_password'|trans({}, 'frame') }}</a>
    <p>{{ '_ABUSFrameBundle@link_lifespan_msg_part_1'|trans({}, 'frame')  }} {{ link_lifespan }} {{ '_ABUSFrameBundle@link_lifespan_msg_part_2'|trans({}, 'frame') }}</p>
{% endblock %}
