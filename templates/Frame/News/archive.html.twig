{% extends 'Frame/Layout/layout.html.twig' %}

{% block javascripts %}
    {{ parent() }}
{% endblock %}

{% block administration %}
    {{ parent() }}

    {% if is_granted('ROLE_PORTAL_NEWS_ADMIN') %}
        <div class="sidebar-panel">
            <h5 class="sidebar-panel-title">
                {{ ["frame/news"]|join|trans({}, 'across') }}
            </h5>
        </div>

        <div class="content" >
            <a href="{{ path('abus_frame_news') }}" class="btn btn-danger btn-block btn-sm mr5"><i class="fa fa-lock" aria-hidden="true"></i>&nbsp;&nbsp;&nbsp;{{ "frame/news/administration"|trans({}, 'across') }}</a>
        </div>
    {% endif %}
{% endblock %}

{% block content %}

    <news :archive="true" {% if is_granted('ROLE_PORTAL_NEWS_ADMIN') %}:admin="true"{% endif %}></news>

{% endblock %}
