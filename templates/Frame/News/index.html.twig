{% extends 'Frame/Layout/layout.html.twig' %}

{% block javascripts %}
    {{ parent() }}
{% endblock %}

{% block administration %}
    {{ parent() }}

    {% if is_granted('ROLE_PORTAL_NEWS_ADMIN') %}
        <div class="sidebar-panel">
            <h5 class="sidebar-panel-title">
                {{ ["frame/news"]|join|trans({}, 'across') }}
            </h5>
        </div>

        <div class="content" >
            <a href="{{ path('abus_frame_homepage') }}" class="btn btn-danger btn-block btn-sm mr5">{{ "frame/news/leave_adminsitration"|trans({}, 'across') }}</a>
            <hr />
            <modal-button
                    variant="success"
                    css-class="success"
                    title="{{ "frame/news/create"|trans({}, 'across') }}"
                    href="{{ path('abus_frame_news_create') }}"
                    modal-title="{{ "frame/news/create"|trans({}, 'across') }}"
                    modal-message="{{ "frame/news/create_message"|trans({}, 'across') }}"
            ></modal-button>
        </div>
    {% endif %}
{% endblock %}

{% block content %}
    <news :backend="true" {% if is_granted('ROLE_PORTAL_NEWS_ADMIN') %}:admin="true"{% endif %}></news>
{% endblock %}
