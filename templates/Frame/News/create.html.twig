{% extends 'Frame/Layout/layout.html.twig' %}

{% block javascripts %}
    {{ parent() }}
{% endblock %}

{% block administration %}
    {{ parent() }}

    {% if is_granted('ROLE_PORTAL_NEWS_ADMIN') %}
        <div class="sidebar-panel">
            <h5 class="sidebar-panel-title">
                {{ ["frame/news"]|join|trans({}, 'across') }}
            </h5>
        </div>

        <div class="content" >
            <a href="{{ path('abus_frame_news') }}" class="btn btn-info btn-block btn-sm mr5">{{ "frame/news/administration"|trans({}, 'across') }}</a>
        </div>
    {% endif %}
{% endblock %}

{% block content %}

    <news-create {% if edit is defined %}:edit="{{ edit }}"{% endif %}></news-create>

{% endblock %}