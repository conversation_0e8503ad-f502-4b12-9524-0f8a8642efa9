<div class="sidebar-panel">
    <h5 class="sidebar-panel-title">{{ 'frame/userprofile'|trans({}, 'across') }}</h5>
</div>

<div class="user-info">

    <div class="pull-left">{{ user.firstname }} {{ user.lastname }}</div>

    {% if user.avatar %}
        <div class="avatar">
            <img src="{{ asset('dist/images/pixel.gif') }}" data-src="{{ asset(user.avatar) }}" alt="avatar" />
        </div>
    {% endif %}

</div>

<div class="side-nav">
    <ul class="nav">
        <li>
            <a href="{{ path('abus_frame_profile') }}" {% if highlightModule is defined and highlightModule == 'profile' %}class="expand"{% endif %}><i class="fas fa-cog fa-fw" aria-hidden="true"></i><span class="txt">{{ "frame/profile_settings"|trans({}, 'across') }}</span></a>
        </li>
        <li>
            <a href="{{ path('abus_api_change_password') }}" {% if highlightModule is defined and highlightModule == 'changePassword' %}class="expand"{% endif %}><i class="far fa-key fa-fw" aria-hidden="true"></i><span class="txt">{{ "frame/change_password"|trans({}, 'across') }}</span></a>
        </li>
    </ul>
</div>
