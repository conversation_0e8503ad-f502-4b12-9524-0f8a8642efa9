<ul class="nav navbar-nav navbar-right" style="flex-direction: row;">

    {% if is_granted('IS_IMPERSONATOR') %}
        <div class="hint d-none d-sm-block">
            {{ "frame/usermode/no_live_update_of_ad_roles"|trans({}, 'across') }}
        </div>

        <div class="username d-none d-sm-block">
            {{ user.firstName }} {{ user.lastName }}
        </div>

        <li>
            <a id="leaveUserMode" href="?_username=_exit" class="nav-link" data-toggle="tooltip" data-placement="bottom" title="{{ "frame/usermode/leave_usermode"|trans({}, 'across') }}">
                <i class="fa fa-times"></i>
                <span class="sr-only">{{ "frame/usermode/leave_usermode"|trans({}, 'across') }}</span>
            </a>
        </li>
    {% else %}
        <li>
            <a href="{{ path('mainick_keycloak_security_auth_logout') }}" class="nav-link" data-toggle="tooltip" data-placement="bottom" data-trigger="hover" title="{{ "frame/logout"|trans({}, 'across') }}">
                <i class="far fa-power-off fa-fw fa-lg" ></i>
                <span class="sr-only">{{ "frame/logout"|trans({}, 'across') }}</span>
            </a>
        </li>
    {% endif %}

    {% if (hideRightSidebar is not defined or hideRightSidebar == false) and ((openModule is defined and openSubmodule is defined and user.canSeeRightSidebar(openModule, openSubmodule)) or (openModule is defined and user.canSeeRightSidebar(openModule))) %}
        {% set rightSidebar = true %}
    {% elseif (hideRightSidebar is not defined or hideRightSidebar == false) and (user.user.canSwitchToUsermode() or user.user.canSeeActiveUsers()) %}
        {% set rightSidebar = true %}
    {% else %}
        {% set rightSidebar = false %}
    {% endif %}

    {% if rightSidebar %}
        <li class="d-none d-lg-block">
            <a id="toggle-right-sidebar" href="#" class="nav-link" data-toggle="tooltip" data-placement="bottom" data-trigger="hover" title="{{ "frame/toggle_right_sidebar"|trans({}, 'across') }}">
                <i class="far fa-toggle-off fa-lg d-none d-lg-block d-xl-none" data-fa-transform="flip-h"></i> {# Tablet Ansicht Navigation Collapsed #}
                <i class="far fa-toggle-on fa-lg d-none d-xl-block" data-fa-transform="flip-h"></i>
                <span class="sr-only">Toggle right sidebar</span>
            </a>
        </li>
    {% endif %}

</ul>
