{% set content %}
    <div class="sidebar-inner">
        {% if user.user.canSwitchToUsermode %}
            <div class="sidebar-panel">
                <h5 class="sidebar-panel-title">{{ "frame/usermode"|trans({}, 'across') }}</h5>
            </div>

            <div class="content">
                <div class="userModeTypeahead">
                    <user-mode></user-mode>
                </div>
            </div>
        {% endif %}

        {% if user.allowedToSeeActiveUsers %}
            {% if app.request.cookies.get('usersOnlineMinimized') is same as("1") %}
                {% if user.allowedToUseUserMode %}
                    <active-users :minimized="true" :usermode="true"></active-users>
                {% else %}
                    <active-users :minimized="true" :usermode="false"></active-users>
                {% endif %}
            {% else %}
                {% if user.allowedToUseUserMode %}
                    <active-users :minimized="false" :usermode="true"></active-users>
                {% else %}
                    <active-users :minimized="false" :usermode="false"></active-users>
                {% endif %}
            {% endif %}
        {% endif %}

        {% block administration %}
            {% if administration is defined %}
                {{ administration }}
            {% endif %}
        {% endblock %}

        {% block anyUser %}
            {% if anyUser is defined %}
                {{ anyUser }}
            {% endif %}
        {% endblock %}

    </div>
{% endset %}

<aside id="right-sidebar" class="right-sidebar {% if scrollRightSidebar is not defined or scrollRightSidebar == false %} sidebar-fixed {% endif %} d-none d-lg-block d-xl-block">
    {{ content }}
</aside>
