{% extends 'Frame/Layout/base.html.twig' %}

{% block body %}

        <div id="header" class="navbar navbar-expand-lg page-navbar header-fixed {% if is_granted('IS_IMPERSONATOR') %}userMode{% endif %}">
            {% include 'Frame/Layout/Parts/NavBrand.html.twig' %}
            {% include 'Frame/Layout/Parts/NavTopLeft.html.twig' %}
            {% include 'Frame/Layout/Parts/NavTopRight.html.twig' %}
        </div>

            {% include 'Frame/Layout/Parts/SidebarLeft.html.twig' %}

            {% if (hideRightSidebar is not defined or hideRightSidebar == false) and ((openModule is defined and openSubmodule is defined and user.canSeeRightSidebar(openModule, openSubmodule)) or (openModule is defined and user.canSeeRightSidebar(openModule))) %}
                {% set rightSidebar = true %}
            {% elseif (hideRightSidebar is not defined or hideRightSidebar == false) and (user.user.canSwitchToUsermode() or user.user.canSeeActiveUsers()) %}
                {% set rightSidebar = true %}
            {% else %}
                {% set rightSidebar = false %}
            {% endif %}

            {% if rightSidebar %}

                {% set administration %}
                    {% block administration %}{% endblock %}
                {% endset %}

                {% set anyUser %}
                    {% block anyUser %}{% endblock %}
                {% endset %}

                {% embed 'Frame/Layout/Parts/SidebarRight.html.twig' with {administration: administration, anyUser: anyUser} %}{% endembed %}
            {% endif %}

            <div class="page-content sidebar-page {% if rightSidebar %}right-sidebar-page hasRightSidebar{% endif %} clearfix">
                <div class="page-content-wrapper{% if( iframe is defined and iframe == true ) %} iframe{% endif %} {% if( iframeWithBorder is defined and iframeWithBorder == true ) %} iframeWithBorder{% endif %}">
                    <div class="page-content-inner {% if rightSidebar == false %} rsidebar-collapsed{% endif %}">
                            {% if ( showPageHeader is defined and showPageHeader ) or showPageHeader is not defined %}
                                <div id="page-header" class="clearfix">
                                    <div class="page-header">
                                        <h2>{% block headline %}ABUS Portal{% endblock %}</h2>
                                        <span class="txt">{% block headline_description %}Welcome to ABUS Portal{% endblock %}</span>
                                        {% block headline_backbutton %}
                                        <div id="backButton">
                                            <a href="{% block headline_backbutton_link %}{% endblock %}" class="btn btn-primary"><span class="glyphicon glyphicon-chevron-left" aria-hidden="true"></span> {% block headline_backbutton_text %}Zurück{% endblock %}</a>
                                        </div>
                                        {% endblock %}
                                    </div>
                                </div>
                            {% endif %}

                        <div {% block ngContentController %}{% endblock %}>
                            {% block search %}{% endblock %}
                            {% block content %}{% endblock %}
                        </div>

                    </div>
                </div>
            </div>

{% endblock %}
