<div class="panel panel-{{ class }}
    {% if plain is defined and plain == true %}plain{% endif %}
    {% if movable is defined and movable == true %}panelMove{% endif %}
    {% if iconToggle is defined and iconToggle == true %}toggle{% endif %}
    {% if iconRefresh is defined and iconRefresh == true %}panelRefresh{% endif %}
    {% if iconClose is defined and iconClose == true %}panelClose{% endif %}
    {% if closedByDefault is defined and closedByDefault == true %}panel-closed{% endif %}
    {% if marginBottom is defined and marginBottom == false %}noBottomMargin{% endif %}
    "

    {% if ngAttributes.container is defined and ngAttributes.container|length > 0 %}
        {% for ngAttribute in ngAttributes.container %}
            {{ ngAttribute|raw }}
        {% endfor %}
    {% endif %}
>

    {% if heading is defined and heading == true %}
        <div class="panel-heading {% if closedByDefault is defined and closedByDefault == true %}panel-collapsed{% endif %} {% if headingClickable is defined and headingClickable == true %}clickable{% endif %}"
            {% if ngAttributes.heading is defined and ngAttributes.heading|length > 0 %}
                {% for ngAttribute in ngAttributes.heading %}
                    {{ ngAttribute|raw }}
                {% endfor %}
            {% endif %}
        >
            <h4 class="panel-title">
                {% if iconClass is defined and iconClass != false %}
                    <div class="icon"><i class="{{ iconClass }}"></i></div>
                {% endif %}
                {% if topic is defined %}<div>{{ topic|raw }}</div>{% endif %}
            </h4>

            <div class="panel-controls panel-controls-right">
                {% if iconRefresh is defined and iconRefresh == true %}<a href="#" class="panel-refresh"><i class="fa fa-circle-o"></i></a>{% endif %}
                {% if iconToggle is defined and iconToggle == true %}<a href="#" class="toggle panel-minimize"><i class="fa {% if closedByDefault is defined and closedByDefault == true %}fa-angle-down{% else %}fa-angle-up{% endif %}"></i></a>{% endif %}
                {% if iconClose is defined and iconClose == true %}<a href="#" class="panel-close"><i class="fa fa-times"></i></a>{% endif %}
            </div>
        </div>
    {% endif %}

    <div class="panel-body {% if closedByDefault is defined and closedByDefault == true %}collapse{% endif %}">
        {% if content is defined and content != '' %}{{ content|raw }}{% endif %}
    </div>

    {% if footer is defined and footer == true %}
        <div class="panel-footer"
            {% if ngAttributes.footer is defined and ngAttributes.footer|length > 0 %}
                {% for ngAttribute in ngAttributes.footer %}
                    {{ ngAttribute|raw }}
                {% endfor %}
            {% endif %}
        >
            {% if topicFooter is defined %}{{ topicFooter|raw }}{% endif %}
        </div>
    {% endif %}
</div>