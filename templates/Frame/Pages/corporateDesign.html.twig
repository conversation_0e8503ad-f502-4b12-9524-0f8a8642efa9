{% extends 'Frame/Layout/layout.html.twig' %}

{% block javascripts %}
    {{ parent() }}
{% endblock %}

{% block content %}

    <div class="mt10"></div>

    <h3>ABUS Farben - Vorgaben für Webseite von Bunte Brause</h3>

    <div class="row mt10">
        <div class="col-md-2 mb-3">
            <div style="border: #dfdfdf 1px solid;">
                <div class="bg-abus-red" style="height: 50px; width: 100%;"></div>
                <div class="text-center pt5 pb5 bg-white">$abus-red<br />#FF4F00</div>
            </div>
        </div>

        <div class="col-md-2 mb-3">
            <div style="border: #dfdfdf 1px solid;">
                <div class="bg-abus-red-1" style="height: 50px; width: 100%;"></div>
                <div class="text-center pt5 pb5 bg-white">$abus-red-1<br />#D60000</div>
            </div>
        </div>

        <div class="col-md-2">
            <div style="border: #dfdfdf 1px solid;">
                <div class="bg-abus-green" style="height: 50px; width: 100%;"></div>
                <div class="text-center pt5 pb5 bg-white">$abus-green<br />#53C200</div>
            </div>
        </div>

        <div class="col-md-2">
            <div style="border: #dfdfdf 1px solid;">
                <div class="bg-abus-creme" style="height: 50px; width: 100%;"></div>
                <div class="text-center pt5 pb5 bg-white">$abus-creme<br />#FCFBF0</div>
            </div>
        </div>

        <div class="col-md-2">
            <div style="border: #dfdfdf 1px solid;">
                <div class="bg-abus-grey-1" style="height: 50px; width: 100%;"></div>
                <div class="text-center pt5 pb5 bg-white">$abus-grey-1<br />#DAD7CE</div>
            </div>
        </div>

        <div class="col-md-2">
            <div style="border: #dfdfdf 1px solid;">
                <div class="bg-abus-grey-2" style="height: 50px; width: 100%;"></div>
                <div class="text-center pt5 pb5 bg-white">$abus-grey-2<br />#C0BEB5</div>
            </div>
        </div>

        <div class="col-md-2">
            <div style="border: #dfdfdf 1px solid;">
                <div class="bg-abus-grey-3" style="height: 50px; width: 100%;"></div>
                <div class="text-center pt5 pb5 bg-white">$abus-grey-3<br />#A6A59D</div>
            </div>
        </div>

        <div class="col-md-2">
            <div style="border: #dfdfdf 1px solid;">
                <div class="bg-abus-grey-4" style="height: 50px; width: 100%;"></div>
                <div class="text-center pt5 pb5 bg-white">$abus-grey-4<br />#807F79</div>
            </div>
        </div>

        <div class="col-md-2 mb-3">
            <div style="border: #dfdfdf 1px solid;">
                <div class="bg-abus-grey-5" style="height: 50px; width: 100%;"></div>
                <div class="text-center pt5 pb5 bg-white">$abus-grey-5<br />#5A5955</div>
            </div>
        </div>

        <div class="col-md-2">
            <div style="border: #dfdfdf 1px solid;">
                <div class="bg-abus-grey-6" style="height: 50px; width: 100%;"></div>
                <div class="text-center pt5 pb5 bg-white">$abus-grey-6<br />#D403F3C</div>
            </div>
        </div>

        <div class="col-md-2">
            <div style="border: #dfdfdf 1px solid;">
                <div class="bg-abus-grey-7" style="height: 50px; width: 100%;"></div>
                <div class="text-center pt5 pb5 bg-white">$abus-grey-7<br />#262624</div>
            </div>
        </div>

        <div class="col-md-2">
            <div style="border: #dfdfdf 1px solid;">
                <div class="bg-abus-grey-8" style="height: 50px; width: 100%;"></div>
                <div class="text-center pt5 pb5 bg-white">$abus-grey-8<br />#EDEDED</div>
            </div>
        </div>


        <div class="col-md-2">
            <div style="border: #dfdfdf 1px solid;">
                <div class="bg-abus-yellow-light" style="height: 50px; width: 100%;"></div>
                <div class="text-center pt5 pb5 bg-white">$abus-yellow-light<br />#FFC500</div>
            </div>
        </div>

        <div class="col-md-2">
            <div style="border: #dfdfdf 1px solid;">
                <div class="bg-abus-yellow-dark" style="height: 50px; width: 100%;"></div>
                <div class="text-center pt5 pb5 bg-white">$abus-yellow-dark<br />#FFAA00</div>
            </div>
        </div>

        <div class="col-md-2">
            <div style="border: #dfdfdf 1px solid;">
                <div class="bg-abus-blue-light" style="height: 50px; width: 100%;"></div>
                <div class="text-center pt5 pb5 bg-white">$abus-blue-light<br />#005AAA</div>
            </div>
        </div>

        <div class="col-md-2">
            <div style="border: #dfdfdf 1px solid;">
                <div class="bg-abus-blue-dark" style="height: 50px; width: 100%;"></div>
                <div class="text-center pt5 pb5 bg-white">$abus-blue-dark<br />#00467B</div>
            </div>
        </div>

    </div>

    <div class="row mt20">

        <div class="col-md-4">
            <div class="card text-black mb-3">
                <div class="card-header bg-default">
                    <i class="fa fa-star"></i>
                    Card Default with icon</div>
                <div class="card-body bg-white">
                    Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua.
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card mb-3">
                <div class="card-header text-white bg-primary">
                    Card Primary with controls
                    <div class="card-controls card-controls-right">
                        <a href="#" class="toggle card-minimize">
                            <i class="fa-angle-up fa"></i>
                        </a>
                    </div>
                </div>
                <div class="card-body bg-white">
                    Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua.
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card mb-3">
                <div class="card-header bg-secondary text-white">Card Secondary</div>
                <div class="card-body bg-white">
                    Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua.
                </div>
            </div>
        </div>

    </div>

    <div class="row mt10">

        <div class="col-md-4">
            <div class="card mb-3">
                <div class="card-header bg-success text-white">Card Success</div>
                <div class="card-body bg-white">
                    Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua.
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card mb-3">
                <div class="card-header bg-danger text-white">
                    Card Danger
                </div>
                <div class="card-body bg-white text-black">
                    Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua.
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card mb-3">
                <div class="card-header bg-warning text-white">
                    Card Warning
                </div>
                <div class="card-body bg-white text-black">
                    Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua.
                </div>
            </div>
        </div>

    </div>

    <div class="row mt10">

        <div class="col-md-4">
            <div class="card mb-3">
                <div class="card-header bg-info text-white">
                    Card Info
                </div>
                <div class="card-body bg-white text-black">
                    Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua.
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card mb-3">
                <div class="card-header bg-light text-black">
                    Card Light
                </div>
                <div class="card-body bg-white text-black">
                    Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua.
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card mb-3">
                <div class="card-header bg-dark text-white">
                    Card Dark
                </div>
                <div class="card-body bg-white text-black">
                    Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua.
                </div>
            </div>
        </div>

    </div>


    <div class="row mt10">

        <div class="col-md-4">
            <div class="card text-white bg-primary mb-3">
                <div class="card-header">Card Primary</div>
                <div class="card-body">
                    Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua.
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card text-white bg-secondary mb-3">
                <div class="card-header">Card Secondary</div>
                <div class="card-body">
                    Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua.
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card text-white bg-success mb-3">
                <div class="card-header">Card Success</div>
                <div class="card-body">
                    Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua.
                </div>
            </div>
        </div>

    </div>

    <div class="row mt10">

        <div class="col-md-4">
            <div class="card text-white bg-danger mb-3">
                <div class="card-header">Card Danger</div>
                <div class="card-body">
                    Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua.
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card text-white bg-warning mb-3">
                <div class="card-header">Card Warning</div>
                <div class="card-body">
                    Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua.
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card text-white bg-info mb-3">
                <div class="card-header">Card Info</div>
                <div class="card-body">
                    Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua.
                </div>
            </div>
        </div>

    </div>

    <div class="row mt10">

        <div class="col-md-4">
            <div class="card text-black bg-light mb-3">
                <div class="card-header">Card Light</div>
                <div class="card-body">
                    Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua.
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card text-white bg-dark mb-3">
                <div class="card-header">Card Dark</div>
                <div class="card-body">
                    Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua.
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card card-closed mb-3">
                <div class="card-header bg-default text-black">
                    Closed by default
                    <div class="card-controls card-controls-right">
                        <a href="#" class="toggle card-minimize">
                            <i class="fa-angle-up fa"></i>
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua.
                </div>
            </div>
        </div>

    </div>

    <hr />

    <div class="row mt10">

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">Basic buttons</div>
                <div class="card-body">
                    <div class="panel-body">
                        <button type="button" class="btn btn-default mr5 mb10">Default</button>
                        <button type="button" class="btn btn-primary mr5 mb10">Primary</button>
                        <button type="button" class="btn btn-secondary mr5 mb10">Secondary</button>
                        <button type="button" class="btn btn-success mr5 mb10">Success</button>
                        <button type="button" class="btn btn-danger mr5 mb10">Danger</button>
                        <button type="button" class="btn btn-warning mr5 mb10">Warning</button>
                        <button type="button" class="btn btn-info mr5 mb10">Info</button>
                        <button type="button" class="btn btn-light mr5 mb10">Light</button>
                        <button type="button" class="btn btn-dark mr5 mb10">Dark</button>
                        <button type="button" class="btn btn-link mr5 mb10">Link</button>
                        <button type="button" class="btn btn-abus-blue-dark mr5 mb10">ABUS blue dark</button>
                        <button type="button" class="btn btn-abus-blue-light mr5 mb10">ABUS blue Light</button>
                        <button type="button" class="btn btn-abus-yellow-dark mr5 mb10">ABUS yellow dark</button>
                        <button type="button" class="btn btn-abus-yellow-light mr5 mb10">ABUS yellow Light</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">Different button sizes</div>
                <div class="card-body">
                    <div class="panel-body">
                        <button type="button" class="btn btn-default btn-xs mr5 mb10">Default</button>
                        <button type="button" class="btn btn-primary btn-sm mr5 mb10">Primary</button>
                        <button type="button" class="btn btn-danger mr5 mb10">Danger</button>
                        <button type="button" class="btn btn-success btn-lg mr5 mb10">Success</button>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="row mt10">

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">Alternative style</div>
                <div class="card-body">
                    <button type="button" class="btn btn-default btn-alt mr5 mb10">Default</button>
                    <button type="button" class="btn btn-primary btn-alt mr5 mb10">Primary</button>
                    <button type="button" class="btn btn-secondary btn-alt mr5 mb10">Secondary</button>
                    <button type="button" class="btn btn-danger btn-alt mr5 mb10">Danger</button>
                    <button type="button" class="btn btn-warning btn-alt mr5 mb10">Warning</button>
                    <button type="button" class="btn btn-info btn-alt mr5 mb10">Info</button>
                    <button type="button" class="btn btn-link btn-alt mr5 mb10">Link</button>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">Disabled</div>
                <div class="card-body">
                    <button type="button" class="btn btn-primary mr5 mb10 disabled">Primary</button>
                    <button type="button" class="btn btn-danger btn-alt mr5 mb10 disabled">Danger alt</button>
                    <button type="button" class="btn btn-success btn-round mr5 mb10 disabled">1</button>
                    <button type="button" class="btn btn-info btn-left mr5 mb10 disabled">Left</button>
                    <button type="button" class="btn btn-warning btn-right mr5 mb10 disabled">Right</button>
                    <button type="button" class="btn btn-pink btn-block disabled">Block button</button>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt10">

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">Block buttons</div>
                <div class="card-body">
                    <button type="button" class="btn btn-default btn-block mb10">Default</button>
                    <button type="button" class="btn btn-primary btn-block mb10">Primary</button>
                    <button type="button" class="btn btn-secondary btn-block mb10">Secondary</button>
                    <button type="button" class="btn btn-success btn-block">Success</button>
                    <button type="button" class="btn btn-danger btn-block mr5 mb10">Danger</button>
                    <button type="button" class="btn btn-warning btn-block mr5 mb10">Warning</button>
                    <button type="button" class="btn btn-info btn-block mr5 mb10">Info</button>
                    <button type="button" class="btn btn-ligth btn-block mr5 mb10">Light</button>
                    <button type="button" class="btn btn-dark btn-block mr5 mb10">Dark</button>

                    <button type="button" class="btn btn-abus-blue-dark btn-block mr5 mb10">ABUS blue dark</button>
                    <button type="button" class="btn btn-abus-blue-light btn-block mr5 mb10">ABUS blue light</button>
                    <button type="button" class="btn btn-abus-yellow-light btn-block mr5 mb10">ABUS yellow light</button>
                    <button type="button" class="btn btn-abus-yellow-light btn-block mr5 mb10">ABUS yellow light</button>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">Buttons with icons</div>
                <div class="card-body">
                    <button type="button" class="btn btn-default mr5 mb10"><i class="fa fa-star mr5"></i> Default</button>
                    <button type="button" class="btn btn-primary mr5 mb10"><i class="fa fa-facebook mr5"></i> Primary</button>
                    <button type="button" class="btn btn-secondary mr5 mb10"><i class="fa fa-telegram mr5"></i> Secondary</button>
                    <button type="button" class="btn btn-success mr5 mb10"><i class="fa fa-map-marker mr5"></i> Success</button>
                    <button type="button" class="btn btn-danger mr5 mb10"><i class="fa fa-thumbs-up mr5"></i> Danger</button>
                    <button type="button" class="btn btn-warning mr5 mb10"><i class="fa fa-bullhorn mr5"></i> Warning</button>
                    <button type="button" class="btn btn-info mr5 mb10"><i class="fa fa-volume-down mr5"></i> Info</button>
                    <button type="button" class="btn btn-light mr5 mb10"><i class="fa fa-volume-up mr5"></i> Light</button>
                    <button type="button" class="btn btn-dark mr5 mb10"><i class="fa fa-bath mr5"></i> Dark</button>
                </div>
            </div>

            <div class="card mt20">
                <div class="card-header">Buttons with different shapes</div>
                <div class="card-body">
                    <div class="panel-body">
                        <button type="button" class="btn btn-default btn-xs btn-round mr5 mb10">1</button>
                        <button type="button" class="btn btn-primary btn-sm btn-round mr5 mb10">2</button>
                        <button type="button" class="btn btn-danger btn-round mr5 mb10">3</button>
                        <button type="button" class="btn btn-success btn-lg btn-round mr5 mb10">4</button>
                        <button type="button" class="btn btn-default btn-xs btn-alt btn-round mr5 mb10">1</button>
                        <button type="button" class="btn btn-primary btn-sm btn-alt btn-round mr5 mb10">2</button>
                        <button type="button" class="btn btn-danger btn-alt btn-round mr5 mb10">3</button>
                        <button type="button" class="btn btn-success btn-lg btn-alt btn-round mr5 mb10">4</button>
                        <br>
                        <button type="button" class="btn btn-default btn-left btn-xs mr5 mb10">Left button</button>
                        <button type="button" class="btn btn-primary btn-left btn-sm mr5 mb10">Left button</button>
                        <button type="button" class="btn btn-success btn-left mr5 mb10">Left button</button>
                        <button type="button" class="btn btn-info btn-left btn-lg mr5 mb10">Left button</button>
                        <br>
                        <button type="button" class="btn btn-default btn-right btn-xs ml5 mb10">Right btn</button>
                        <button type="button" class="btn btn-primary btn-right btn-sm ml5 mb10">Right btn</button>
                        <button type="button" class="btn btn-success btn-right ml5 mb10">Right btn</button>
                        <button type="button" class="btn btn-info btn-right btn-lg ml5 mb10">Right btn</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt10">

        <div class="col-md-6">

            <div class="card">
                <div class="card-header">Static alert messages</div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                        <i class="fa fa-exclamation-triangle alert-icon "></i>
                        <strong>Warning!</strong> Best check yo self, you're not looking too good.
                    </div>

                    <div class="alert alert-danger">
                        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                        <i class="fa fa-ban alert-icon "></i>
                        <strong>Oh snap!</strong> Change a few things up and try submitting again.
                    </div>

                    <div class="alert alert-success">
                        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                        <i class="fa fa-check alert-icon "></i>
                        <strong>Well done!</strong> You successfully read this important alert message.
                    </div>

                    <div class="alert alert-info">
                        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                        <i class="fa fa-info-circle alert-icon "></i>
                        <strong>Heads up!</strong> This alert needs your attention, but it's not super important.
                    </div>
                </div>
            </div>

            <div class="card mt20">
                <div class="card-header">Bootstrap tooltip</div>
                <div class="card-body">

                    <button type="button" class="btn btn-secondary mr15" data-toggle="tooltip" data-placement="top" title="Tooltip on top">
                        Tooltip on top
                    </button>
                    <button type="button" class="btn btn-secondary mr15" data-toggle="tooltip" data-placement="right" title="Tooltip on right">
                        Tooltip on right
                    </button>
                    <button type="button" class="btn btn-secondary mr15" data-toggle="tooltip" data-placement="bottom" title="Tooltip on bottom">
                        Tooltip on bottom
                    </button>
                    <button type="button" class="btn btn-secondary mr15" data-toggle="tooltip" data-placement="left" title="Tooltip on left">
                        Tooltip on left
                    </button>
                </div>
            </div>

            <div class="card mt20">
                <div class="card-header">Bootstrap popovers  <small>click for action</small></div>
                <div class="card-body">

                    <button type="button" class="btn btn-secondary mr15" data-container="body" data-toggle="popover" data-placement="top" data-content="Vivamus sagittis lacus vel augue laoreet rutrum faucibus.">
                        Popover on top
                    </button>

                    <button type="button" class="btn btn-secondary mr15" data-container="body" data-toggle="popover" data-placement="right" data-content="Vivamus sagittis lacus vel augue laoreet rutrum faucibus.">
                        Popover on right
                    </button>

                    <button type="button" class="btn btn-secondary mr15" data-container="body" data-toggle="popover" data-placement="bottom" data-content="Vivamus sagittis lacus vel augue laoreet rutrum faucibus.">
                        Popover on bottom
                    </button>

                    <button type="button" class="btn btn-secondary mr15" data-container="body" data-toggle="popover" data-placement="left" data-content="Vivamus sagittis lacus vel augue laoreet rutrum faucibus.">
                        Popover on left
                    </button>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">Callout messages</div>
                <div class="card-body">

                    <div class="bs-callout bs-callout-warning">
                        <button type="button" data-dismiss="alert" aria-hidden="true" class="close">×</button>

                        <h4>Warning</h4>
                        <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Tempora, explicabo, doloremque, ullam neque veritatis quasi hic excepturi eum laborum sunt nisi libero natus ab eos autem voluptatum non numquam fuga!</p>
                    </div>

                    <div class="bs-callout bs-callout-info">
                        <button type="button" data-dismiss="alert" aria-hidden="true" class="close">×</button>
                        <h4>Info</h4>
                        <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Tempora, explicabo, doloremque, ullam neque veritatis quasi hic excepturi eum laborum sunt nisi libero natus ab eos autem voluptatum non numquam fuga!</p>
                    </div>

                    <div class="bs-callout bs-callout-success">
                        <button type="button" data-dismiss="alert" aria-hidden="true" class="close">×</button>
                        <h4>Success</h4>
                        <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Tempora, explicabo, doloremque, ullam neque veritatis quasi hic excepturi eum laborum sunt nisi libero natus ab eos autem voluptatum non numquam fuga!</p>
                    </div>

                    <div class="bs-callout bs-callout-danger">
                        <button type="button" data-dismiss="alert" aria-hidden="true" class="close">×</button>
                        <h4>Danger</h4>
                        <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Tempora, explicabo, doloremque, ullam neque veritatis quasi hic excepturi eum laborum sunt nisi libero natus ab eos autem voluptatum non numquam fuga!</p>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <div class="row mt10">

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">Tabs</div>
                <div class="card-body">

                    <ul class="nav nav-tabs">
                        <li class="nav-item">
                            <a class="nav-link active" href="#">Active</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">Link</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">Link</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link disabled" href="#">Disabled</a>
                        </li>
                    </ul>

                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">Tabs with card</div>
                <div class="card-body">
                    <div class="card ">
                        <div class="card-header">
                            <ul class="nav nav-tabs card-header-tabs"  id="myTab" role="tablist">
                                <li class="nav-item">
                                    <a class="nav-link active" id="tab-1" data-toggle="tab" href="#home" role="tab" aria-controls="home" aria-selected="true">Home</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" id="tab-2" data-toggle="tab" href="#profile" role="tab" aria-controls="profile" aria-selected="false">Profile</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" id="tab-3" data-toggle="tab" href="#contact" role="tab" aria-controls="contact" aria-selected="false">Contact</a>
                                </li>
                            </ul>
                        </div>
                        <div class="card-body">
                            <div class="tab-content" id="myTabContent">
                                <div class="tab-pane fade show active" id="home" role="tabpanel" aria-labelledby="home-tab">
                                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nulla sagittis ex quis ex posuere, quis finibus elit dignissim. Sed purus nisi, placerat accumsan lorem at, fringilla finibus mauris.
                                </div>
                                <div class="tab-pane fade" id="profile" role="tabpanel" aria-labelledby="profile-tab">
                                    Nulla id placerat sem. Nunc laoreet erat quis ligula semper tempor. Phasellus ornare ligula non vestibulum tincidunt. Nam tincidunt elit nec blandit faucibus. Nunc at erat nec nulla cursus fringilla quis at magna. Vestibulum maximus volutpat arcu vel laoreet. Donec et ligula erat. Ut pellentesque posuere turpis. Aliquam varius scelerisque mauris vitae consequat. Quisque sed commodo sem, eget lacinia urna. Cras sollicitudin enim vel pharetra pulvinar.
                                </div>
                                <div class="tab-pane fade" id="contact" role="tabpanel" aria-labelledby="contact-tab">
                                    Integer ullamcorper pulvinar ante vitae venenatis. Cras maximus semper sapien, at porta est tempus eu. Quisque hendrerit arcu vel ante bibendum, ac interdum mauris ultricies. Class aptent taciti sociosqu ad litora torquent per conubia nostra, per inceptos himenaeos. Suspendisse at libero eget arcu ornare mattis. Curabitur consequat, nunc non mattis malesuada, felis leo sodales sem, in mattis mauris neque sodales risus. Sed rhoncus pulvinar arcu nec tristique. Phasellus placerat odio quis diam iaculis vulputate. Fusce fermentum tellus ut pharetra sodales. Nunc blandit auctor auctor. Fusce sed nisi id ante facilisis ultricies. Morbi tristique a metus vel lobortis. Cras nec semper quam, sed varius erat. Aliquam accumsan gravida rhoncus. Donec vehicula volutpat posuere.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="row mt10">

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">Tabs with card (right)</div>
                <div class="card-body">
                    <div class="card ">
                        <div class="card-header">
                            <ul class="nav nav-tabs card-header-tabs pull-right"  id="myTab" role="tablist">
                                <li class="nav-item">
                                    <a class="nav-link active" id="home-tab" data-toggle="tab" href="#home" role="tab" aria-controls="home" aria-selected="true">Home</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" id="profile-tab" data-toggle="tab" href="#profile" role="tab" aria-controls="profile" aria-selected="false">Profile</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" id="contact-tab" data-toggle="tab" href="#contact" role="tab" aria-controls="contact" aria-selected="false">Contact</a>
                                </li>
                            </ul>
                        </div>
                        <div class="card-body">
                            <div class="tab-content" id="myTabContent">
                                <div class="tab-pane fade show active" id="home" role="tabpanel" aria-labelledby="home-tab">...</div>
                                <div class="tab-pane fade" id="profile" role="tabpanel" aria-labelledby="profile-tab">...</div>
                                <div class="tab-pane fade" id="contact" role="tabpanel" aria-labelledby="contact-tab">...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>





    <div class="row mt10">

        <div class="col-md-6">

            <div class="card">
                <div class="card-header">Labels</div>
                <div class="card-body">
                    <span class="badge badge-pill badge-default mr10 mb10">Default</span>
                    <span class="badge badge-pill badge-primary mr10 mb10">Primary</span>
                    <span class="badge badge-pill badge-secondary mr10 mb10">Secondary</span>
                    <span class="badge badge-pill badge-success mr10 mb10">Success</span>
                    <span class="badge badge-pill badge-danger mr10 mb10">Danger</span>
                    <span class="badge badge-pill badge-warning mr10 mb10">Warning</span>
                    <span class="badge badge-pill badge-info mr10 mb10">Info</span>
                    <span class="badge badge-pill badge-light mr10 mb10">Light</span>
                    <span class="badge badge-pill badge-dark mr10 mb10">Dark</span>
                </div>
            </div>

        </div>

        <div class="col-md-6">

            <div class="card">
                <div class="card-header">Badges</div>
                <div class="card-body">
                    <span class="badge badge-default mr10 mb10">Default</span>
                    <span class="badge badge-primary mr10 mb10">Primary</span>
                    <span class="badge badge-secondary mr10 mb10">Secondary</span>
                    <span class="badge badge-success mr10 mb10">Success</span>
                    <span class="badge badge-info mr10 mb10">Info</span>
                    <span class="badge badge-warning mr10 mb10">Warning</span>
                    <span class="badge badge-danger mr10 mb10">Danger</span>
                    <span class="badge badge-light mr10 mb10">Light</span>
                    <span class="badge badge-dark mr10 mb10">Dark</span>
                </div>
            </div>

        </div>

    </div>

    <div class="row mt10">

        <div class="col-md-12">
            <div class="card">
                <div class="card-header">Forms</div>
                <div class="card-body">
                    <form class="form-horizontal group-border stripped">

                        <div class="form-group">
                            <label class="col-lg-2 col-md-3 control-label" for="">Default input</label>
                            <div class="col-lg-10 col-md-9">
                                <input type="text" class="form-control" name="default">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-lg-2 col-md-3  control-label" for="">Password field</label>
                            <div class="col-lg-10 col-md-9">
                                <input type="password" class="form-control" id="passwordfield" placeholder="Type your password">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-lg-2 col-md-3 control-label" for="">Disabled</label>
                            <div class="col-lg-10 col-md-9">
                                <input type="text" class="form-control" name="disabled" disabled="" placeholder="This is disabled filed">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-lg-2 col-md-3 control-label" for="">Read only</label>
                            <div class="col-lg-10 col-md-9">
                                <input type="text" class="form-control" name="readonly" readonly="" placeholder="Read only field">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-lg-2 col-md-3 control-label" for="">Predefined value</label>
                            <div class="col-lg-10 col-md-9">
                                <input type="text" class="form-control" name="predefined" value="http://">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-lg-2 col-md-3 control-label" for="">Placeholder</label>
                            <div class="col-lg-10 col-md-9">
                                <input type="text" class="form-control" name="placeholder" placeholder="This is placeholder text">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-lg-2 col-md-3 control-label" for="">With tooltip</label>
                            <div class="col-lg-10 col-md-9">
                                <input type="text" class="form-control" name="tooltip" placeholder="Hover for tooltip action" data-toggle="tooltip" data-placement="top" title="Vivamus sagittis lacus vel augue laoreet rutrum faucibus.">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-lg-2 col-md-3 control-label" for="">Static control</label>
                            <div class="col-lg-10 col-md-9">
                                <p class="form-control-plaintext">Jonh Doe</p>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-lg-2 col-md-3 control-label" for="">Rounded input</label>
                            <div class="col-lg-10 col-md-9">
                                <input type="text" class="form-control input-rounded" name="default">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-lg-2 col-md-3 control-label" for="">Help block</label>
                            <div class="col-lg-10 col-md-9">
                                <div class="row">
                                    <div class="col-lg-4 col-md-4">
                                        <input type="text" class="form-control">
                                        <small class="form-text text-muted">Left help block</small>
                                    </div>
                                    <div class="col-lg-4 col-md-4">
                                        <input type="text" class="form-control">
                                        <small class="form-text text-muted text-center">Center help block</small>
                                    </div>
                                    <div class="col-lg-4 col-md-4">
                                        <input type="text" class="form-control">
                                        <small class="form-text text-muted text-right">Right help block</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-lg-2 col-md-3  control-label" for="">With icons</label>
                            <div class="col-lg-10 col-md-9">
                                <div class="row">
                                    <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                                        <div class="input-group input-icon">
                                            <span class="input-group-addon"><i class="fa fa-user s16"></i></span>
                                            <input type="text" class="form-control" placeholder="Icon on left">
                                        </div>
                                    </div>
                                    <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
                                        <div class="input-group input-icon">
                                            <input type="text" class="form-control" placeholder="Icon on right">
                                            <span class="input-group-addon"><i class="fa fa-key s16"></i> </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group has-success">
                            <label class="col-lg-2 col-md-3 control-label">Success input</label>
                            <div class="col-lg-10 col-md-9">
                                <input type="text" class="form-control" placeholder="Success Field">
                            </div>
                        </div>

                        <div class="form-group has-warning">
                            <label class="col-lg-2 col-md-3 control-label">Warning input</label>
                            <div class="col-lg-10 col-md-9">
                                <input type="text" class="form-control" placeholder="Warning Field">
                            </div>
                        </div>

                        <div class="form-group has-error">
                            <label class="col-lg-2 col-md-3 control-label">Error input</label>
                            <div class="col-lg-10 col-md-9">
                                <input type="text" class="form-control" placeholder="Error Field">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-lg-2 col-md-3 control-label" for="">File upload</label>
                            <div class="col-lg-10 col-md-9">
                                <div class="custom-file">
                                    <label class="custom-file-label" for="customFile">File upload</label>
                                    <input type="file" class="custom-file-input" id="customFile">
                                </div>
                            </div>
                        </div>

                        <div class="form-group form-group-vertical">
                            <label class="col-lg-2 col-md-3 control-label" for="">Vertical form group</label>
                            <div class="col-lg-10 col-md-9">
                                <input type="text" class="form-control" name="default" placeholder="Your Name">
                                <input type="email" class="form-control" name="email" placeholder="Your Email">
                                <input type="password" class="form-control" name="password" placeholder="Your Password">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-lg-2 col-md-3 control-label">Grid input sizes</label>
                            <div class="col-lg-10 col-md-9">
                                <div class="row">
                                    <div class="col-lg-6 col-md-6">
                                        <input type="text" class="form-control" placeholder=".col-lg-6">
                                    </div>
                                    <div class="col-lg-3 col-md-3">
                                        <input type="text" class="form-control" placeholder=".col-lg-3">
                                    </div>
                                    <div class="col-lg-3 col-md-3">
                                        <input type="text" class="form-control" placeholder=".col-lg-3">
                                    </div>
                                </div>
                            </div>
                        </div>

                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt10">

        <div class="col-md-12">
            <div class="card">
                <div class="card-header">Checkboxes, radios and selects</div>
                <div class="card-body">

                    <form class="form-horizontal group-border stripped">

                        <div class="form-group">
                            <label class="col-lg-2 col-md-3 control-label">Checkboxes</label>
                            <div class="col-lg-10 col-md-9">

                                <div class="custom-control custom-checkbox">
                                    <input class="custom-control-input" type="checkbox" value="" id="check1">
                                    <label class="custom-control-label" for="check1">
                                        Unchecked
                                    </label>
                                </div>

                                <div class="custom-control custom-checkbox">
                                    <input class="custom-control-input" type="checkbox" value="" id="check2" checked="checked">
                                    <label class="custom-control-label" for="check2">
                                        Checked
                                    </label>
                                </div>

                                <div class="custom-control custom-checkbox">
                                    <input class="custom-control-input" type="checkbox" value="" id="check3" disabled>
                                    <label class="custom-control-label" for="check3">
                                        Disabled
                                    </label>
                                </div>

                                <div class="custom-control custom-checkbox">
                                    <input class="custom-control-input" type="checkbox" value="" id="check4" checked="checked" disabled>
                                    <label class="custom-control-label" for="check4">
                                        Checked and disabled
                                    </label>
                                </div>
                            </div>
                        </div>


                        <div class="form-group">
                            <label class="col-lg-2 col-md-3 control-label">Inline Checkboxes</label>
                            <div class="col-lg-10 col-md-9">

                                <div class="custom-control custom-control-inline custom-checkbox">
                                    <input class="custom-control-input" type="checkbox" value="" id="check5">
                                    <label class="custom-control-label" for="check5">
                                        Unchecked
                                    </label>
                                </div>

                                <div class="custom-control custom-control-inline custom-checkbox">
                                    <input class="custom-control-input" type="checkbox" value="" id="check6" checked="checked">
                                    <label class="custom-control-label" for="check6">
                                        Checked
                                    </label>
                                </div>

                                <div class="custom-control custom-control-inline custom-checkbox">
                                    <input class="custom-control-input" type="checkbox" value="" id="check7" disabled>
                                    <label class="custom-control-label" for="check7">
                                        Disabled
                                    </label>
                                </div>

                                <div class="custom-control custom-control-inline custom-checkbox">
                                    <input class="custom-control-input" type="checkbox" value="" id="check8" checked="checked" disabled>
                                    <label class="custom-control-label" for="check8">
                                        Checked and disabled
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-lg-2 col-md-3 control-label">Check all</label>
                            <div class="col-lg-10 col-md-9">

                                <div class="custom-control custom-checkbox">
                                    <input class="custom-control-input check-all" type="checkbox" value="" id="check9">
                                    <label class="custom-control-label" for="check9">
                                        Check all
                                    </label>
                                </div>

                                <div class="children">
                                    <div class="custom-control custom-checkbox">
                                        <input class="custom-control-input" type="checkbox" value="" id="check10">
                                        <label class="custom-control-label" for="check10">
                                            Option 1
                                        </label>
                                    </div>

                                    <div class="custom-control custom-checkbox">
                                        <input class="custom-control-input" type="checkbox" value="" id="check11">
                                        <label class="custom-control-label" for="check11">
                                            Option 2
                                        </label>
                                    </div>

                                    <div class="custom-control custom-checkbox">
                                        <input class="custom-control-input" type="checkbox" value="" id="check12">
                                        <label class="custom-control-label" for="check12">
                                            Option 3
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-lg-2 col-md-3 control-label">Radios</label>
                            <div class="col-lg-10 col-md-9">

                                <div class="custom-control custom-radio">
                                    <input type="radio" id="customRadio1" name="customRadio" class="custom-control-input">
                                    <label class="custom-control-label" for="customRadio1">Unchecked</label>
                                </div>

                                <div class="custom-control custom-radio">
                                    <input type="radio" id="customRadio2" name="customRadio" class="custom-control-input" checked="checked">
                                    <label class="custom-control-label" for="customRadio2">Checked</label>
                                </div>

                                <div class="custom-control custom-radio">
                                    <input type="radio" id="customRadio3" name="customRadio" class="custom-control-input" disabled="disabled">
                                    <label class="custom-control-label" for="customRadio3">Disabled</label>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-lg-2 col-md-3 control-label">Inline radios</label>
                            <div class="col-lg-10 col-md-9">

                                <div class="custom-control custom-control-inline custom-radio">
                                    <input type="radio" id="customRadio4" name="customRadio2" class="custom-control-input">
                                    <label class="custom-control-label" for="customRadio4">Unchecked</label>
                                </div>

                                <div class="custom-control custom-control-inline custom-radio">
                                    <input type="radio" id="customRadio5" name="customRadio2" class="custom-control-input" checked="checked">
                                    <label class="custom-control-label" for="customRadio5">Checked</label>
                                </div>

                                <div class="custom-control custom-control-inline custom-radio">
                                    <input type="radio" id="customRadio6" name="customRadio2" class="custom-control-input" disabled="disabled">
                                    <label class="custom-control-label" for="customRadio6">Disabled</label>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-lg-2 col-md-3 control-label">Simple select</label>
                            <div class="col-lg-10 col-md-9">
                                <select class="form-control">
                                    <optgroup label="Alaskan/Hawaiian Time Zone">
                                        <option value="AK">Alaska</option>
                                        <option value="HI">Hawaii</option>
                                    </optgroup>
                                    <optgroup label="Pacific Time Zone">
                                        <option value="CA">California</option>
                                        <option value="NV">Nevada</option>
                                        <option value="OR">Oregon</option>
                                        <option value="WA">Washington</option>
                                    </optgroup>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-lg-2 col-md-3 control-label">Multiple select</label>
                            <div class="col-lg-10 col-md-9">
                                <select style="height:200px;" class="form-control" multiple="">
                                    <optgroup label="Alaskan/Hawaiian Time Zone">
                                        <option value="AK">Alaska</option>
                                        <option value="HI">Hawaii</option>
                                    </optgroup>
                                    <optgroup label="Pacific Time Zone">
                                        <option value="CA">California</option>
                                        <option value="NV">Nevada</option>
                                        <option value="OR">Oregon</option>
                                        <option value="WA">Washington</option>
                                    </optgroup>
                                </select>
                                <span class="help-block color-red">Multiple select (Hold CTRL key )</span>
                            </div>
                        </div>

                    </form>

                </div>
            </div>
        </div>

    </div>

    <div class="row mt10">

        <div class="col-md-12">
            <div class="card">
                <div class="card-header">Input groups</div>
                <div class="card-body">
                    <form class="form-horizontal group-border stripped">

                        <div class="form-group">
                            <label class="col-lg-2 col-md-3 control-label" for="">Append input group</label>
                            <div class="col-lg-10 col-md-9">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <div class="input-group-text">@</div>
                                    </div>
                                    <input type="text" class="form-control" placeholder="Username">
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-lg-2 col-md-3 control-label" for="">Prepend input group</label>
                            <div class="col-lg-10 col-md-9">
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="Username">
                                    <div class="input-group-append">
                                        <div class="input-group-text">@</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-lg-2 col-md-3 control-label" for="">Both input groups</label>
                            <div class="col-lg-10 col-md-9">
                                <div class="input-group mb-3">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">$</span>
                                    </div>
                                    <input type="text" class="form-control" aria-label="Amount (to the nearest dollar)">
                                    <div class="input-group-append">
                                        <span class="input-group-text">.00</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-lg-2 col-md-3 control-label" for="">With buttons</label>
                            <div class="col-lg-10 col-md-9">
                                <div class="row">
                                    <div class="col-lg-6 col-md-6">
                                        <div class="input-group mb-3">
                                            <div class="input-group-prepend">
                                                <button class="btn btn-outline-secondary" type="button">Button</button>
                                            </div>
                                            <input type="text" class="form-control" placeholder="" aria-label="" aria-describedby="basic-addon1">
                                        </div>
                                    </div>
                                    <div class="col-lg-6 col-md-6">
                                        <div class="input-group">
                                            <div class="input-group mb-3">
                                                <input type="text" class="form-control" placeholder="Recipient's username" aria-label="Recipient's username" aria-describedby="basic-addon2">
                                                <div class="input-group-append">
                                                    <button class="btn btn-outline-secondary" type="button">Button</button>
                                                </div>
                                            </div>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-lg-2 col-md-3 control-label" for="">With dropdowns</label>
                            <div class="col-lg-10 col-md-9">
                                <div class="row">
                                    <div class="col-lg-6 col-md-6">
                                        <div class="input-group mb-3">
                                            <div class="input-group-prepend">
                                                <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Dropdown</button>
                                                <div class="dropdown-menu">
                                                    <a class="dropdown-item" href="#">Action</a>
                                                    <a class="dropdown-item" href="#">Another action</a>
                                                    <a class="dropdown-item" href="#">Something else here</a>
                                                    <div role="separator" class="dropdown-divider"></div>
                                                    <a class="dropdown-item" href="#">Separated link</a>
                                                </div>
                                            </div>
                                            <input type="text" class="form-control" aria-label="Text input with dropdown button">
                                        </div>
                                        <!-- /input-group -->
                                    </div>
                                    <div class="col-lg-6 col-md-6">
                                        <div class="input-group">
                                            <input type="text" class="form-control" aria-label="Text input with dropdown button">
                                            <div class="input-group-append">
                                                <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Dropdown</button>
                                                <div class="dropdown-menu">
                                                    <a class="dropdown-item" href="#">Action</a>
                                                    <a class="dropdown-item" href="#">Another action</a>
                                                    <a class="dropdown-item" href="#">Something else here</a>
                                                    <div role="separator" class="dropdown-divider"></div>
                                                    <a class="dropdown-item" href="#">Separated link</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                </div>
            </div>
        </div>
    </div>

    <div class="row mt10">

        <div class="col-md-12">
            <div class="card">
                <div class="card-header">Headings</div>
                <div class="card-body">
                    <h1 id="h1-heading">h1 heading</h1>
                    <h2 id="h2-heading">h2 heading</h2>
                    <h3 id="h3-heading">h3 heading</h3>
                    <h4 id="h4-heading">h4 heading</h4>
                    <h5 id="h5-heading">h5 heading</h5>
                    <h6 id="h6-heading">h6 heading</h6>
                </div>
            </div>
        </div>

    </div>








    {#<div class="row mt10">#}

    {#<div class="col-md-6">#}
    {#<div class="card">#}
    {#<div class="card-header">???</div>#}
    {#<div class="card-body">#}

    {#</div>#}
    {#</div>#}
    {#</div>#}

    {#<div class="col-md-6">#}
    {#<div class="card">#}
    {#<div class="card-header">???</div>#}
    {#<div class="card-body">#}

    {#</div>#}
    {#</div>#}
    {#</div>#}
    {#</div>#}

{% endblock %}




