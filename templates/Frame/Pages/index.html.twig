{% extends 'Frame/Layout/layout.html.twig' %}

{% block javascripts %}
    {{ parent() }}
{% endblock %}

{% block administration %}
    {{ parent() }}

    {% if is_granted('ROLE_PORTAL_NEWS_ADMIN') %}
        <div class="sidebar-panel">
            <h5 class="sidebar-panel-title">
                {{ ["frame/news"]|join|trans({}, 'across') }}
            </h5>
        </div>

        <div class="content" >
            <a href="{{ path('abus_frame_news') }}" class="btn btn-danger btn-block btn-sm mr5"><i class="fa fa-lock" aria-hidden="true"></i>&nbsp;&nbsp;&nbsp;{{ "frame/news/administration"|trans({}, 'across') }}</a>
        </div>
    {% endif %}
{% endblock %}

{% block content %}

    {% if user.hasRole('/^ROLE_PORTAL_INFORMATION_/i', true) %}
        {% set apis = true %}
    {% elseif user.hasRole('ROLE_PORTAL_GRUPPE_MONTAGEPARTNER', false) %}
        {% set apis = true %}
    {% elseif user.hasRole('ROLE_PORTAL_GRUPPE_SERVICEPARTNER', false) %}
        {% set apis = true %}
    {% elseif user.hasRole('ROLE_PORTAL_GRUPPE_ABNAHMEPARTNER', false) %}
        {% set apis = true %}
    {% else %}
        {% set apis = false %}
    {% endif %}

    {% if user.hasRole('ROLE_PORTAL_ADMIN', false) %}
        {% set newsAdmin = true %}
    {% elseif user.hasRole('ROLE_PORTAL_NEWS_ADMIN', false) %}
        {% set newsAdmin = true %}
    {% else %}
        {% set newsAdmin = false %}
    {% endif %}

    <dashboard
        dashboard-apis="{{ apis }}"
        dashboard-news-admin="{{ newsAdmin }}"
        abukonfis="{{ user.hasRole('/^ROLE_PORTAL_TECHNICAL_DATA_ABUKONFIS(.*)/i', true) }}"
    ></dashboard>

{% endblock %}
