{% extends 'Frame/Layout/layout.html.twig' %}

{% block title %} {{ "frame/reportProblem/title"|trans({}, 'across') }} | ABUS Kransysteme GmbH{% endblock %}

{% set showPageHeader = false %}

{% block content %}

        {% set moduleGroups = [] %}
        {% set modules = [] %}

        {% for module in user.modules %}

            {% if module.submodules is defined and module.submodules|length > 0 %}

                {% set groups = [] %}

                {% for submodule in module.submodules %}
                    {% set groups = groups|merge([{ 'name': submodule.name|trans({}, 'across'), 'value': [module.identifier, ' - ', submodule.identifier]|join }]) %}
                {% endfor %}

                {% set moduleGroups = moduleGroups|merge([{ 'name': module.name|trans({}, 'across'), 'groups': groups }]) %}

            {% else %}
                {% set modules = modules|merge([{ 'name': module.name|trans({}, 'across'), 'value': module.identifier }]) %}
            {% endif %}

        {% endfor %}

        {% set modules = modules|merge([{ 'name': 'frame/reportProblem/others'|trans({}, 'across'), 'value': 'others' }]) %}

    <report-problem
        upload-url="{{ path('abus_frame_report_problem_send') }}"
        image-upload-url="{{ path('abus_frame_report_problem_image_upload') }}"
        module-groups="{{ moduleGroups|json_encode() }}"
        modules="{{ modules|json_encode() }}"
        domain="{{ app.request.getSchemeAndHttpHost() }}">
    </report-problem>

{% endblock %}
