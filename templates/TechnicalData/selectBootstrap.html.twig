<label class="col-6 col-lg-2 control-label">
    {% if showname is defined %}
        {{ ['tdata/', showname|lower|replace({' ': '_'})]|join|trans({}, 'across') }}
    {% else %}
        {{ ['tdata/', name|lower|replace({' ': '_'})]|join|trans({}, 'across') }}
    {% endif %}
    {% if info is defined %}
        <i class="fa fa-info-circle hidden-xs" data-toggle="tooltip" data-placement="top" title="{{ info|trans({}, 'across') }}"></i>
    {% endif %}
</label>
<div class="col-6 col-lg-3 mb-4">
    <select name="{{ name|replace({' ': '_'})|lower() }}" id="{{ name|replace({' ': '_'})|lower() }}" class="form-control">
        <option value=""></option>
        {% for option in options %}
            {% if option.key is defined %}
                {% set key = option.key %}
            {% else %}
                {% set key = option.value %}
            {% endif %}
            <option value="{{ key|lower() }}">{% if prefix is defined %}{{ prefix }}{% endif %}{{ translateTechnicalDataOption(option.value) }}{% if suffix is defined and option.value != 'manual' and option.value != 'electrical' and option.value != 'min' %} {{ suffix }}{% endif %}</option>
        {% endfor %}
    </select>
</div>
