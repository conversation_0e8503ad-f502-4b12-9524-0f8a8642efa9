{% set showPageHeader = true %}

{% extends 'TechnicalData/tdataBaseFrame.html.twig' %}

{% block headline %}{{ "tdata/technical_data"|trans({}, 'across') }}{% endblock %}
{% block headline_description %}{{ "tdata/mobile_gantry_cranes"|trans({}, 'across') }}{% endblock %}
{% block headline_backbutton %}{% endblock %}

{% block tdataContent %}

    <div class="row mt-3 mb-3">
        <div class="col">

            <div class="card card-closed mt-3">
                <div class="card-header text-white bg-abus-blue-light">
                    <i class="fa fa-th-large"></i>
                    {{ 'tdata/crane_selection_help'|trans({}, 'across') }}
                    <div class="card-controls card-controls-right">
                        <a href="#" class="toggle card-minimize">
                            <i class="fa-angle-up fa"></i>
                        </a>
                    </div>
                </div>
                <div class="card-body bg-white">
                    {% include 'TechnicalData/ProduktInformation/Leichtportalkran/Leichtportalkran.html.twig' %}

                    {#<box-frontend start-folder="{{ "tdata/technische_daten/mobile_gantry"|trans({}, 'across') }}" :is-admin="false" :inline-search="false"></box-frontend>#}
                    <nextcloud start-folder="{{ "tdata/technische_daten/mobile_gantry"|trans({}, 'across') }}" :is-admin="false" :inline-search="false"></nextcloud>
                </div>
            </div>
        </div>
    </div>

    {% if user.werksvertretung != false %}
        <div class="row mb-3">
            <div class="col-md-12 col-lg-6 mb-3">
                {% include 'TechnicalData/KontaktInformation/ABUS.html.twig' %}
            </div>
            <div class="col-md-12 col-lg-6 mb-3">
                {% include 'TechnicalData/KontaktInformation/website_mobile_gantry_cranes.html.twig' %}
            </div>
        </div>
    {% endif %}

    <div class="card mb-3">
        <div class="card-body bg-white selectionPortlet">
            <form action="" method="post" enctype="text/plain">
                <div class="form-group form-row">
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'Load capacity', 'suffix': 'kg', 'options': tdata.getSelectValues('tune_mobile_gantry_cranes_load_capacity') } only %}
                    <div class="d-none d-lg-block col-lg-2"></div>
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'Trolley travel', 'options': tdata.getSelectValues('tune_mobile_gantry_cranes_drive') } only %}
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'Total width', 'suffix': 'mm', 'options': tdata.getSelectValues('tune_mobile_gantry_cranes_total_width') } only %}
                    <div class="d-none d-lg-block col-lg-2"></div>
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'Total height', 'suffix': 'mm', 'options': tdata.getSelectValues('tune_mobile_gantry_cranes_total_height') } only %}
                </div>
            </form>
        </div>
        <div class="card-footer text-muted">
            {{ ['<center>', "tdata/please_select_three"|trans({}, 'across'), '</center>']|join|raw }}
        </div>
    </div>

{% endblock %}
