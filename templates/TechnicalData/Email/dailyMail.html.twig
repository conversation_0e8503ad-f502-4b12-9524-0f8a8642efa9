{% extends 'Api/email/email.html.twig' %}

{% block content %}

    <span style="font-weight: bold;">
        {{ 'Dowloads within the last 24 hours'|trans({}, 'across', 'de_DE') }}:
    </span>

    <br /><br />

    <table border=0 width=100% cellpadding=0 cellspacing=0 style="border-spacing: 0px; font-family: sans-serif; font-size: 10pt;">
        <tr>
            <td style="padding-bottom: 5px; padding-right: 10px;">{{ 'Name'|trans({}, 'across', 'de_DE') }}:</td>
            <td>{{ name }}</td>
        </tr>
        <tr>
            <td style="padding-bottom: 5px; padding-right: 10px;">{{ 'Company'|trans({}, 'across', 'de_DE') }}:</td>
            <td>{{ company }}</td>
        </tr>
        <tr>
            <td style="padding-bottom: 5px; padding-right: 10px;">{{ 'Customernumber'|trans({}, 'across', 'de_DE') }}:</td>
            <td>{{ customernumber }}</td>
        </tr>
        <tr>
            <td style="padding-bottom: 5px; padding-right: 10px;">{{ 'Telephone'|trans({}, 'across', 'de_DE') }}:</td>
            <td>{{ telephone }}</td>
        </tr>
        <tr>
            <td style="padding-bottom: 5px; padding-right: 10px;">{{ 'eMail'|trans({}, 'across', 'de_DE') }}:</td>
            <td>{{ mail }}</td>
        </tr>
        <tr>
            <td valign=top style=" padding-right: 10px;">{{ 'Files'|trans({}, 'across', 'de_DE') }}:</td>
            <td>{{ downloads|raw() }}</td>
        </tr>
    </table>

{% endblock %}
