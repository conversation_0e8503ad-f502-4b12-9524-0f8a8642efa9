{% set showPageHeader = true %}

{% extends 'TechnicalData/tdataBaseFrame.html.twig' %}

{% block headline %}{{ "tdata/technical_data"|trans({}, 'across') }}{% endblock %}
{% block headline_description %}{{ "tdata/chain_hoists_with_trolley"|trans({}, 'across') }}{% endblock %}
{% block headline_backbutton %}{% endblock %}

{% block tdataContent %}

    <div class="row mt-3 mb-3">
        <div class="col">

            <div class="card card-closed mt-3">
                <div class="card-header text-white bg-abus-blue-light">
                    <i class="fa fa-th-large"></i>
                    {{ 'tdata/crane_selection_help'|trans({}, 'across') }}
                    <div class="card-controls card-controls-right">
                        <a href="#" class="toggle card-minimize">
                            <i class="fa-angle-up fa"></i>
                        </a>
                    </div>
                </div>
                <div class="card-body bg-white">
                    {% include 'TechnicalData/ProduktInformation/Kettenzug/gmc.html.twig' %}
                    {% include 'TechnicalData/ProduktInformation/Kettenzug/gm2.html.twig' %}
                    {% include 'TechnicalData/ProduktInformation/Kettenzug/gm4.html.twig' %}
                    {% include 'TechnicalData/ProduktInformation/Kettenzug/gm6.html.twig' %}
                    {% include 'TechnicalData/ProduktInformation/Kettenzug/gm8.html.twig' %}

                    {#<box-frontend start-folder="{{ "tdata/technische_daten/chain_hoist"|trans({}, 'across') }}" :is-admin="false" :inline-search="false"></box-frontend>#}
                    <nextcloud start-folder="{{ "tdata/technische_daten/chain_hoist"|trans({}, 'across') }}" :is-admin="false" :inline-search="true"></nextcloud>

                </div>
            </div>
        </div>
    </div>

    {% if user.werksvertretung != false %}
        <div class="row mb-3">
            <div class="col-md-12 col-lg-6 mb-3">
                {% include 'TechnicalData/KontaktInformation/ABUS.html.twig' %}
            </div>
            <div class="col-md-12 col-lg-6 mb-3">
                {% include 'TechnicalData/KontaktInformation/website_chain_hoists_with_trolley.html.twig' %}
            </div>
        </div>
    {% endif %}

    <div class="card mb-3">
        <div class="card-body bg-white selectionPortlet">
            <form action="" method="post" enctype="text/plain">
                <div class="form-group form-row">
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'Hoist size', 'prefix': 'GM', 'options': tdata.getSelectValues('tune_chain_hoists_hoist_size') } only %}
                    <div class="d-none d-lg-block col-lg-2"></div>
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'Load capacity', 'suffix': 'kg', 'options': tdata.getSelectValues('tune_chain_hoists_load_capacity') } only %}
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'Hook path', 'suffix': 'mm', 'options': tdata.getSelectValues('tune_chain_hoists_hw'), 'info': 'tdata/hook_path_hint' } only %}
                    <div class="d-none d-lg-block col-lg-2"></div>
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'Lifting', 'suffix': 'm/min', 'options': tdata.getSelectValues('tune_chain_hoists_lifting') } only %}
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'Drive', 'suffix': 'm/min', 'options': tdata.getSelectValues('tune_chain_hoists_drive') } only %}
                    <div class="d-none d-lg-block col-lg-2"></div>
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'fem_iso', 'showname': 'FEM/ISO', 'options': tdata.getSelectValues('tune_chain_hoists_femiso'), 'info': 'tdata/fem_hint' } only %}
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'Reeving', 'options': tdata.getSelectValues('tune_chain_hoists_reeving') } only %}
                    <div class="d-none d-lg-block col-lg-2"></div>
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'Operation voltage', 'suffix': 'V/Hz', 'options': tdata.getSelectValues('tune_chain_hoists_voltage') } only %}
                </div>
            </form>
        </div>
        <div class="card-footer text-muted">
            {{ ['<center>', "tdata/please_select_three"|trans({}, 'across'), '</center>']|join|raw }}
        </div>
    </div>

{% endblock %}
