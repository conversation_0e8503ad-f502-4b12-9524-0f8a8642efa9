{% set showPageHeader = true %}

{% extends 'TechnicalData/tdataBaseFrame.html.twig' %}

{% block headline %}{{ "tdata/technical_data"|trans({}, 'across') }}{% endblock %}
{% block headline_description %}{{ "tdata/crane_drives"|trans({}, 'across') }}{% endblock %}
{% block headline_backbutton %}{% endblock %}

{% block tdataContent %}

    <div class="mt-3"></div>

    {% if user.werksvertretung != false %}
        <div class="row mb-3">
            <div class="col-md-12 col-lg-6 mb-3">
                {% include 'TechnicalData/KontaktInformation/ABUS.html.twig' %}
            </div>
            <div class="col-md-12 col-lg-6 mb-3">
                {% include 'TechnicalData/KontaktInformation/website_crane_drives.html.twig' %}
            </div>
        </div>
    {% endif %}

    <div class="card mb-3">
        <div class="card-body bg-white selectionPortlet">
            <form action="" method="post" enctype="text/plain">
                <div class="form-group form-row">
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'Type', 'options': tdata.getSelectValues('tune_crane_drives_type') } only %}
                    <div class="d-none d-lg-block col-lg-2"></div>
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'Type of drive', 'options': tdata.getSelectValues('tune_crane_drives_type_of_drive') } only %}
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'Wheel', 'suffix': 'mm', 'options': tdata.getSelectValues('tune_crane_drives_wheel') } only %}
                    <div class="d-none d-lg-block col-lg-2"></div>
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'Wheel base', 'suffix': 'mm', 'options': tdata.getSelectValues('tune_crane_drives_wheel_base') } only %}
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'Drive', 'suffix': 'm/min', 'options': tdata.getSelectValues('tune_crane_drives_drive') } only %}
                    <div class="d-none d-lg-block col-lg-2"></div>
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'Spacing', 'suffix': 'mm', 'options': tdata.getSelectValues('tune_crane_drives_spacing') } only %}
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'FEM group', 'options': tdata.getSelectValues('tune_crane_drives_fem_group') } only %}
                    <div class="d-none d-lg-block col-lg-2"></div>
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'Operation voltage', 'suffix': 'V/Hz', 'options': tdata.getSelectValues('tune_crane_drives_voltage') } only %}
                </div>
            </form>
        </div>
        <div class="card-footer text-muted">
            {{ ['<center>', "tdata/please_select_three"|trans({}, 'across'), '</center>']|join|raw }}
        </div>
    </div>
{% endblock %}
