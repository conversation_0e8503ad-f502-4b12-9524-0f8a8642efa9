<table style="width: 100%;">

    <tr>
        <th>{{ "tdata/hoist"|trans({}, 'across') }}</th>
        {% if trolley_travel is defined %}<th>{{ "tdata/trolley_travel"|trans({}, 'across') }}</th>{% endif %}
        <th>{{ "tdata/slewing"|trans({}, 'across') }}</th>
        <th>{{ "tdata/operation"|trans({}, 'across') }}</th>
        <th>{{ "tdata/mounting"|trans({}, 'across') }}</th>
    </tr>

    <tr>
        <td valign="top">{% for h in hoist %}{{ h|trans({}, 'across') }}{% endfor %}</td>
        {% if trolley_travel is defined %}<td valign="top">{% for t in trolley_travel %}{{ t|trans({}, 'across') }}{% endfor %}</td>{% endif %}
        <td valign="top">{% for s in slewing %}{{ s|trans({}, 'across') }}{% endfor %}</td>
        <td valign="top">{% for o in operation %}{{ o|trans({}, 'across') }}{% endfor %}</td>
        <td valign="top">{% for m in mounting %}<li>{{ m|trans({}, 'across') }}</li>{% endfor %}</td>
    </tr>

</table>

