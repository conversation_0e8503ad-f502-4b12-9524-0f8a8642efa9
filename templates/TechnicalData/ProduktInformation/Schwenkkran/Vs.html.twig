<div class="card card-closed mb-3">
    <div class="card-header text-black productBox">
        <div class="image"><img src="{{ asset('dist/images/technicalData/vs.png') }}"></div>
        <div class="product">{{ "tdata/pillar_jib_crane_vs"|trans({}, 'across') }}</div>
        <div class="card-controls card-controls-right">
            <a href="#" class="toggle card-minimize">
                <i class="fa-angle-up fa"></i>
            </a>
        </div>
    </div>
    <div class="card-body">

        <div class="card card-closed mb-3">
            <div class="card-header text-black bg-abus-yellow-dark">
                {{ ["tdata/pillar_jib_crane_vs"|trans({}, 'across'), " <strong>", "tdata/with_abus_electric_chain_hoist"|trans({}, 'across'), "</strong>"]|join|raw }}
                <div class="card-controls card-controls-right">
                    <a href="#" class="toggle card-minimize">
                        <i class="fa-angle-up fa"></i>
                    </a>
                </div>
            </div>
            <div class="card-body bg-white">

                {% set postContent %}
                    <hr>
                    <div class="product">
                        {% include 'TechnicalData/ProduktInformation/Hebezeug.html.twig' with {
                            'hoist': {
                                0: 'tdata/electric_chain_hoist'
                            },
                            'trolley_travel': {
                                0: 'tdata/manual_or_electrical'
                            },
                            'slewing': {
                                0: 'tdata/manual_or_electrical'
                            },
                            'operation': {
                                0: 'tdata/via_push_button_pendant,_mounted_on_the_trolley_or_mobile_on_separate_rail'
                            },
                            'mounting': {
                                0: 'tdata/foundation_with_anchor_bolts',
                                1: 'tdata/dowel_plate'
                            }
                        } %}
                    </div>
                {% endset %}

                <div class="product">
                    {% include 'TechnicalData/ProduktInformation/Produkt.html.twig' with {
                    'maxValue': [4000, 10],
                    'prefix': ["tdata/up_to"|trans({}, 'across'), ''],
                    'suffix': ["tdata/load_capacity"|trans({}, 'across'), "tdata/max_jib_length"|trans({}, 'across')],
                    'unit': ['kg', 'm'],
                    'values': [
                        [2500, 10],
                        [3200, 9],
                        [4000, 8]
                    ],
                    'postContent': postContent
                    } %}
                </div>
            </div>
        </div>


        <div class="card card-closed mb-3">
            <div class="card-header text-black bg-abus-yellow-dark">
                {{ ["tdata/pillar_jib_crane_vs"|trans({}, 'across'), " <strong>", "tdata/with_abus_electric_wire_rope_hoist"|trans({}, 'across'), "</strong>"]|join|raw }}
                <div class="card-controls card-controls-right">
                    <a href="#" class="toggle card-minimize">
                        <i class="fa-angle-up fa"></i>
                    </a>
                </div>
            </div>
            <div class="card-body bg-white">

                {% set postContent %}
                    <hr>
                    <div class="product">
                        {% include 'TechnicalData/ProduktInformation/Hebezeug.html.twig' with {
                            'hoist': {
                                0: 'tdata/electric_wire_rope_hoist'
                            },
                            'trolley_travel': {
                                0: 'tdata/electrical'
                            },
                            'slewing': {
                                0: 'tdata/electrical'
                            },
                            'operation': {
                                0: 'tdata/via_push_button_pendant,_mobile_on_separate_rail'
                            },
                            'mounting': {
                                0: 'tdata/foundation_with_anchor_bolts',
                                1: 'tdata/dowel_plate'
                            }
                        } %}
                    </div>
                {% endset %}

                <div class="product">
                    {% include 'TechnicalData/ProduktInformation/Produkt.html.twig' with {
                        'maxValue': [6300, 10],
                        'prefix': ["tdata/up_to"|trans({}, 'across'), ''],
                        'suffix': ["tdata/load_capacity"|trans({}, 'across'), "tdata/max_jib_length"|trans({}, 'across')],
                        'unit': ['kg', 'm'],
                        'values': [
                            [2500, 10],
                            [3200, 9],
                            [4000, 8],
                            [5000, 7],
                            [6300, 5],
                        ],
                        'postContent': postContent
                    } %}
                </div>

            </div>
        </div>

        <div style="width: 100%; text-align: center; margin-bottom: 10px;">
            <a href="{{ "tdata/link_pillar-jib-crane-vs"|trans({}, 'across') }}" target="_blank">{{ "tdata/please_visit_website"|trans({}, 'across') }}</a>
        </div>

        <div class="infobox">
            {{ "tdata/in_the_case_you_do_not_find_the_cran_type_or_the_swl_with_the_corresponding_jib_length_in_the_portal"|trans({}, 'across')|raw }}
        </div>

    </div>
</div>
