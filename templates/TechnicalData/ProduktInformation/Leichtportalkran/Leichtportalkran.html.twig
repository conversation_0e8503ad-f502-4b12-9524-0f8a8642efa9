<div class="card mb-3">
    <div class="card-header text-black productBox">
        <div class="image"><img src="{{ asset('dist/images/technicalData/lpk.png') }}"></div>
        <div class="product">{{ "tdata/mobile_gantry_cranes"|trans({}, 'across') }}</div>
        <div class="card-controls card-controls-right">
            <a href="#" class="toggle card-minimize">
                <i class="fa-angle-up fa"></i>
            </a>
        </div>
    </div>

    <div class="card-body">
        <div class="card mb-3">
            <div class="card-body bg-white">
                <div class="product">
                    {% include 'TechnicalData/ProduktInformation/Produkt.html.twig' with {
                    'maxValue': [2000, 7900],
                    'prefix': ["tdata/up_to"|trans({}, 'across'), ''],
                    'suffix': ["tdata/load_capacity"|trans({}, 'across'), "tdata/max_total_width"|trans({}, 'across')],
                    'unit': ['kg', 'mm'],
                    'values': [
                        [250, 7900],
                        [500, 7000],
                        [800, 6500],
                        [1000, 6300],
                        [1250, 5650],
                        [1600, 5000],
                        [2000, 4000]
                    ]
                    } %}
                </div>
            </div>
        </div>

        <div style="width: 100%; text-align: center; margin-bottom: 10px;">
            <a href="{{ "tdata/link_lightweight-mobile-gantry"|trans({}, 'across') }}" target="_blank">{{ "tdata/please_visit_website"|trans({}, 'across') }}</a>
        </div>

        <div class="infobox">
            {{ "tdata/in_the_case_you_do_not_find_the_crane_type_or_the_swl_with_the_corresponding_total_width_in_the_portal"|trans({}, 'across')|raw }}
        </div>
    </div>
</div>
