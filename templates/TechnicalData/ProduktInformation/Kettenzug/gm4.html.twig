<div class="card card-closed mb-3">
    <div class="card-header text-black productBox">
        <div class="image"><img src="{{ asset('dist/images/technicalData/gm4.png') }}"></div>
        <div class="product">{{ "tdata/electric_chain_hoist"|trans({}, 'across') }} - GM4</div>
        <div class="card-controls card-controls-right">
            <a href="#" class="toggle card-minimize">
                <i class="fa-angle-up fa"></i>
            </a>
        </div>
    </div>
    <div class="card-body">

        <div class="card mb-3">
            <div class="card-body bg-white">
                <div class="product">
                    {% include 'TechnicalData/ProduktInformation/Kettenzug.html.twig' with {
                        'hoistSize': {
                            'GM4': {
                                'loadCapacity': {
                                    '250': {
                                        'lifting': 20,
                                        'hookPath': {
                                            0: 50
                                        }
                                    },
                                    '320': {
                                        'lifting': 16,
                                        'hookPath': {
                                            0: 50
                                        }
                                    },
                                    '400': {
                                        'lifting': 12,
                                        'hookPath': {
                                            0: 50
                                        }
                                    },
                                    '500': {
                                        'lifting': 10,
                                        'hookPath': {
                                            0: 50
                                        }
                                    },
                                    '630': {
                                        'lifting': 8,
                                        'hookPath': {
                                            0: 25
                                        }
                                    },
                                    '800': {
                                        'lifting': 6,
                                        'hookPath': {
                                            0: 25
                                        }
                                    },
                                    '1000': {
                                        'lifting': 5,
                                        'hookPath': {
                                            0: 25
                                        }
                                    },
                                    '1250': {
                                        'lifting': 4,
                                        'hookPath': {
                                            0: 25
                                        }
                                    }
                                }
                            }
                        }
                    } %}
                </div>
            </div>
        </div>

        <div style="width: 100%; text-align: center; margin-bottom: 10px;">
            <a href="{{ "tdata/link_gm4"|trans({}, 'across') }}" target="_blank">{{ "tdata/please_visit_website"|trans({}, 'across') }}</a>
        </div>

        <div class="infobox">
            {{ "tdata/further_executions_on_request"|trans({}, 'across')|raw }}
        </div>

    </div>
</div>
