<div class="card card-closed mb-3">
    <div class="card-header text-black productBox">
        <div class="image"><img src="{{ asset('dist/images/technicalData/gm8.png') }}"></div>
        <div class="product">{{ "tdata/electric_chain_hoist"|trans({}, 'across') }} - GM8</div>
        <div class="card-controls card-controls-right">
            <a href="#" class="toggle card-minimize">
                <i class="fa-angle-up fa"></i>
            </a>
        </div>
    </div>
    <div class="card-body">

        <div class="card mb-3">
            <div class="card-body bg-white">
                <div class="product">
                    {% include 'TechnicalData/ProduktInformation/Kettenzug.html.twig' with {
                        'hoistSize': {
                            'GM8': {
                                'loadCapacity': {
                                    '800': {
                                        'lifting': 20,
                                        'hookPath': {
                                            0: 32
                                        }
                                    },
                                    '1000': {
                                        'lifting': 16,
                                        'hookPath': {
                                            0: 32
                                        }
                                    },
                                    '1250': {
                                        'lifting': 12,
                                        'hookPath': {
                                            0: 32
                                        }
                                    },
                                    '1600': {
                                        'lifting': 10,
                                        'hookPath': {
                                            0: 32
                                        }
                                    },
                                    '2000': {
                                        'lifting': 8,
                                        'hookPath': {
                                            0: 32
                                        }
                                    },
                                    '2500': {
                                        'lifting': 6,
                                        'hookPath': {
                                            0: 16
                                        }
                                    },
                                    '3200': {
                                        'lifting': 5,
                                        'hookPath': {
                                            0: 16
                                        }
                                    },
                                    '4000': {
                                        'lifting': 4,
                                        'hookPath': {
                                            0: 16
                                        }
                                    }
                                }
                            }
                        }
                    } %}
                </div>
            </div>
        </div>

        <div style="width: 100%; text-align: center; margin-bottom: 10px;">
            <a href="{{ "tdata/link_gm8"|trans({}, 'across') }}" target="_blank">{{ "tdata/please_visit_website"|trans({}, 'across') }}</a>
        </div>

        <div class="infobox">
            {{ "tdata/further_executions_on_request"|trans({}, 'across')|raw }}
        </div>

    </div>
</div>
