<div class="card card-closed mb-3">
    <div class="card-header text-black productBox">
        <div class="image"><img src="{{ asset('dist/images/technicalData/gm2.png') }}"></div>
        <div class="product">{{ "tdata/electric_chain_hoist"|trans({}, 'across') }} - GM2</div>
        <div class="card-controls card-controls-right">
            <a href="#" class="toggle card-minimize">
                <i class="fa-angle-up fa"></i>
            </a>
        </div>
    </div>
    <div class="card-body">

        <div class="card mb-3">
            <div class="card-body bg-white">
                <div class="product">
                    {% include 'TechnicalData/ProduktInformation/Kettenzug.html.twig' with {
                        'hoistSize': {
                            'GM2': {
                                'loadCapacity': {
                                    '100': {
                                        'lifting': 20,
                                        'hookPath': {
                                            0: 32
                                        }
                                    },
                                    '125': {
                                        'lifting': 16,
                                        'hookPath': {
                                            0: 32
                                        }
                                    },
                                    '160': {
                                        'lifting': 12,
                                        'hookPath': {
                                            0: 32
                                        }
                                    },
                                    '200': {
                                        'lifting': 10,
                                        'hookPath': {
                                            0: 32
                                        }
                                    },
                                    '250': {
                                        'lifting': 8,
                                        'hookPath': {
                                            0: 32
                                        }
                                    },
                                    '320': {
                                        'lifting': 6,
                                        'hookPath': {
                                            0: 32
                                        }
                                    },
                                    '400': {
                                        'lifting': 5,
                                        'hookPath': {
                                            0: 16
                                        }
                                    },
                                    '500': {
                                        'lifting': 4,
                                        'hookPath': {
                                            0: 16
                                        }
                                    },
                                    '630': {
                                        'lifting': 3,
                                        'hookPath': {
                                            0: 16
                                        }
                                    }
                                }
                            }
                        }
                    } %}
                </div>
            </div>
        </div>

        <div style="width: 100%; text-align: center; margin-bottom: 10px;">
            <a href="{{ "tdata/link_gm2"|trans({}, 'across') }}" target="_blank">{{ "tdata/please_visit_website"|trans({}, 'across') }}</a>
        </div>

        <div class="infobox">
            {{ "tdata/further_executions_on_request"|trans({}, 'across')|raw }}
        </div>

    </div>
</div>
