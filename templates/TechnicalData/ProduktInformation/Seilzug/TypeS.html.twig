<div class="card card-closed mb-3">
    <div class="card-header text-black productBox">
        <div class="image"><img src="{{ asset('dist/images/technicalData/type_s.png') }}"></div>
        <div class="product">{{ "tdata/wire_rope_hoists"|trans({}, 'across') }} - {{ "tdata/type"|trans({}, 'across') }} S</div>
        <div class="card-controls card-controls-right">
            <a href="#" class="toggle card-minimize">
                <i class="fa-angle-up fa"></i>
            </a>
        </div>
    </div>
    <div class="card-body">

        <div class="card mb-3">
            <div class="card-body bg-white">
                <div class="product">
                    {% include 'TechnicalData/ProduktInformation/Seilzug.html.twig' with {
                        'hoistSize': {
                            'GM 800': {
                                'reeving': {
                                    0: {
                                        'value': '4/1',
                                        'loadCapacity': 3200,
                                        'hookPath': {
                                            0: 6000,
                                            1: 9000,
                                            2: ''
                                        }
                                    }
                                }
                            },
                            'GM 1000': {
                                'reeving': {
                                    0: {
                                        'value': '4/1',
                                        'loadCapacity': 5000,
                                        'hookPath': {
                                            0: 6000,
                                            1: 9000,
                                            2: ''
                                        }
                                    }
                                }
                            },
                            'GM 2000': {
                                'reeving': {
                                    0: {
                                        'value': '4/1',
                                        'loadCapacity': 6300,
                                        'hookPath': {
                                            0: 6000,
                                            1: 9000,
                                            2: ''
                                        }
                                    }
                                }
                            },
                            'GM 3000': {
                                'reeving': {
                                    0: {
                                        'value': '2/1',
                                        'loadCapacity': 5000,
                                        'hookPath': {
                                            0: 12000,
                                            1: 20000,
                                            2: ''
                                        }
                                    },
                                    1: {
                                        'value': '4/1',
                                        'loadCapacity': 10000,
                                        'hookPath': {
                                            0: 6000,
                                            1: 10000,
                                            2: 15000
                                        }
                                    }
                                }
                            }
                        }
                    } %}
                </div>
            </div>
        </div>

        <div style="width: 100%; text-align: center; margin-bottom: 10px;">
            <a href="{{ "tdata/link_type-s-side-mounted-hoist"|trans({}, 'across') }}" target="_blank">{{ "tdata/please_visit_website"|trans({}, 'across') }}</a>
        </div>

        <div class="infobox">
            {{ "tdata/further_executions_on_request"|trans({}, 'across')|raw }}
        </div>

    </div>
</div>
