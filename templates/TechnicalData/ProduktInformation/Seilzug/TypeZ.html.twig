<div class="card card-closed mb-3">
    <div class="card-header text-black productBox">
        <div class="image"><img src="{{ asset('dist/images/technicalData/type_z.png') }}"></div>
        <div class="product">{{ "tdata/wire_rope_hoists"|trans({}, 'across') }} - {{ "tdata/type"|trans({}, 'across') }} Z, ZA, ZB</div>
        <div class="card-controls card-controls-right">
            <a href="#" class="toggle card-minimize">
                <i class="fa-angle-up fa"></i>
            </a>
        </div>
    </div>
    <div class="card-body">

        <div class="card mb-3">
            <div class="card-body bg-white">
                <div class="product">
                    {% include 'TechnicalData/ProduktInformation/Seilzug.html.twig' with {
                        'type': true,
                        'hoistSize': {
                            'GM 5000': {
                                'type': 'Z',
                                'reeving': {
                                    0: {
                                        'value': '4/2',
                                        'loadCapacity': 20000,
                                        'hookPath': {
                                            0: 12000,
                                            1: 20000,
                                            2: 30000,
                                            3: 37000
                                        }
                                    },
                                    1: {
                                        'value': '8/2',
                                        'loadCapacity': 40000,
                                        'hookPath': {
                                            0: 6000,
                                            1: 10000,
                                            2: 15000,
                                            3: 18500
                                        }
                                    }
                                }
                            },
                            'GM 6000': {
                                'type': 'Z',
                                'reeving': {
                                    0: {
                                        'value': '4/2',
                                        'loadCapacity': 25000,
                                        'hookPath': {
                                            0: 12000,
                                            1: 20000,
                                            2: 30000,
                                            3: 37000
                                        }
                                    },
                                    1: {
                                        'value': '8/2',
                                        'loadCapacity': 50000,
                                        'hookPath': {
                                            0: 6000,
                                            1: 10000,
                                            2: 15000,
                                            3: 18500
                                        }
                                    }
                                }
                            },
                            'GM 7000': {
                                'type': 'Z',
                                'reeving': {
                                    0: {
                                        'value': '4/2',
                                        'loadCapacity': 40000,
                                        'hookPath': {
                                            0: 16000,
                                            1: 30000,
                                            2: 45000,
                                            3: ''
                                        }
                                    },
                                    1: {
                                        'value': '6/2',
                                        'loadCapacity': 63000,
                                        'hookPath': {
                                            0: 10600,
                                            1: 20000,
                                            2: 30000,
                                            3: 36000
                                        }
                                    },
                                    2: {
                                        'value': '8/2',
                                        'loadCapacity': 80000,
                                        'hookPath': {
                                            0: 8000,
                                            1: 15000,
                                            2: 22500,
                                            3: 27500
                                        }
                                    },
                                    3: {
                                        'value': '10/2',
                                        'loadCapacity': 100000,
                                        'hookPath': {
                                            0: 12000,
                                            1: 18000,
                                            2: 22000,
                                            3: ''
                                        }
                                    },
                                    4: {
                                        'value': '12/2',
                                        'loadCapacity': 120000,
                                        'hookPath': {
                                            0: 15000,
                                            1: '',
                                            2: '',
                                            3: ''
                                        }
                                    }
                                }
                            },
                        }
                    } %}
                </div>
            </div>
        </div>

        <div style="width: 100%; text-align: center; margin-bottom: 10px;">
            <a href="{{ "tdata/link_type-z-twin-barrel-crab-unit"|trans({}, 'across') }}" target="_blank">{{ "tdata/please_visit_website"|trans({}, 'across') }}</a>
        </div>

        <div class="infobox">
            {{ "tdata/further_executions_on_request"|trans({}, 'across')|raw }}
        </div>

    </div>
</div>
