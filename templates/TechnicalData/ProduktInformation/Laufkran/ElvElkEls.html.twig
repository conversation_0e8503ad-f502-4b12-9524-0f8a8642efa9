<div class="card card-closed mb-3">
    <div class="card-header text-black productBox">
        <div class="image"><img src="{{ asset('dist/images/technicalData/elv.png') }}"></div>
        <div class="product">{{ "tdata/single_girder_overhead_travelling_crane"|trans({}, 'across') }} - ELV / ELK / ELS</div>
        <div class="card-controls card-controls-right">
            <a href="#" class="toggle card-minimize">
                <i class="fa-angle-up fa"></i>
            </a>
        </div>
    </div>

    <div class="card-body">
        <div class="card card-closed mb-3">
            <div class="card-header text-black bg-abus-yellow-dark">
                {{ ['<strong>ELV</strong> ', "tdata/single_girder_travelling_crane_with_rolled_section_girder"|trans({}, 'across')]|join|raw }}
                <div class="card-controls card-controls-right">
                    <a href="#" class="toggle card-minimize">
                        <i class="fa-angle-up fa"></i>
                    </a>
                </div>
            </div>
            <div class="card-body bg-white">
                <div class="product">
                    {% include 'TechnicalData/ProduktInformation/Produkt.html.twig' with {
                        'maxValue': [16, 39],
                        'prefix': ["tdata/up_to"|trans({}, 'across'), ''],
                        'suffix': ["tdata/load_capacity"|trans({}, 'across'), "tdata/max_span"|trans({}, 'across')],
                        'unit': ['t', 'm'],
                        'values': [
                            [5, 18.5],
                            [6.3, 17.5],
                            [8, 17],
                            [10, 14.5]
                        ]
                    } %}
                </div>
            </div>
        </div>

        <div class="card card-closed mb-3">
            <div class="card-header text-black bg-abus-yellow-dark">
                {{ ['<strong>ELK</strong> ', "tdata/single_girder_travelling_crane_with_welded_box_girder"|trans({}, 'across')]|join|raw }}
                <div class="card-controls card-controls-right">
                    <a href="#" class="toggle card-minimize">
                        <i class="fa-angle-up fa"></i>
                    </a>
                </div>
            </div>
            <div class="card-body bg-white">
                <div class="product">
                    {% include 'TechnicalData/ProduktInformation/Produkt.html.twig' with {
                        'maxValue': [16, 39],
                        'prefix': ["tdata/up_to"|trans({}, 'across'), ''],
                        'suffix': ["tdata/load_capacity"|trans({}, 'across'), "tdata/max_span"|trans({}, 'across')],
                        'unit': ['t', 'm'],
                        'values': [
                            [5, 28.5],
                            [10, 26],
                            [16, 22],
                        ]
                    } %}
                </div>
            </div>
        </div>

        <div class="card card-closed mb-3">
            <div class="card-header text-black bg-abus-yellow-dark">
                {{ ['<strong>ELS</strong> ', "tdata/single_girder_torsion_box_crane_with_side-mounted_trolley"|trans({}, 'across')]|join|raw }}
                <div class="card-controls card-controls-right">
                    <a href="#" class="toggle card-minimize">
                        <i class="fa-angle-up fa"></i>
                    </a>
                </div>
            </div>
            <div class="card-body bg-white">
                <div class="product">
                    {% include 'TechnicalData/ProduktInformation/Produkt.html.twig' with {
                        'maxValue': [16, 39],
                        'prefix': ["tdata/up_to"|trans({}, 'across'), ''],
                        'suffix': ["tdata/load_capacity"|trans({}, 'across'), "tdata/max_span"|trans({}, 'across')],
                        'unit': ['t', 'm'],
                        'values': [
                            [6.3, 39],
                            [8, 34.5],
                            [10, 34],
                        ]
                    } %}
                </div>
            </div>
        </div>

        <div style="width: 100%; text-align: center; margin-bottom: 10px;">
            <a href="{{ "tdata/link_single-girder-overhead-travelling-crane"|trans({}, 'across') }}" target="_blank">{{ "tdata/please_visit_website"|trans({}, 'across') }}</a>
        </div>

        <div class="infobox">
            {{ "tdata/higher_swl_and_bigger_spans_on_request"|trans({}, 'across')|raw }}
        </div>
    </div>
</div>


{#<div class="panel panel-default">#}
    {#<div class="panel-heading">#}
        {#<h5 class="panel-title">#}
            {#<a class="accordion" data-toggle="collapse" data-parent="#accordion1" href="#collapseOne1">#}
                {#<div class="image"><img src="{{ asset('dist/images/technicalData/elv.png') }}"></div>#}
                {#<div class="product">{{ "tdata/single_girder_overhead_travelling_crane"|trans({}, 'across') }} - ELV / ELK / ELS</div>#}
            {#</a>#}
        {#</h5>#}
    {#</div>#}

    {#<div id="collapseOne1" class="panel-collapse collapse">#}
        {#<div class="panel-body">#}

            {#{% set content %}#}
                {#<div class="product">#}
                    {#{% include 'TechnicalData/ProduktInformation/Produkt.html.twig' with {#}
                        {#'maxValue': [16, 39],#}
                        {#'prefix': ["tdata/up_to"|trans({}, 'across'), ''],#}
                        {#'suffix': ["tdata/load_capacity"|trans({}, 'across'), "tdata/max_span"|trans({}, 'across')],#}
                        {#'unit': ['t', 'm'],#}
                        {#'values': [#}
                            {#[5, 18.5],#}
                            {#[6.3, 17.5],#}
                            {#[8, 17],#}
                            {#[10, 14.5]#}
                        {#]#}
                    {#} %}#}
                {#</div>#}
            {#{% endset %}#}

            {#{% include 'Frame/TwigTemplates/portlet.html.twig' with {#}
                {#'id': false,#}
                {#'class': 'warning bgABUSYellow',#}
                {#'heading': true,#}
                {#'iconClass': false,#}
                {#'topic': ['<strong>ELV</strong> ', "tdata/single_girder_travelling_crane_with_rolled_section_girder"|trans({}, 'across')]|join,#}
                {#'content': content,#}
                {#'footer': false,#}
                {#'iconRefresh': false,#}
                {#'iconToggle': true,#}
                {#'iconClose': false,#}
                {#'plain': false,#}
                {#'movable': false,#}
                {#'closedByDefault': false#}
            {#} only %}#}


            {#{% set content %}#}
                {#<div class="product">#}
                    {#{% include 'TechnicalData/ProduktInformation/Produkt.html.twig' with {#}
                    {#'maxValue': [16, 39],#}
                    {#'prefix': ["tdata/up_to"|trans({}, 'across'), ''],#}
                    {#'suffix': ["tdata/load_capacity"|trans({}, 'across'), "tdata/max_span"|trans({}, 'across')],#}
                    {#'unit': ['t', 'm'],#}
                    {#'values': [#}
                        {#[5, 28.5],#}
                        {#[10, 26],#}
                        {#[16, 22],#}
                    {#]#}
                    {#} %}#}
                {#</div>#}
            {#{% endset %}#}

            {#{% include 'Frame/TwigTemplates/portlet.html.twig' with {#}
                {#'id': false,#}
                {#'class': 'warning bgABUSYellow',#}
                {#'heading': true,#}
                {#'iconClass': false,#}
                {#'topic': ['<strong>ELK</strong> ', "tdata/single_girder_travelling_crane_with_welded_box_girder"|trans({}, 'across')]|join,#}
                {#'content': content,#}
                {#'footer': false,#}
                {#'iconRefresh': false,#}
                {#'iconToggle': true,#}
                {#'iconClose': false,#}
                {#'plain': false,#}
                {#'movable': false,#}
                {#'closedByDefault': false#}
            {#} only %}#}


            {#{% set content %}#}
                {#<div class="product">#}
                    {#{% include 'TechnicalData/ProduktInformation/Produkt.html.twig' with {#}
                    {#'maxValue': [16, 39],#}
                    {#'prefix': ["tdata/up_to"|trans({}, 'across'), ''],#}
                    {#'suffix': ["tdata/load_capacity"|trans({}, 'across'), "tdata/max_span"|trans({}, 'across')],#}
                    {#'unit': ['t', 'm'],#}
                    {#'values': [#}
                        {#[6.3, 39],#}
                        {#[8, 34.5],#}
                        {#[10, 34],#}
                    {#]#}
                    {#} %}#}
                {#</div>#}
            {#{% endset %}#}

            {#{% include 'Frame/TwigTemplates/portlet.html.twig' with {#}
                {#'id': false,#}
                {#'class': 'warning bgABUSYellow',#}
                {#'heading': true,#}
                {#'iconClass': false,#}
                {#'topic': ['<strong>ELS</strong> ', "tdata/single_girder_torsion_box_crane_with_side-mounted_trolley"|trans({}, 'across')]|join,#}
                {#'content': content,#}
                {#'footer': false,#}
                {#'iconRefresh': false,#}
                {#'iconToggle': true,#}
                {#'iconClose': false,#}
                {#'plain': false,#}
                {#'movable': false,#}
                {#'closedByDefault': false#}
            {#} only %}#}


            {#{% include 'Frame/TwigTemplates/portlet.html.twig' with {#}
                {#'id': false,#}
                {#'class': 'primary infobox',#}
                {#'heading': false,#}
                {#'iconClass': false,#}
                {#'topic': '',#}
                {#'content': "tdata/higher_swl_and_bigger_spans_on_request"|trans({}, 'across'),#}
                {#'footer': false,#}
                {#'iconRefresh': false,#}
                {#'iconToggle': false,#}
                {#'iconClose': false,#}
                {#'plain': true,#}
                {#'movable': false,#}
                {#'closedByDefault': false#}
            {#} only %}#}

        {#</div>#}
    {#</div>#}
{#</div>#}
