<div class="card card-closed mb-3">
    <div class="card-header text-black productBox">
        <div class="image"><img src="{{ asset('dist/images/technicalData/zlk.png') }}"></div>
        <div class="product">{{ "tdata/double_girder_overhead_travelling_crane"|trans({}, 'across') }} - ZLK</div>
        <div class="card-controls card-controls-right">
            <a href="#" class="toggle card-minimize">
                <i class="fa-angle-up fa"></i>
            </a>
        </div>
    </div>
    <div class="card-body">

        <div class="card mb-3">
            <div class="card-header text-black bg-abus-yellow-dark">
                {{ ['<strong>ZLK</strong> ', "tdata/double_girder_travelling_crane_with_welded_box_girder"|trans({}, 'across')]|join|raw }}
                <div class="card-controls card-controls-right">
                    <a href="#" class="toggle card-minimize">
                        <i class="fa-angle-up fa"></i>
                    </a>
                </div>
            </div>
            <div class="card-body bg-white">
                <div class="product">
                    {% include 'TechnicalData/ProduktInformation/Produkt.html.twig' with {
                    'maxValue': [100, 40],
                    'prefix': ["tdata/up_to"|trans({}, 'across'), ''],
                    'suffix': ["tdata/load_capacity"|trans({}, 'across'), "tdata/max_span"|trans({}, 'across')],
                    'unit': ['t', 'm'],
                    'values': [
                        [40, 40],
                        [50, 33],
                        [100, 30],
                        ]
                    } %}
                </div>
            </div>
        </div>

        <div style="width: 100%; text-align: center; margin-bottom: 10px;">
            <a href="{{ "tdata/link_double-girder-overhead-travelling-crane"|trans({}, 'across') }}" target="_blank">{{ "tdata/please_visit_website"|trans({}, 'across') }}</a>
        </div>

        <div class="infobox">
            {{ "tdata/higher_swl_and_bigger_spans_on_request"|trans({}, 'across')|raw }}
        </div>
    </div>
</div>
