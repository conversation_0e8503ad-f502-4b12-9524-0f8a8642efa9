<div class="card card-closed mb-3">
    <div class="card-header text-black productBox">
        <div class="image"><img src="{{ asset('dist/images/technicalData/dlvm.png') }}"></div>
        <div class="product">{{ "tdata/underslung_overhead_travelling_crane"|trans({}, 'across') }} - DLVM / EDL / EDK</div>
        <div class="card-controls card-controls-right">
            <a href="#" class="toggle card-minimize">
                <i class="fa-angle-up fa"></i>
            </a>
        </div>
    </div>
    <div class="card-body">

        <div class="card card-closed mb-3">
            <div class="card-header text-black bg-abus-yellow-dark">
                {{ ['<strong>DLVM</strong> ', "tdata/underslung_travelling_crane_with_rolled_section_girder_and_welded_main_girder_connection"|trans({}, 'across')]|join|raw }}
                <div class="card-controls card-controls-right">
                    <a href="#" class="toggle card-minimize">
                        <i class="fa-angle-up fa"></i>
                    </a>
                </div>
            </div>
            <div class="card-body bg-white">
                <div class="product">
                    {% include 'TechnicalData/ProduktInformation/Produkt.html.twig' with {
                        'maxValue': [8, 25],
                        'prefix': ["tdata/up_to"|trans({}, 'across'), ''],
                        'suffix': ["tdata/load_capacity"|trans({}, 'across'), "tdata/max_span"|trans({}, 'across')],
                        'unit': ['t', 'm'],
                        'values': [
                            [3.2, 14],
                        ]
                    } %}
                </div>
            </div>
        </div>

        <div class="card card-closed mb-3">
            <div class="card-header text-black bg-abus-yellow-dark">
                {{ ['<strong>EDL</strong> ', "tdata/underslung_travelling_crane_with_rolled_section_girder_and_bolted_main_girder_connection"|trans({}, 'across')]|join|raw }}
                <div class="card-controls card-controls-right">
                    <a href="#" class="toggle card-minimize">
                        <i class="fa-angle-up fa"></i>
                    </a>
                </div>
            </div>
            <div class="card-body bg-white">
                <div class="product">
                    {% include 'TechnicalData/ProduktInformation/Produkt.html.twig' with {
                        'maxValue': [8, 25],
                        'prefix': ["tdata/up_to"|trans({}, 'across'), ''],
                        'suffix': ["tdata/load_capacity"|trans({}, 'across'), "tdata/max_span"|trans({}, 'across')],
                        'unit': ['t', 'm'],
                        'values': [
                            [5, 17.5],
                            [6.3, 17],
                            [8, 9],
                        ]
                    } %}
                </div>
            </div>
        </div>

        <div class="card card-closed mb-3">
            <div class="card-header text-black bg-abus-yellow-dark">
                {{ ['<strong>EDK</strong> ', "tdata/underslung_travelling_crane_with_box_girder_and_bolted_main_girder_connection"|trans({}, 'across')]|join|raw }}
                <div class="card-controls card-controls-right">
                    <a href="#" class="toggle card-minimize">
                        <i class="fa-angle-up fa"></i>
                    </a>
                </div>
            </div>
            <div class="card-body bg-white">
                <div class="product">
                    {% include 'TechnicalData/ProduktInformation/Produkt.html.twig' with {
                        'maxValue': [8, 25],
                        'prefix': ["tdata/up_to"|trans({}, 'across'), ''],
                        'suffix': ["tdata/load_capacity"|trans({}, 'across'), "tdata/max_span"|trans({}, 'across')],
                        'unit': ['t', 'm'],
                        'values': [
                            [6.3, 25],
                            [8, 13]
                        ]
                    } %}
                </div>
            </div>
        </div>

        <div style="width: 100%; text-align: center; margin-bottom: 10px;">
            <a href="{{ "tdata/link_underslung-overhead-travelling-crane"|trans({}, 'across') }}" target="_blank">{{ "tdata/please_visit_website"|trans({}, 'across') }}</a>
        </div>

        <div class="infobox">
            {{ "tdata/higher_swl_and_bigger_spans_on_request"|trans({}, 'across')|raw }}
        </div>

    </div>
</div>
