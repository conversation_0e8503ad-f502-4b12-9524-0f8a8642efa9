{% for value in values %}
    {% set width = (70 / maxValue[0] ) * value[0] %}
        <div class="bar" style="width: {{ width }}%; white-space: nowrap;">{{ prefix[0] }} <strong>{{ value[0] }} {{ unit[0] }}</strong> {{ suffix[0] }}</div>
        <div class="second">{{ prefix[1] }} <strong>{{ value[1] }} {{ unit[1] }}</strong> {{ suffix[1] }}</div>
{% endfor %}

{% if postContent is defined %}{{ postContent }}{% endif %}
