<table class="table table-striped" style="margin-bottom: 0">

    <tr>
        {% if type is defined and type == true %}<th>{{ "tdata/type"|trans({}, 'across') }}</th>{% endif %}
        <th>{{ "tdata/hoist_size"|trans({}, 'across') }}</th>
        <th>{{ "tdata/reeving"|trans({}, 'across') }}</th>
        <th>{{ "tdata/load_capacity"|trans({}, 'across') }}</th>
        <th colspan="4">{{ "tdata/hook_path"|trans({}, 'across') }}</th>
    </tr>

    {% set first = true %}
    {% for key, hs in hoistSize %}

        {% set index = 0 %}
        {% for reeving in hs.reeving %}

            <tr {% if first == false and index == 0 %}style="border-top: #585858 2px solid;"{% endif %}>
                {% if hs.type is defined %}<td>{% if index == 0 %}{{ hs.type }}{% endif %}</td>{% endif %}
                <td>{% if index == 0 %}{{ key|replace({'(invisible)': ''}) }}{% endif %}</td>
                <td>{{ reeving.value }}</td>
                <td>{{ reeving.loadCapacity }} kg</td>

                {% for hp in reeving.hookPath %}
                    <td>{{ hp }} {% if hp != "" %}mm{% endif %}</td>
                {% endfor %}

            </tr>
            {% set index = 1 %}
            {% set first = false %}
        {% endfor %}

    {% endfor %}

</table>
