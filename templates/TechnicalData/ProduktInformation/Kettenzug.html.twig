<table class="table table-striped" style="margin-bottom: 0">

    <tr>
        <th>{{ "tdata/hoist_size"|trans({}, 'across') }}</th>
        <th>{{ "tdata/load_capacity"|trans({}, 'across') }}</th>
        <th>{{ "tdata/lifting"|trans({}, 'across') }}</th>
        <th>{{ "tdata/hook_path"|trans({}, 'across') }}</th>
    </tr>

    {% set first = true %}
    {% for key, hs in hoistSize %}

        {% set index = 0 %}
        {% for lKey, loadCapacity in hs.loadCapacity %}

            <tr {% if first == false and index == 0 %}style="border-top: #585858 2px solid;"{% endif %}>
                <td>{% if index == 0 %}{{ key }}{% endif %}</td>
                <td>{{ lKey }} kg</td>
                <td>{{ "tdata/up_to"|trans({}, 'across') }} {{ loadCapacity.lifting }} m/min</td>

                {% for hp in loadCapacity.hookPath %}
                    <td>{{ hp }} {% if hp != "" %}m{% endif %}</td>
                {% endfor %}

            </tr>
            {% set index = 1 %}
        {% endfor %}


    {% endfor %}

</table>
