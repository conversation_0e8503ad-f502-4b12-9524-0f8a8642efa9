{% extends 'Frame/Layout/layout.html.twig' %}

{% block title %} {{ "tdata/technical_data"|trans({}, 'across') }} | ABUS Kransysteme GmbH{% endblock %}

{% block javascripts %}
    {{ parent() }}
    {{ vite_script('assets/protected/technicalData/technicalData.ts') }}
{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    {{ vite_css('assets/protected/technicalData/technicalData.ts') }}
{% endblock %}


{% block administration %}
    {{ parent() }}

    <div class="sidebar-panel">
        <h5 class="sidebar-panel-title">{{ "tdata/admin/administration"|trans({}, 'across') }}</h5>
    </div>

    <div class="content">
        <select class="form-control" name="preview" {% if preview %}data-initial-value="1"{% else %}data-initial-value="0"{% endif %}>
            <option value="0" {% if not preview %}selected="selected"{% endif %}>{{ "tdata/admin/live_database"|trans({}, 'across') }}</option>
            <option value="1" {% if preview %}selected="selected"{% endif %}>{{ "tdata/admin/preview_database"|trans({}, 'across') }}</option>
        </select>

        <br />

        <button type="button" class="btn btn-primary btn-block mr5" data-toggle="modal" data-target="#indexModal">{{ "tdata/admin/index_database"|trans({}, 'across') }}</button>
    </div>
{% endblock %}


{% block content %}

    {% if preview is not defined or preview == '' %}
        {% set preview = 0 %}
    {% endif %}

    <div id="tdata" data-product="{{ product }}" data-preview="{{ preview }}">

        {% if preview %}
            <div class="alert alert-warning mt20">
                <i class="fa fa-exclamation-triangle alert-icon "></i>&nbsp;&nbsp;&nbsp;
                {{ "tdata/admin/watching_preview"|trans({}, 'across') }}
            </div>
        {% endif %}

        {% block tdataContent %}{% endblock %}

        {% block tdataResults %}

            <div id="initialLoading" style="display: none;"></div>

            <div class="tdataResults" style="display: none;">

                <div class="card mb-3">
                    <div class="card-body bg-white">

                        <div class="alert alert-info" style="display: none;">
                            <strong></strong> {{ "tdata/matching_records_found"|trans({}, 'across') }}
                        </div>

                        <div class="tdataProductsWrapper">
                            <table id="table-ajax-defer" class="table table-striped table-bordered resultTable" cellspacing="0" width="100%">
                                <thead>
                                <tr>
                                    <th></th>
                                    <!-- Headers will be populated by JavaScript -->
                                </tr>
                                </thead>

                                <tbody>
                                    <!-- Results will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>

                    </div>
                </div>

            </div>
        {% endblock %}

        <div class="modal fade" id="indexModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal">
                            <span aria-hidden="true">&times;</span><span class="sr-only">Close</span>
                        </button>
                        <h4 class="modal-title" id="mySmallModalLabel">{{ "tdata/admin/index_database"|trans({}, 'across') }}</h4>
                    </div>
                    <div class="modal-body">
                        <p>Beim Indizieren der Datenbank werden die Hilfstabellen für die Auswahlfelder neu erstellt. Diese werden zum Optimieren der Seitengeschwindigkeit benötigt.</p>
                        <p style="font-weight: bold;">Die Datenbank muss neu indiziert werden, wenn sich etwas an den zugrundeliegenden Daten geändert hat.</p>

                        <p style="font-size: 16px; background-color: #53c200; padding: 10px; color: #ffffff; display: none;">Indizierung läuft ... <b>Fenster schließt automatisch und Seite wird neu geladen.</b></p>
                        <p style="font-size: 16px; background-color: #ff4f00; padding: 10px; color: #ffffff; display: none;">{{ "tdata/admin/error_occured"|trans({}, 'across') }}: <span class="error-message"></span></p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">{{ "frame/cancel"|trans({}, 'across') }}</button>
                        <button type="button" class="btn btn-success">{{ "tdata/admin/index_database"|trans({}, 'across') }}</button>
                    </div>
                </div>
            </div>
        </div>

    </div>

{% endblock %}

{% block custom_javascripts %}{% endblock %}

{% block footer %}{% endblock %}
