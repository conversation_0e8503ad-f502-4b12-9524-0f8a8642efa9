{% set showPageHeader = true %}

{% extends 'TechnicalData/tdataBaseFrame.html.twig' %}

{% block headline %}{{ "tdata/technical_data"|trans({}, 'across') }}{% endblock %}
{% block headline_description %}{{ "HB-systems"|trans({}, 'across') }}{% endblock %}
{% block headline_backbutton %}{% endblock %}

{% block tdataContent %}

    <div class="mt-3"></div>

    {% if user.werksvertretung != false %}
        <div class="row mb-3">
            <div class="col-md-12 col-lg-6 mb-3">
                {% include 'TechnicalData/KontaktInformation/ABUS.html.twig' %}
            </div>
            <div class="col-md-12 col-lg-6 mb-3">
                {% include 'TechnicalData/KontaktInformation/website_hb-systems.html.twig' %}
            </div>
        </div>
    {% endif %}

    {#<box-frontend start-folder="{{ "tdata/technische_daten/hb_system"|trans({}, 'across') }}" :is-admin="false" :inline-search="true"></box-frontend>#}

    <nextcloud start-folder="{{ "tdata/technische_daten/hb_system"|trans({}, 'across') }}" :is-admin="false" :inline-search="true"></nextcloud>


{% endblock %}


