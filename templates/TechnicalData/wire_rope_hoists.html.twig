{% set showPageHeader = true %}

{% extends 'TechnicalData/tdataBaseFrame.html.twig' %}

{% block headline %}{{ "tdata/technical_data"|trans({}, 'across') }}{% endblock %}
{% block headline_description %}{{ "tdata/wire_rope_hoists"|trans({}, 'across') }}{% endblock %}
{% block headline_backbutton %}{% endblock %}

{% block tdataContent %}

    <div class="row mt-3 mb-3">
        <div class="col">

            <div class="card card-closed mt-3">
                <div class="card-header text-white bg-abus-blue-light">
                    <i class="fa fa-th-large"></i>
                    {{ 'tdata/crane_selection_help'|trans({}, 'across') }}
                    <div class="card-controls card-controls-right">
                        <a href="#" class="toggle card-minimize">
                            <i class="fa-angle-up fa"></i>
                        </a>
                    </div>
                </div>
                <div class="card-body bg-white">
                    {% include 'TechnicalData/ProduktInformation/Seilzug/TypeE.html.twig' %}
                    {% include 'TechnicalData/ProduktInformation/Seilzug/TypeS.html.twig' %}
                    {% include 'TechnicalData/ProduktInformation/Seilzug/TypeU.html.twig' %}
                    {% include 'TechnicalData/ProduktInformation/Seilzug/TypeD.html.twig' %}
                    {% include 'TechnicalData/ProduktInformation/Seilzug/TypeZ.html.twig' %}

                    {#<box-frontend start-folder="{{ "tdata/technische_daten/wire_rope_hoist"|trans({}, 'across') }}" :is-admin="false" :inline-search="false"></box-frontend>#}
                    <nextcloud start-folder="{{ "tdata/technische_daten/wire_rope_hoist"|trans({}, 'across') }}" :is-admin="false" :inline-search="false"></nextcloud>
                </div>
            </div>
        </div>
    </div>

    {% if user.werksvertretung != false %}
        <div class="row mb-3">
            <div class="col-md-12 col-lg-6 mb-3">
                {% include 'TechnicalData/KontaktInformation/ABUS.html.twig' %}
            </div>
            <div class="col-md-12 col-lg-6 mb-3">
                {% include 'TechnicalData/KontaktInformation/website_wire_rope_hoists.html.twig' %}
            </div>
        </div>
    {% endif %}

    <div class="card mb-3">
        <div class="card-body bg-white selectionPortlet">
            <form action="" method="post" enctype="text/plain">
                <div class="form-group form-row">
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'Type', 'showname': 'Type_', 'options': tdata.getSelectValues('tune_wire_rope_hoists_crane_type') } only %}
                    <div class="d-none d-lg-block col-lg-2"></div>
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'Drive', 'suffix': 'm/min', 'options': tdata.getSelectValues('tune_wire_rope_hoists_drive') } only %}
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'Hoist size', 'prefix': 'GM ', 'options': tdata.getSelectValues('tune_wire_rope_hoists_hoist_size') } only %}
                    <div class="d-none d-lg-block col-lg-2"></div>
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'fem_iso', 'showname': 'FEM/ISO', 'options': tdata.getSelectValues('tune_wire_rope_hoists_femiso'), 'info': 'tdata/fem_hint' } only %}
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'Load capacity', 'suffix': 'kg', 'options': tdata.getSelectValues('tune_wire_rope_hoists_load_capacity') } only %}
                    <div class="d-none d-lg-block col-lg-2"></div>
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'Reeving', 'options': tdata.getSelectValues('tune_wire_rope_hoists_reeving') } only %}
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'Hook path', 'suffix': 'mm', 'options': tdata.getSelectValues('tune_wire_rope_hoists_hw'), 'info': 'tdata/hook_path_hint' } only %}
                    <div class="d-none d-lg-block col-lg-2"></div>
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'Track', 'suffix': 'mm', 'options': [{'value' : 'min'}]|merge(tdata.getSelectValues('tune_wire_rope_hoists_track' )), 'info': 'tdata/track_hint' } only %}
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'Lifting', 'suffix': 'm/min', 'options': tdata.getSelectValues('tune_wire_rope_hoists_lifting') } only %}
                    <div class="d-none d-lg-block col-lg-2"></div>
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'Operation voltage', 'suffix': 'V/Hz', 'options': tdata.getSelectValues('tune_wire_rope_hoists_voltage') } only %}
                </div>
            </form>
        </div>
        <div class="card-footer text-muted">
            {{ ['<center>', "tdata/please_select_three"|trans({}, 'across'), '</center>']|join|raw }}
        </div>
    </div>

{% endblock %}
