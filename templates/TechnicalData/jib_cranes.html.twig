{% set showPageHeader = true %}

{% extends 'TechnicalData/tdataBaseFrame.html.twig' %}

{% block headline %}{{ "tdata/technical_data"|trans({}, 'across') }}{% endblock %}
{% block headline_description %}{{ "tdata/jib_cranes"|trans({}, 'across') }}{% endblock %}
{% block headline_backbutton %}{% endblock %}

{% block tdataContent %}

    <div class="row mt-3 mb-3">
        <div class="col">

            <div class="card card-closed mt-3">
                <div class="card-header text-white bg-abus-blue-light">
                    <i class="fa fa-th-large"></i>
                    {{ 'tdata/crane_selection_help'|trans({}, 'across') }}
                    <div class="card-controls card-controls-right">
                        <a href="#" class="toggle card-minimize">
                            <i class="fa-angle-up fa"></i>
                        </a>
                    </div>
                </div>
                <div class="card-body bg-white">
                    {% include 'TechnicalData/ProduktInformation/Schwenkkran/Ls.html.twig' %}
                    {% include 'TechnicalData/ProduktInformation/Schwenkkran/Lsx.html.twig' %}
                    {% include 'TechnicalData/ProduktInformation/Schwenkkran/Vs.html.twig' %}
                    {% include 'TechnicalData/ProduktInformation/Schwenkkran/Lw.html.twig' %}
                    {% include 'TechnicalData/ProduktInformation/Schwenkkran/Lwx.html.twig' %}
                    {% include 'TechnicalData/ProduktInformation/Schwenkkran/Vw.html.twig' %}
                </div>
            </div>
        </div>
    </div>

    {% if user.werksvertretung != false %}
        <div class="row mb-3">
            <div class="col-md-12 col-lg-6 mb-3">
                {% include 'TechnicalData/KontaktInformation/ABUS.html.twig' %}
            </div>
            <div class="col-md-12 col-lg-6 mb-3">
                {% include 'TechnicalData/KontaktInformation/website_jib_cranes.html.twig' %}
            </div>
        </div>
    {% endif %}

    <div class="card mb-3">
        <div class="card-body bg-white selectionPortlet">
            <form action="" method="post" enctype="text/plain">
                <div class="form-group form-row">
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'crane_type', 'options': tdata.getSelectValues('tune_jib_cranes_crane_type') } only %}
                    <div class="d-none d-lg-block col-lg-2"></div>
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'Lifting', 'suffix': 'm/min', 'options': tdata.getSelectValues('tune_jib_cranes_lifting') } only %}
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'Load capacity', 'suffix': 'kg', 'options': tdata.getSelectValues('tune_jib_cranes_load_capacity') } only %}
                    <div class="d-none d-lg-block col-lg-2"></div>
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'Drive', 'suffix': 'm/min', 'options': tdata.getSelectValues('tune_jib_cranes_drive') } only %}
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'Jib length', 'suffix': 'mm', 'options': tdata.getSelectValues('tune_jib_cranes_length') } only %}
                    <div class="d-none d-lg-block col-lg-2"></div>
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'Slewing', 'suffix': '', 'options': tdata.getSelectValues('tune_jib_cranes_slewing') } only %}
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'Height of lower edge of jib', 'suffix': 'mm', 'options': tdata.getSelectValues('tune_jib_cranes_ukdim') } only %}
                    <div class="d-none d-lg-block col-lg-2"></div>
                    {% include "TechnicalData/selectBootstrap.html.twig" with { 'name': 'Mounting', 'suffix': '', 'options': tdata.getSelectValues('tune_jib_cranes_mounting') } only %}
                </div>
            </form>
        </div>
        <div class="card-footer text-muted">
            {{ ['<center>', "tdata/please_select_three"|trans({}, 'across'), '</center>']|join|raw }}
        </div>
    </div>

{% endblock %}
