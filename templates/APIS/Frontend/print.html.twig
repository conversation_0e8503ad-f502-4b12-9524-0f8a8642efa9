<!DOCTYPE html>
<html>

<head>
    <title>{{ informationBuilder.sectionPrefix }} - {{ informationBuilder.information.number }} {{ informationBuilder.information.title|striptags }}</title>
    <meta charset="UTF-8">

    <style>
        body {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 10pt;
            margin: 0 0 0 20px;
            padding: 10px;
            width: 910px;
        }

        img {
            max-width: 610px;
        }

        div.header div.logo {
            padding-top: 10px;
        }

        div.header div.logo img {
            width: 160px;
            float: right;
        }

        div.header div.meta {
            clear: both;
            padding-top: 5px;
            float: right;
            font-size: 10pt;
        }

        div.content {
            clear: both;
            text-align: justify;
            font-size: 10pt;
            padding-top: 10px;
        }

        div.content span {
            font-size: 10pt !important;
        }

        div.content a {
            text-decoration: none;
        }

        div.dokumente table {
            margin-left: -3px;
            padding: 0;
        }

        div.footer div {
            height: 65px;
            float: left;
            border: #999999 1px solid;
            background-color: #eeeeee;
            font-size: 8pt;
            padding: 2px;
            margin-bottom: 5px;
        }

        div.footer div.address {
            width: 220px;
            padding-left: 5px;
        }

        div.footer div.name {
            border-left: 0;
            width: 500px;
            font-size: 15pt;
            line-height: 65px;
            text-align: center;
        }

        div.footer div.number {
            border-left: 0;
            width: 170px;
            font-size: 17pt;
            line-height: 65px;
            text-align: center;
        }

    </style>
</head>

<body class="print">

    <div class="header">

        <div class="logo">
            <img src="{{ asset('dist/images/abus/logos/abus_logo_mehr_bewegen_rgb.png') }}" />
{#            <img src="file:///var/www/html/assets/public/{{ 'images/abus/logos/abus_logo_mehr_bewegen_rgb.png' }}" />#}
        </div>

        <div class="meta">
            {{ "apis/gummersbach"|trans({}, 'across', locale) }}, {{ informationBuilder.information.releasedate|format_datetime('medium', 'none', locale) }}<br />
            {{ "apis/responsible_for_content_and_wording"|trans({}, 'across', locale) }}: {{ informationBuilder.information.author }}<br />
            {{ "apis/email"|trans({}, 'across', locale) }}: {{ informationBuilder.information.authorEmail }}
        </div>

    </div>

    <div class="content">

        <h1>{{ sectionHeaderTrans|trans({}, 'across', locale) }}</h1>

        <h2>{{ informationBuilder.information.title|raw() }}</h2>

        {{ "apis/salutation"|trans({}, 'across', locale) }}
        {{ informationBuilder.information.content|raw }}

        {{ "apis/goodbye"|trans({}, 'across', locale) }}<br />
        {{ "apis/abus"|trans({}, 'across', locale) }}<br /><br />
        {{ informationBuilder.information.author }}
        <br/><br/>

        {% set copyright %}
            {{ ["apis/", informationBuilder.information.section, "/copyright"]|join|trans({}, 'across')|raw }}
        {% endset %}

        {% if copyright != "" and copyright != ["apis/", informationBuilder.information.section, "/copyright"]|join|trans({}, 'across')|raw %}
            {{ copyright }}
            <br/><br/>
        {% endif %}

        {% if informationBuilder.files|length > 0 %}
            {% set first = true %}
            <div class="dokumente">
                <table>
                    <tr>
                        <td>{{ "apis/attached"|trans({}, 'across', locale) }}:</td>

                        {% for file in informationBuilder.files %}
                            {% if not first %}
                                <tr><td></td>
                            {% endif %}
                            <td>{{ file.filename }}</td></tr>
                            {% set first = false %}
                        {% endfor %}
                </table>
                <br />
            </div>
        {% endif %}
    </div>

    <div class="footer">
        <div class="address">
            <strong>{{ "apis/abus"|trans({}, 'across', locale) }}</strong><br/>
            {{ "apis/address_1"|trans({}, 'across', locale) }}<br/>
            {{ "apis/address_2"|trans({}, 'across', locale) }}<br/>
            {{ "apis/address_3"|trans({}, 'across', locale) }}
                {% if informationBuilder.information.authorPhone is not empty %}
                    {{ informationBuilder.information.authorPhone }}
                {% else %}
                    0
                {% endif %}
        </div>
        <div class="name">
            {{ sectionHeaderTrans|trans({}, 'across', locale) }}
        </div>
        <div class="number">
            {{ "apis/no"|trans({}, 'across', locale) }} {{ informationBuilder.information.number }}/{{ "apis/gb"|trans({}, 'across', locale) }}
        </div>
    </div>
</body>
</html>
