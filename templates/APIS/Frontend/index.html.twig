{% extends 'Frame/Layout/layout.html.twig' %}

{% block administration %}
    {{ parent() }}

    {% if isAdmin or isEditor or isTranslator %}
        <div class="sidebar-panel">
            <h5 class="sidebar-panel-title">
                {{ ["apis/", module, "/headline_short"]|join|trans({}, 'across') }}
            </h5>
        </div>

        <div class="content" >
            <a href="{{ path('apis_backend_index', {'section': module}) }}" class="btn btn-danger btn-block btn-sm mr5"><i class="fa fa-lock" aria-hidden="true"></i>&nbsp;&nbsp;&nbsp;{{ "apis/admin/administration"|trans({}, 'across') }}</a>
        </div>
    {% endif %}
{% endblock %}

{% block anyUser %}

    {% set categoriesTranslated = [] %}

    {% for category in categories %}
        {% set categoriesTranslated = categoriesTranslated|merge({ (category): ["apis/", module, "/", category|lower|replace({' ': '_'})]|join|trans({}, 'across')}) %}
        {% set categoriesTranslated = categoriesTranslated|sort %}
    {% endfor %}

    <apis-search
        section="{{ module }}"
        {% if single is not empty %}single="{{ single }}"{% endif %}
        :categories="{{ categoriesTranslated|json_encode() }}">
    </apis-search>
{% endblock %}


{% block headline %}{{ ["apis/", module, "/headline"]|join|trans({}, 'across') }}{% endblock %}
{% block headline_description %}

    {% set description %}
        {{ ["apis/", module, "/headline_description"]|join|trans({}, 'across')|raw }}
    {% endset %}

    {% if description != ["apis/", module, "/headline_description"]|join|trans({}, 'across')|raw %}
        {{ description }}
    {% endif %}

{% endblock %}
{% block headline_backbutton %}{% endblock %}


{% block content %}
    <div class="mt20"></div>
    <apis-frontend section="{{ module }}" {% if single is not empty %}single="{{ single }}"{% endif %}></apis-frontend>
{% endblock %}

{% block custom_javascripts %}

{% endblock %}
