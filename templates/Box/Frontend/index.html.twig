{% extends 'Frame/Layout/layout.html.twig' %}

{% block javascripts %}
    {{ parent() }}
{% endblock %}

{% block stylesheets %}
    {{ parent() }}
{% endblock %}

{% block bodyClass %}box{% endblock %}

{% block headline %}{{ "module/box"|trans({}, 'across') }}{% endblock %}
{% block headline_description %}{% endblock %}
{% block headline_backbutton %}{% endblock %}

{% block administration %}

    {{ parent() }}

    {% if isAdmin %}
        <box-admin-sidebar></box-admin-sidebar>
    {% endif %}

{% endblock %}

{% block anyUser %}
    <box-download-mode></box-download-mode>
{% endblock %}

{% block content %}
    <box-frontend is-admin="{{ isAdmin }}" :inline-search="true"
                  :languages="{{ languages|json_encode() }}"></box-frontend>
{% endblock %}
