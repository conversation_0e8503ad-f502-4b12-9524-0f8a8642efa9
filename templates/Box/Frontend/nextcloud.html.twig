{% extends 'Frame/Layout/layout.html.twig' %}

{% block javascripts %}
    {{ parent() }}
{% endblock %}

{% block stylesheets %}
    {{ parent() }}
{% endblock %}

{% block bodyClass %}box{% endblock %}

{% block headline %}{{ "module/box"|trans({}, 'across') }}{% endblock %}
{% block headline_description %}{% endblock %}
{% block headline_backbutton %}{% endblock %}

{% block administration %}

    {{ parent() }}

    {% if isAdmin %}
        <nextcloud-admin-sidebar></nextcloud-admin-sidebar>
    {% endif %}

{% endblock %}

{% block anyUser %}
    <nextcloud-download-mode></nextcloud-download-mode>
{% endblock %}

{% block content %}

    {#<nextcloud is-admin="{{ isAdmin }}" :inline-search="true"></nextcloud>#}
    <nextcloud is-admin="{{ isAdmin }}" :inline-search="true" open-folder="{{ openFolder }}"></nextcloud>

    {#<box-frontend is-admin="{{ isAdmin }}" :inline-search="true"#}
                  {#:languages="{{ languages|json_encode() }}"></box-frontend>#}
{% endblock %}
