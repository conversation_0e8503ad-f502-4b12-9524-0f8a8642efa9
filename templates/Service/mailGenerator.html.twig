{% extends 'Frame/Layout/layout.html.twig' %}

{% block headline %}{{ "service/mailGenerator/headline"|trans({}, 'across') }}{% endblock %}
{% block headline_description %}{% endblock %}
{% block headline_backbutton %}{% endblock %}

{% block content %}

    {#hier den Select-Status aus dem übergebenen Array holen#}
    {% set state = 'none' %}

    <div class="w-50 my-4 py-4">
        <form action="{{ path('abus_portal_service_mailGenerate') }}" method="post">
            <div class="form-group row">
                <div class="alert alert-danger px-5" id="error-message">
                    Sie müssen die Sprache auswählen und eine gültige Email Adresse eingeben, um fortzufahren!
                </div>
                <label for="language" class="col-sm-4 col-form-label">{{ "service/mailGenerator/language"|trans({}, 'across') }}</label>
                <div class="col-sm-8">
                    <select class="form-control" name="language" id="language" onChange="activateButton()">
                        <option value=""></option>
                        <option value="en_GB" {% if (values and values.language == "en_GB") %} selected="selected"{% endif %}>{{ "service/english"|trans({}, 'across') }}</option>
                        <option value="de_DE"  {% if (values and values.language == "de_DE") %} selected="selected"{% endif %}>{{ "service/german"|trans({}, 'across') }}</option>
                    </select>
                </div>
            </div>
            <div class="form-group row">
                <label for="type" class="col-sm-4 col-form-label">{{ "service/mailGenerator/typ"|trans({}, 'across') }}</label>
                <div class="col-sm-8">
                    <select class="form-control" name="type" id="type">
                        <option value="new"     {% if (values and values.type == "New") %} selected="selected"{% endif %}>{{ "service/mailGenerator/abus_Portal_New_logon"|trans({}, 'across') }}</option>
                        <option value="add"     {% if (values and values.type == "Add") %} selected="selected"{% endif %}>{{ "service/mailGenerator/abus_Portal_Add_new_services"|trans({}, 'across') }}</option>
                        <option value="newTD"   {% if (values and values.type == "NewTD") %} selected="selected"{% endif %}>{{ "service/mailGenerator/abus_Technical_Data_New_logon"|trans({}, 'across') }}</option>
                    </select>
                </div>
            </div>
            <div class="form-group row">
                <label for="email" class="col-sm-4 col-form-label">{{ "service/mailGenerator/email"|trans({}, 'across') }}</label>
                <div class="col-sm-8">
                    <input type="email" class="form-control" name="email" onChange="activateButton()" id="email" placeholder="{{ "service/mailGenerator/email"|trans({}, 'across') }}" value="{{ values.email }}">
                </div>
            </div>

            <input type="hidden" name="_csrf_token" value="{{ csrf_token('mailGenerator') }}">

            <pwd-generator ent-point="{{ path('abus_portal_service_pwd_generate') }}"></pwd-generator>

            <div class="pt-4">
                {% for module in modules %}
                    <div class="form-group row mt-4 w-full">
                        <label for="password" class="col-sm-6 font-weight-bold">{{ module.getName()|trans({}, 'across') }}</label>
                        <div class="col-sm-6 d-flex justify-content-between text-center">
                            <div class="d-flex justify-content-between">
                                <input class="form-check-input mt-1" type="radio" {% if (state == 'none') %} checked {% endif %} name="{{ module.getUniqueIdentifier() }}" id="{{ module.getName() ~ loop.index ~ 'none' }}" value="none">
                                <label class="font-weight-bold" for="{{ module.getName() ~ loop.index ~ 'none' }}">{{ "service/mailGenerator/none"|trans({}, 'across') }}</label>
                            </div>
                            <div class="d-flex justify-content-between text-end">
                                <input class="form-check-input mt-1" type="radio" {% if (state == 'allowed') %} checked {% endif %} name="{{ module.getUniqueIdentifier() }}" id="{{ module.getName() ~ loop.index ~ 'allowed' }}" value="allowed">
                                <label class="font-weight-bold" for="{{ module.getName() ~ loop.index ~ 'allowed' }}">{{ "service/mailGenerator/allowed"|trans({}, 'across') }}</label>
                            </div>
                            <div class="d-flex justify-content-between">
                                <input class="form-check-input mt-1" type="radio" {% if (state == 'denied') %} checked {% endif %} name="{{ module.getUniqueIdentifier() }}" id="{{ module.getName() ~ loop.index ~ 'denied' }}" value="denied">
                                <label class="font-weight-bold" for="{{ module.getName() ~ loop.index ~ 'denied' }}">{{ "service/mailGenerator/denied"|trans({}, 'across') }}</label>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
            <button type="submit" id="create" class="btn btn-success btn-lg btn-block" disabled="disabled">{{ "service/mailGenerator/create_email"|trans({}, 'across') }}</button>
        </form>
    </div>

{% endblock %}

{% block javascripts %}
    {{ parent() }}

    <script>
        const email = document.querySelector('#email');
        const language = document.querySelector('#language');
        const btn = document.querySelector('#create');
        const error = document.querySelector('#error-message');

        function activateButton() {
            if(email.value.length > 0 && language.value.length > 0) {
                btn.removeAttribute('disabled');
                error.classList.add('d-none');
                return;
            }
            btn.setAttribute('disabled', 'disabled');
            error.classList.remove('d-none');
        }
    </script>
{% endblock %}
