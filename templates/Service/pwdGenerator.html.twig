{% extends 'Frame/Layout/layout.html.twig' %}

{% block headline %}{{ "service/passwordGenerator/headline"|trans({}, 'across') }}{% endblock %}
{% block headline_description %}{% endblock %}
{% block headline_backbutton %}{% endblock %}

{% block content %}

    <table class="table table-striped">
        <thead>
        <tr>
            <th scope="col">{{ "service/passwordGenerator/number"|trans({}, 'across') }}</th>
            <th scope="col">{{ "service/passwordGenerator/password"|trans({}, 'across') }}</th>
        </tr>
        </thead>
        <tbody>
        {% for pwd in pwdlist %}
            <tr>
                <th scope="row">{{ loop.index }}</th>
                <td>{{ pwd }}</td>
            </tr>
        {% endfor %}
        </tbody>
    </table>
{% endblock %}
