{% extends 'Frame/Layout/layout.html.twig' %}

{% block headline %}{{ "service/activedirectory/check/headline"|trans({}, 'across') }}{% endblock %}
{% block headline_description %}{% endblock %}
{% block headline_backbutton %}{% endblock %}

{% block content %}

    <div class="container-fluid mt-3 p-2">
        {% if data.portal %}
            <table cellpadding=0 cellspacing=0 style="padding: 0px;">
                <tr>
                    <td>
                        <table border=0 width=100% cellpadding=0 cellspacing=0 style="border-spacing: 0px; font-family: sans-serif; font-size: 10pt; padding: 10px;">
                            <tr>
                                <td>
                                    {% if data.typ == 'new' %}
                                        <h1 style="font-size: 12pt; color: #002F5C; font-weight: bold;">{{ "Thank_your_very_much_for_your_registration_in_the_ABUS_Portal" |trans({}, 'across', data.locale) }}</h1>
                                    {% elseif data.typ == 'add' %}
                                        <h1 style="font-size: 12pt; color: #002F5C; font-weight: bold;">{{ "Request_of_new_services_ABUS_Portal" |trans({}, 'across', data.locale) }}</h1>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                        <table cellpadding=0 cellspacing=0 style="font-family: sans-serif; font-size: 10pt; padding: 5px 10px;">
                            <tr>
                                {% if data.allowedServices is not empty %}
                                    <b>{{ "We_verified_your_registration_and_activated_the_following_services"|trans({}, 'across', data.locale) }}</b><br /><br />
                                    <ul>
                                        {% for allowedService in data.allowedServices %}
                                            <li>
                                                <div style="padding-bottom: 5px; color: green">
                                                    {{ allowedService.name }}
                                                </div>
                                            </li>
                                        {% endfor %}
                                    </ul>
                                {% endif %}
                            </tr>
                            <tr>
                                {% if data.deniedServices is not empty %}
                                    <b>{{ "For_the_following_services"|trans({}, 'across', data.locale) }}</b><br /><br />
                                    <ul>
                                        {% for deniedService in data.deniedServices %}
                                            {% if deniedService.name is defined %}
                                                <li>
                                                    <div style="padding-bottom: 5px; color: red">
                                                        {{ deniedService.name }} <span style="color: black">({{ "rejected_by"|trans({}, 'across', data.locale) }} <a
                                                                    href="mailto:{{ deniedService.decisionMakerEmail }}">{{ deniedService.decisionMakerEmail }}</a>)</span>
                                                    </div>
                                                </li>
                                            {% endif %}
                                        {% endfor %}
                                    </ul>
                                {% endif %}
                            </tr>
                        </table>
                        <table border=0 width=100% cellpadding=0 cellspacing=0 style="border-spacing: 0px; font-family: sans-serif; font-size: 10pt; padding: 10px;">
                            <tr>
                                <td>
                                    {% if data.typ == 'new' %}
                                        <h1 style="font-size: 12pt; color: #002F5C; font-weight: bold;">{{ "Your_access_data_to_the_ABUS_portal" |trans({}, 'across', data.locale) }} (<a href="https://portal.abus-kransysteme.de">https://portal.abus-kransysteme.de</a>) {{ "are_as_follows"|trans({}, 'across', data.locale) }}:</h1>
                                        <br/>
                                        <br/>
                                        <table width="100%" cellpadding=0 cellspacing=0>
                                            <tr style="font-family:'Arial'; font-size: 10pt; vertical-align: top">
                                                <td width="15%">{{ "newregister/email"|trans({}, 'across', data.locale) }}:</td>
                                                <td width="85%" style="padding-bottom: 10px;"><b>{{ data.email }}</b></td>
                                            </tr>
                                            <tr style="font-family:'Arial'; font-size: 10pt; vertical-align: top">
                                                <td>{{ "frame/password"|trans({}, 'across', data.locale) }}:</td>
                                                <td><b>{{ data.password }}</b></td>
                                            </tr>
                                        </table>
                                        <br/><br/>
                                        <div style="font-size: 8pt; font-family:'Arial';  font-style: italic;">
                                            <b>{{ "We_only_use_automatically_generated_passwords"|trans({}, 'across', data.locale) }}.</b>
                                            <b>{{ "For_safety_reasons"|trans({}, 'across', data.locale)}}.</b>
                                            <br/>
                                        </div>
                                    {% endif %}
                                    {% if not data.send %}
                                        <br><br>
                                        <form action="" method="post">
                                            <input type="hidden" name="typ" value="{{ data.typ }}">
                                            <input type="hidden" name="create" value="{{ data.id }}">
                                            <input type="hidden" name="sendmailLanguage" value="{{ data.locale }}">
                                            <input type="hidden" name="email" value="{{ data.email }}">
                                            <input type="hidden" name="password" value="{{ data.password }}">
                                            <input type="hidden" name="groups" value="{{ data.groups }}">
                                            {% if services|default() %}<input type="hidden" name="services" value="{{ services }}">{% endif %}
                                            <input type="submit" name="sendmail" class="btn btn-primary btn-block" value="E-Mail senden">
                                        </form>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        {% else %}
            <h1 style="font-size: 12pt; color: #002F5C; font-weight: bold;">{{ "Thank_your_very_much_for_your_registration_in_the_ABUS_Technical_Data"|trans({}, 'across',  data.locale) }}</h1>
            <br/>
            {{ "Dear_Madam_or_Sir"|trans({} , 'across',  data.locale) ~ ',' }}
            <br/>
            <br/>
            {{ "Thank_you_for_using_the"|trans({}, 'across',  data.locale) }}
            <b>{{ "ABUS_Technical_Data"|trans({}, 'across',  data.locale) }}</b>
            {{ "for_the_planning_and_design"|trans({}, 'across',  data.locale) }}.
            <br/>
            <br/>

            {{ "We_verified_your_registration_and_activated_your_account"|trans({}, 'across', data.locale) }}.
            <br/>
            <br/>
            <hr/>
            <table border=0 width=100% cellpadding=0 cellspacing=0 style="border-spacing: 0px; font-family: sans-serif; font-size: 10pt; padding: 10px;">
                <tr>
                    <td>
                        <h1 style="font-size: 12pt; color: #002F5C;">{{ "Your_access_data_to_the_ABUS_Technical_Data" |trans({}, 'across', data.locale) }} (<a href="https://portal.abus-kransysteme.de">https://portal.abus-kransysteme.de</a>) {{ "are_as_follows"|trans({}, 'across', data.locale) }}:</h1>
                        <br/>
                        <br/>
                        <table width="100%" cellpadding=0 cellspacing=0>
                            <tr style="font-family:'Arial'; font-size: 10pt; vertical-align: top">
                                <td width="15%">{{ "newregister/email"|trans({}, 'across', data.locale) }}:</td>
                                <td width="85%" style="padding-bottom: 10px;"><b>{{ data.email }}</b></td>
                            </tr>
                            <tr style="font-family:'Arial'; font-size: 10pt; vertical-align: top">
                                <td>{{ "frame/password"|trans({}, 'across', data.locale) }}:</td>
                                <td><b>{{ data.password }}</b></td>
                            </tr>
                        </table>
                        <br/><br/>
                        <div style="font-size: 8pt; font-family:'Arial';  font-style: italic;">
                            <b>{{ "We_only_use_automatically_generated_passwords"|trans({}, 'across', data.locale) }}.</b>
                            <b>{{ "For_safety_reasons"|trans({}, 'across', data.locale)}}.</b>
                            <br/>
                        </div>
                        <hr/>

                        {{ "The_use_of_ABUS_Technical_Data_is_easy"|trans({}, 'across', data.locale)|raw() }}
                        <br/>
                        <br/>

                        <ul>
                            <li>{{ "Generate_PDF_documents"|trans({}, 'across', data.locale) }}</li>
                            <li>{{ "Generate_2D_3D_data"|trans({}, 'across', data.locale) }}</li>
                        </ul>

                        {{ "Explanation_of_the_colors_in_the_selection_menus"|trans({}, 'across', data.locale) }}
                        <br/>

                        <ul>
                            <li>{{ "blue_minimum_input"|trans({}, 'across', data.locale) }}</li>
                            <li>{{ "white_does_not_have_to_select"|trans({}, 'across', data.locale) }}</li>
                            <li>{{ "green_function_is_possible"|trans({}, 'across', data.locale) }}</li>
                            <li>{{ "red_function_is_not_possible"|trans({}, 'across', data.locale) }}</li>
                        </ul>

                        {{ "Lots_of_additional_equipment_or_projectrelated_designs"|trans({}, 'across', data.locale) }}
                        <br/>
                        <br/>

                        {{ "For_any_questions"|trans({}, 'across', data.locale) }}

                        (<a href="http://www.abus-kransysteme.de/kontakt">http://www.abus-kransysteme.de/kontakt</a>).

                        <br/>
                        <br/>
                        {% if not data.send %}
                            <br><br>
                            <form action="" method="post">
                                <input type="hidden" name="typ" value="{{ data.typ }}">
                                <input type="hidden" name="create" value="{{ data.id }}">
                                <input type="hidden" name="sendmailLanguage" value="{{ data.locale }}">
                                <input type="hidden" name="email" value="{{ data.email }}">
                                <input type="hidden" name="password" value="{{ data.password }}">
                                <input type="hidden" name="groups" value="{{ data.groups }}">
                                <input type="submit" name="sendmail" class="btn btn-primary btn-block" value="E-Mail senden">
                            </form>
                        {% endif %}
                    </td>
                </tr>
            </table>
        {% endif %}
    </div>

{% endblock %}
