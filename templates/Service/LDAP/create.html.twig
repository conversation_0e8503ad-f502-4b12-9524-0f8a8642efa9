{% extends 'Frame/Layout/layout.html.twig' %}

{% block headline %}{{ "service/activedirectory/feedback/headline"|trans({}, 'across') }}{% endblock %}
{% block headline_description %}{% endblock %}
{% block headline_backbutton %}{% endblock %}

{% block content %}

    <div class="w-50 my-4 py-4">
        <div class="card my-2">
            {% if feedback.success is defined %}
                <div class="card-header alert alert-success">
                    {{ feedback.success|trans({}, 'across') }}.
                </div>
            {% else %}
                <div class="card-header alert alert-danger">
                    {{ feedback.error|trans({}, 'across') }}.
                </div>
            {% endif %}
            {% if entry %}
                <div class="card-body">
                    <p class="card-text"><span class="font-weight-bold">{{ "newregister/email"|trans({}, 'across') }}</span> {{ entry.email }}</p>
                    <p class="card-text"><span class="font-weight-bold">{{ "newregister/firstname"|trans({}, 'across') }}</span> {{ entry.firstName }}</p>
                    <p class="card-text"><span class="font-weight-bold">{{ "newregister/lastname"|trans({}, 'across') }}</span> {{ entry.lastName }}</p>
                    <p class="card-text"><span class="font-weight-bold">{{ "newregister/company"|trans({}, 'across') }}</span> {{ entry.company }}</p>
                    <p class="card-text"><span class="font-weight-bold">{{ "DN" }}: </span> {{ entry.dn }}</p>
                    <p class="card-text"><span class="font-weight-bold">{{ "sAMAccountName" }}: </span> {{ entry.sAMAccountName }}</p>
                </div>
            {% endif %}
            <div class="m-4">
                <div class="mb-5">{{ count }} {{ 'account/accessManagement/success/update/data'|trans({}, 'across') }}</div>
                <a href="{{ path('abus_portal_activedirectory') }}">Zur Benutzerauswahl</a>
            </div>
        </div>
    </div>

{% endblock content %}
