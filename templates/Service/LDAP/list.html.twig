{% extends 'Frame/Layout/layout.html.twig' %}

{% block headline %}{{ "service/activedirectory/create/headline"|trans({}, 'across') }}{% endblock %}
{% block headline_description %}{% endblock %}
{% block headline_backbutton %}{% endblock %}

{% block content %}

    <form method="post" enctype="application/x-www-form-urlencoded" action="" data-ajax="false">
        <div class="row mt-3 mb-4">
            <div class="col-6">
                <select class="form-control" name="registration">
                    {% for registration in registrations %}
                        <option class="form-control" value="{{ registration.id }}">{{ registration.lastname }}, {{ registration.firstname }} - {{ registration.company }} ({{ registration.crdate|date('d.m.Y') }})</option>
                    {% endfor %}
                </select>
            </div>
        </div>
        <div class="row">
            <div class="col-6">
                <button type="submit" name="check_next" class="btn btn-primary w-100">{{ "service/activedirectory/selectUser"|trans({}, 'across') }}</button>
            </div>
        </div>
    </form>

{% endblock content %}
