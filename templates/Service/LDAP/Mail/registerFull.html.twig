{% extends 'Api/email/email.html.twig' %}

{% block content %}

    <table cellpadding=0 cellspacing=0 style="border: #eaeaea 1px solid; border-spacing: 0px; padding: 0px;">
        <tr>
            <td>
                <table border=0 width=100% cellpadding=0 cellspacing=0 style="border-spacing: 0px; font-family: sans-serif; font-size: 10pt; padding: 10px;">
                    <tr>
                        <td>
                            {% if data.typ == 'new' %}
                                <h1 style="font-size: 12pt; color: #002F5C; font-weight: bold;">{{ "Thank_your_very_much_for_your_registration_in_the_ABUS_Portal" |trans({}, 'across', data.locale) }}</h1>
                            {% elseif data.typ == 'add' %}
                                <h1 style="font-size: 12pt; color: #002F5C; font-weight: bold;">{{ "Request_of_new_services_ABUS_Portal" |trans({}, 'across', data.locale) }}</h1>
                            {% endif %}
                        </td>
                    </tr>
                </table>
                <table cellpadding=0 cellspacing=0 style="font-family: sans-serif; font-size: 10pt; padding: 5px 10px;">
                    {% if data.allowedServices is not empty %}
                        <tr>
                            <b>{{ "We_verified_your_registration_and_activated_the_following_services"|trans({}, 'across', data.locale) }}</b><br /><br />
                            <ul>
                                {% for allowedService in data.allowedServices %}
                                    <li>
                                        <div style="padding-bottom: 5px; color: green">
                                            {{ allowedService.name }}
                                        </div>
                                    </li>
                                {% endfor %}
                            </ul>
                        </tr>
                    {% endif %}
                    {% if data.deniedServices is not empty %}
                        <tr>
                            <b>{{ "For_the_following_services"|trans({}, 'across', data.locale) }}</b><br /><br />
                            <ul>
                                {% for deniedService in data.deniedServices %}
                                    {% if deniedService.name is defined %}
                                        <li>
                                            <div style="padding-bottom: 5px; color: red">
                                                {{ deniedService.name }} - ( {{ "rejected_by"|trans({}, 'across', data.locale) }} <a href="mailto:{{ deniedService.decisionMakerEmail }}">{{ deniedService.decisionMakerEmail }}</a>)
                                            </div>
                                        </li>
                                    {% endif %}
                                {% endfor %}
                            </ul>
                        </tr>
                    {% endif %}
                </table>
                <table border=0 width=100% cellpadding=0 cellspacing=0 style="border-spacing: 0px; font-family: sans-serif; font-size: 10pt; padding: 10px;">
                    <tr>
                        <td>
                            {% if data.typ == 'new' %}
                                <h1 style="font-size: 12pt; color: #002F5C; font-weight: bold;">{{ "Your_access_data_to_the_ABUS_portal" |trans({}, 'across', data.locale) }} (<a href="https://portal.abus-kransysteme.de">https://portal.abus-kransysteme.de</a>) {{ "are_as_follows"|trans({}, 'across', data.locale) }}:</h1>
                                <br/>
                                <br/>
                                <table width="100%" cellpadding=0 cellspacing=0>
                                    <tr style="font-family:'Arial'; font-size: 10pt; vertical-align: top">
                                        <td width="15%">{{ "newregister/email"|trans({}, 'across', data.locale) }}:</td>
                                        <td width="85%" style="padding-bottom: 10px;"><b>{{ data.email }}</b></td>
                                    </tr>
                                    <tr style="font-family:'Arial'; font-size: 10pt; vertical-align: top">
                                        <td>{{ "frame/password"|trans({}, 'across', data.locale) }}:</td>
                                        <td><b>{{ data.password }}</b></td>
                                    </tr>
                                </table>
                                <br/><br/>
                                <div style="font-size: 8pt; font-family:'Arial';  font-style: italic;">
                                    <b>{{ "We_only_use_automatically_generated_passwords"|trans({}, 'across', data.locale) }}.</b>
                                    <b>{{ "For_safety_reasons"|trans({}, 'across', data.locale)}}.</b>
                                    <br/>
                                </div>
                            {% endif %}
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>

{% endblock %}
