{% extends 'Frame/Layout/layout.html.twig' %}

{% block headline %}{{ "account/accessManagement"|trans({}, 'across') }} - {{ "account/list/mounting/information"|trans({}, 'across') }}{% endblock %}
{% block headline_description %}{% endblock %}
{% block headline_backbutton %}{% endblock %}

{% block content %}

    {% include 'AccessManagement/includes/infoBoxes.html.twig' with {'notComplete': false, 'content': 'mounting_description'} %}

    <div class="my-2 font-weight-bold">{{ "account/workflow/User"|trans({}, 'across') }}: <strong class="font-weight-bold">{{ selectedUser }}</strong></div>

    <information
            :informations="{{ infos | json_encode }}"
            type="MI"
            language="{{ language }}">
    </information>

{% endblock %}
