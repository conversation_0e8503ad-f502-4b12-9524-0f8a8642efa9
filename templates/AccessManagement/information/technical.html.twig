{% extends 'Frame/Layout/layout.html.twig' %}

{% block headline %}{{ "account/accessManagement"|trans({}, 'across') }} - {{ "account/list/tdata/information"|trans({}, 'across') }}{% endblock %}
{% block headline_description %}{% endblock %}
{% block headline_backbutton %}{% endblock %}

{% block content %}

    {% include 'AccessManagement/includes/infoBoxes.html.twig' with {'notComplete': false, 'content': type ~ '_description'  } %}

    <div class="my-2 font-weight-bold">{{ "account/workflow/User"|trans({}, 'across') }}: <strong class="font-weight-bold">{{ selectedUser }}</strong></div>

    <information
            :informations="{{ infos | json_encode }}"
            type="TH"
            language="{{ language }}">
    </information>

{% endblock %}
