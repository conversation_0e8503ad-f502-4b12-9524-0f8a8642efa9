<div style="font-weight: bold; font-size: 1.2em; margin-bottom: 10px;">{{ "account/abukonfis/subtitle/allowed"|trans({}, 'across') }}</div>

<table style="font-family: sans-serif; font-size: 10pt; margin: 0; padding: 3px 0; width: 100%;">
    <tr><td style="width: 150px;">{{ "account/workflow/User"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ to }}</td></tr>
    <tr><td>{{ "account/workflow/AD_Username"|trans({}, 'across') }}:</td><td></td><td>{{ samaccountname }}</td></tr>
    <tr><td>{{ "newregister/email"|trans({}, 'across') }}</td><td></td><td>{{ accountHolderEmail }}</td></tr>
    <tr><td>{{ "account/workflow/Company"|trans({}, 'across') }}:</td><td></td><td>{{ company }}</td></tr>
    <tr><td>{{ "account/position/function"|trans({}, 'across') }}:</td><td></td><td>{{ positionFunction }}</td></tr>

    {% if referenceHint is defined %}
        {% include 'AccessManagement/emails/includes/hr.html.twig' %}

        <tr><td colspan="3"><b>{{ "account/abukonfis/proceed/same/title"|trans({}, 'across') }}:</b></td></tr>
        <tr><td>{{ "account/workflow/Reference_user"|trans({}, 'across') }}:</td><td></td><td>{{ referenceAccountHolder }}</td></tr>
        <tr><td>{{ "account/workflow/AD_Username"|trans({}, 'across') }}:</td><td></td><td>{{ samaccountname2 }}</td></tr>
    {% endif %}
    <tr><td colspan="3"><hr></td></tr>
    <tr><td>{{ "account/Remark"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ remarkField|raw }}</td></tr>
</table>
<br />
