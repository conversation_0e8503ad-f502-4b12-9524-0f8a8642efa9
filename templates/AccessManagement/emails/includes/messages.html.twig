{% if (message_step_1 is defined and message_step_1 != '') or (message_step_2 is defined and message_step_2 != '') or (message_step_3 is defined and message_step_3 != '') or (message_step_4 is defined and message_step_4 != '') %}

    <table style="width: 100%; font-family: sans-serif; font-size: 10pt; margin: 0; padding: 3px 0;">

        <tr><td colspan="2" style="font-weight: bold; font-size: 1.2em; border-bottom: #888888 1px solid;">{{ "abus/account/messages"|trans({}, 'across') }}</td></tr>

        {% if message_step_1 is defined and message_step_1 != '' %}
            <tr><td style="font-weight: bold; vertical-align: top; padding-right: 10px; white-space: nowrap;">{{ type == 'vpn_create' or type == 'vpn_reassign' ? 'Vertrieb-Organisation' : "Marketing"|trans({}, 'across') }}:</td><td width="90%">{{ message_step_1|nl2br }}</td></tr>
        {% endif %}
        {% if message_step_2 is defined and message_step_2 != '' %}
            <tr><td style="font-weight: bold; vertical-align: top; padding-right: 10px; white-space: nowrap;">{{ "Entwicklung-Organisation"|trans({}, 'across') }}:</td><td>{{ message_step_2|nl2br }}</td></tr>
        {% endif %}
        {% if message_step_3 is defined and message_step_3 != '' %}
            <tr><td style="font-weight: bold; vertical-align: top; padding-right: 10px; white-space: nowrap;">{{ "IT"|trans({}, 'across') }}:</td><td>{{ message_step_3|nl2br }}</td></tr>
        {% endif %}
        {% if message_step_4 is defined and message_step_4 != '' %}
            <tr><td style="font-weight: bold; vertical-align: top; padding-right: 10px; white-space: nowrap;">{{ "ProALPHA"|trans({}, 'across') }}:</td><td>{{ message_step_4|nl2br }}</td></tr>
        {% endif %}
        {% if message_step_5 is defined and message_step_5 != '' %}
            <tr><td style="font-weight: bold; vertical-align: top; padding-right: 10px; white-space: nowrap;">{{ "Vertrieb-Organisation"|trans({}, 'across') }}:</td><td>{{ message_step_5|nl2br }}</td></tr>
        {% endif %}
        {% if message_step_6 is defined and message_step_6 != '' %}
            <tr><td style="font-weight: bold; vertical-align: top; padding-right: 10px; white-space: nowrap;">{{ "OVISS"|trans({}, 'across') }}:</td><td>{{ message_step_6|nl2br }}</td></tr>
        {% endif %}
    </table>
    <br /><br />
{% endif %}
