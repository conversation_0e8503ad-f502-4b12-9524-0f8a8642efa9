<div style="font-weight: bold; font-size: 1.2em; margin-bottom: 10px;">
    {% if topic is defined %}
        {{ topic|trans({}, 'across') }}
    {% else %}
        {{ "abus/account/delete"|trans({}, 'across') }}
    {% endif %}
</div>

<table style="font-family: sans-serif; font-size: 10pt; margin: 0; padding: 3px 0; width: 100%;">
    <tr><td style="width: 150px;">{{ "account/workflow/User"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ oldAccountHolder }}</td></tr>
    <tr><td>{{ "account/workflow/AD_Username"|trans({}, 'across') }}:</td><td></td><td>{{ samaccountname }}</td></tr>
    <tr><td>{{ "account/workflow/Company"|trans({}, 'across') }}:</td><td></td><td>{{ company }}</td></tr>

    {% if ovissHint is defined %}
        {% if ovissReceiver != "" %}
            {% include 'AccessManagement/emails/includes/hr.html.twig' %}
            <tr><td colspan="3"><b>{{ "abus/account/transfer/oviss"|trans({}, 'across') }}:</b></td></tr>
            <tr><td>{{ "account/oviss/receiver"|trans({}, 'across') }}:</td><td></td><td>{{ ovissReceiver }}</td></tr>
            <tr><td>{{ "account/workflow/AD_Username"|trans({}, 'across') }}:</td><td></td><td>{{ samaccountname2 }}</td></tr>
        {% endif %}
    {% endif %}

    {% if keepToken == 1 %}
        {% include 'AccessManagement/emails/includes/hr.html.twig' %}
        <tr><td>{{ "Token"|trans({}, 'across') }}:</td><td></td><td>{{ "We'll keep the token for later usage"|trans({}, 'across') }}</td></tr>
    {% endif %}
    <tr><td colspan="3"><hr></td></tr>
    <tr><td>{{ "account/workflow/Remark"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ remarkField|raw }}</td></tr>
</table>
<br />
