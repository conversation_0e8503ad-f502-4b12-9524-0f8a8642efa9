{% if type == 'create' %}

    <div style="font-weight: bold; font-size: 1.2em; margin-bottom: 10px;">{{ "account/abukonfis/subtitle/created"|trans({}, 'across') }} {% if department is defined and department != '' %}( {{ department }} ){% endif %}</div>

    <table style="font-family: sans-serif; font-size: 10pt; margin: 0; padding: 3px 0; width: 100%;">
        <tr><td style="width: 150px;">{{ "account/workflow/User"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ newAccountHolder }}</td></tr>
        <tr><td>{{ "account/workflow/AD_Username"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ samaccountname }}</td></tr>
        <tr><td>{{ "newregister/email"|trans({}, 'across') }}</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ accountHolderEmail }}</td></tr>
        <tr><td>{{ "account/workflow/Company"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ company }}</td></tr>
        <tr><td>{{ "account/Remark"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ remarkField|raw }}</td></tr>
    </table>

    <br />

{% elseif type == 'delete' %}

    {% if department is defined and department == 'OV' %}
        <div style="font-weight: bold; font-size: 1.2em; margin-bottom: 10px;">{{ "account/oviss/subtitle/transfered"|trans({}, 'across') }} {% if department is defined and department != '' %}( {{ department }} ){% endif %}</div>
    {% else %}
        <div style="font-weight: bold; font-size: 1.2em; margin-bottom: 10px;">{{ topic|trans({}, 'across') }} {% if department is defined and department != '' %}( {{ department }} ){% endif %}</div>
    {% endif %}
    <table style="font-family: sans-serif; font-size: 10pt; margin: 0; padding: 3px 0; width: 100%;">
        <tr><td style="width: 150px;">{{ "account/workflow/User"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ oldAccountHolder }}</td></tr>
        <tr><td>{{ "account/workflow/AD_Username"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ samaccountname }}</td></tr>
        <tr><td>{{ "account/workflow/Company"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ company }}</td></tr>
        {% if ovissReceiver != "" %}
            <tr><td colspan="3"><hr></td></tr>
            <tr><td>{{ "account/oviss/receiver"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ ovissReceiver }}</td></tr>
            <tr><td>{{ "account/workflow/AD_Username"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ samaccountname2 }}</td></tr>
        {% endif %}
    </table>

    <br />

{% elseif type == 'vpn_create' %}

    <div style="font-weight: bold; font-size: 1.2em; margin-bottom: 10px;">{{ topic|trans({}, 'across') }} {% if department is defined and department != '' %}( {{ department }} ){% endif %}</div>

    <table style="font-family: sans-serif; font-size: 10pt; margin: 0; padding: 3px 0; width: 100%;">
        <tr><td style="width: 150px;">{{ "account/workflow/User"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ newAccountHolder }}</td></tr>
        <tr><td>{{ "account/workflow/AD_Username"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ samaccountname }}</td></tr>
        <tr><td>{{ "account/workflow/Company"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ company }}</td></tr>
    </table>

    <br />

{% elseif type == 'vpn_reassign' %}

    <div style="font-weight: bold; font-size: 1.2em; margin-bottom: 10px;">{{ "account/vpn_reassigned"|trans({}, 'across') }} {% if department is defined and department != '' %}( {{ department }} ){% endif %}</div>

    <table style="font-family: sans-serif; font-size: 10pt; margin: 0; padding: 3px 0; width: 100%;">
        <tr><td style="width: 150px;">{{ "account/workflow/User"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ newAccountHolder }}</td></tr>
        <tr><td>{{ "account/workflow/AD_Username"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ samaccountname }}</td></tr>
        <tr><td>{{ "account/workflow/Company"|trans({}, 'across') }}:</td><td>&nbsp;&nbsp;&nbsp;</td><td>{{ company }}</td></tr>
    </table>

    <br />

{% endif %}

{% include 'AccessManagement/emails/includes/messages.html.twig' %}
