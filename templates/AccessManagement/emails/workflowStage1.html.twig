{% extends 'Api/email/email.html.twig' %}

{% block content %}

    {% set subdomain = 'account' %}
    {% set pathCheck = '/check/' %}
    {% set pathDone = '/done/' %}

    {% if legacy is defined and legacy %}
        {% set subdomain = 'portal' %}
        {% set pathCheck = '/account/check/' %}
        {% set pathDone = '/account/done/' %}
    {% endif %}

    <table style="font-family: sans-serif; font-size: 10pt; margin: 0; padding: 3px 10px; border: #bbbbbb 1px solid; border-bottom: none; width: 130px; background-color: #7c7c7c; font-weight: bold; font-size: 10pt; color: #ffffff;">
        <tr><td>{{ "account/workflow/Requested_by"|trans({}, 'across') }}:</td></tr>
    </table>
    <table style="font-family: sans-serif; font-size: 10pt; margin: 0; padding: 3px 10px; border: #bbbbbb 1px solid; width: 100%; background-color: #fff7ee;">
        <tr><td>{{ manager_company }}</td></tr>
        <tr><td style="font-weight: bold; font-size: 1.1em;">{{ manager }}</td></tr>
        <tr><td>{{ manager_email }}</td></tr>
    </table>

    <br />

    {% include 'AccessManagement/emails/includes/workflowStage1Header.html.twig' with {'type': type } %}

    <table width="100%" border="0" cellspacing="0" cellpadding="0" style="background-color: #f6fff2; border: #888888 1px dotted; padding: 15px 0 10px 0;"><tr><td>

            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tr>
                        <td style="text-align: center;">
                            <div style="width: 200px;">
                                <!--[if mso]>
                            <v:roundrect xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w="urn:schemas-microsoft-com:office:word" href="{{ url('abus_account_grant', { 'code': codeAllow }) }}" style="height:36px;v-text-anchor:middle;width:150px;" arcsize="5%" strokecolor="#36a849" fillcolor="#3dc152">
                                <w:anchorlock/>
                                <center style="color:#ffffff;font-weight:bold;font-family:Helvetica, Arial,sans-serif;font-size:16px;">{{ "account/allow"|trans({}, 'across') }}</center>
                            </v:roundrect>
                            <![endif]-->
                                <a href="{{ url('abus_account_grant', { 'code': codeAllow }) }}" style="background-color:#3dc152;border:1px solid #36a849;border-radius:3px;color:#ffffff;display:inline-block;font-family:sans-serif;font-size:16px;font-weight:bold;line-height:44px;text-align:center;text-decoration:none;width:150px;-webkit-text-size-adjust:none;mso-hide:all;">{{ "account/allow"|trans({}, 'across') }}</a>
                            </div>
                        </td>
                    </tr>
                </table>

            </td><td>

            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tr>
                        <td style="text-align: center;">
                            <div style="width: 200px;">
                                <!--[if mso]>
                            <v:roundrect xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w="urn:schemas-microsoft-com:office:word" href="{{ url('abus_account_grant', { 'code': codeDeny }) }}" style="height:36px;v-text-anchor:middle;width:150px;" arcsize="5%" strokecolor="#e9af00" fillcolor="#FF0000">
                                <w:anchorlock/>
                                <center style="color:#ffffff;font-weight:bold;font-family:Helvetica, Arial,sans-serif;font-size:16px;">{{ "account/denied"|trans({}, 'across') }}</center>
                            </v:roundrect>
                            <![endif]-->
                                <a href="{{ url('abus_account_grant', { 'code': codeDeny }) }}" style="background-color:#FF0000;border:1px solid #e9af00;border-radius:3px;color:#ffffff;display:inline-block;font-family:sans-serif;font-size:16px;font-weight:bold;line-height:44px;text-align:center;text-decoration:none;width:150px;-webkit-text-size-adjust:none;mso-hide:all;">{{ "account/denied"|trans({}, 'across') }}</a>
                            </div>
                        </td>
                    </tr>
                </table>

        </td></tr></table>
{% endblock %}
