{% extends 'Api/email/email.html.twig' %}

{% block content %}

    {% include 'AccessManagement/emails/includes/workflowStage2Header.html.twig' with {'type': type } %}

    <table width="100%" border="0" cellspacing="0" cellpadding="0" style="background-color: #f6fff2; border: #888888 1px dotted; padding: 15px 0 10px 0;"><tr><td>

                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tr>
                        <td style="text-align: center;">
                            <div style="width: 200px;">
                                <!--[if mso]>
                                <v:roundrect xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w="urn:schemas-microsoft-com:office:word" href="{{ url('abus_account_check', {'code': verificationCode}) }}" style="height:36px;v-text-anchor:middle;width:150px;" arcsize="5%" strokecolor="#e9af00" fillcolor="#ffc000">
                                    <w:anchorlock/>
                                    <center style="color:#ffffff;font-weight:bold;font-family:Helvetica, Arial,sans-serif;font-size:16px;">{{ "account/email/verify"|trans({}, 'across') }}</center>
                                </v:roundrect>
                                <![endif]-->
                                <a href="{{ url('abus_account_check', {'code': verificationCode}) }}" style="background-color:#ffc000;border:1px solid #e9af00;border-radius:3px;color:#ffffff;display:inline-block;font-family:sans-serif;font-size:16px;font-weight:bold;line-height:44px;text-align:center;text-decoration:none;width:150px;-webkit-text-size-adjust:none;mso-hide:all;">{{ "account/email/verify"|trans({}, 'across') }}</a>
                            </div>
                        </td>
                    </tr>
                </table>

            </td><td>

                <table width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tr>
                        <td style="text-align: center;">
                            <div style="width: 200px;">
                                <!--[if mso]>
                                <v:roundrect xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w="urn:schemas-microsoft-com:office:word" href="{{ url('abus_account_done', {'code': verificationCode})}}" style="height:36px;v-text-anchor:middle;width:150px;" arcsize="5%" strokecolor="#36a849" fillcolor="#3dc152">
                                    <w:anchorlock/>
                                    <center style="color:#ffffff;font-weight:bold;font-family:Helvetica, Arial,sans-serif;font-size:16px;">{{ "account/action/already/done"|trans({}, 'across') }}</center>
                                </v:roundrect>
                                <![endif]-->
                                <a href="{{ url('abus_account_done', {'code': verificationCode}) }}" style="background-color:#3dc152;border:1px solid #36a849;border-radius:3px;color:#ffffff;display:inline-block;font-family:sans-serif;font-size:16px;font-weight:bold;line-height:44px;text-align:center;text-decoration:none;width:150px;-webkit-text-size-adjust:none;mso-hide:all;">{{ "account/action/already/done"|trans({}, 'across') }}</a>
                            </div>
                        </td>
                    </tr>
                </table>

            </td></tr></table>

{% endblock %}
