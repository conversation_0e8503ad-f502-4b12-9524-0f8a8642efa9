{% extends 'Frame/Layout/base.html.twig' %}

{% block bodyClass %}layoutNotAuthenticated login{% endblock %}

{% block body %}
    <div data-role="page" id="grantpermission" class="default-background text-dark">
        <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100vh; background-color: #0058A1; padding: -20px;">

            <div style="width: 980px; margin: 20px auto 0; padding: 20px; background-color: #e8e8e8;">

                <h2>{{ "account/accessManagement"|trans({}, 'across') }}</h2>

                {% if success %}
                    <div class="successMessageOuter mt-2">
                        {% if count is defined %}
                            <div class="alert alert-{{ success }}">{{ count }} - {{ content|trans({}, 'across' )|raw }}</div>
                        {% else %}
                            <div class="alert alert-{{ success }}">{{ content|trans({}, 'across' )|raw }}</div>
                        {% endif %}
                    </div>
                {% endif %}

            </div>
        </div>
    </div>
{% endblock %}
