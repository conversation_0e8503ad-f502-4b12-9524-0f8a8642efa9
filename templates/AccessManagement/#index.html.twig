{% extends 'Frame/Layout/layout.html.twig' %}

{% block headline %}{{ "account/accessManagement"|trans({}, 'across') }} - {{ "account/listUsers"|trans({}, 'across') }}{% endblock %}
{% block headline_description %}{% endblock %}
{% block headline_backbutton %}{% endblock %}

{% block content %}
    <form action="" method="post" data-ajax="false">

        <div class="row mt-4">
            {% if admin or superadmin %}
                <div class="col-12">
                    <select name="companies" id="companies" class="form-control w-100" onchange="this.form.submit()">
                        {% for c in companies %}
                            <option value="{{ c.company|base64encode }}" {% if company == c.company %}selected="selected"{% endif %}>{{ c.company }}</option>
                        {% endfor %}
                        <option value="{{ 'NOCOMPANY'|base64encode }}" {% if company == "NOCOMPANY" %}selected="selected"{% endif %}>==> {{ "account/noCompanyAssigned"|trans({}, 'across') }} <==</option>
                    </select>
                </div>
            {% elseif companyPermission|length > 1 %}
                <div class="col-12">
                    <select name="companies" id="companies" class="form-control w-100" onchange="this.form.submit()">
                        {% for c in companyPermission %}
                            <option value="{{ c|base64encode }}" {% if company == c %}selected="selected"{% endif %}>{{ c }}</option>
                        {% endfor %}
                    </select>
                </div>
            {% endif %}
        </div>

    </form>
    {% include 'AccessManagement/userTable.html.twig' %}

    <div class="row">
        <div class="col-12">
            {% if admin or superadmin %}
                <button type="submit" name="excel" value="true" style="background-color: lightgreen;" class="btn btn-primary w-100 mb-4">Excel Liste erstellen</button>
            {% endif %}
        </div>
    </div>

{% endblock %}
