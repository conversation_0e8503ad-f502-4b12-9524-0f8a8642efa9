<div class="row">
    <div class="col-12">
        <table id="all" class="table">
            <thead style="font-size: .65rem;">
            <tr>
                {% if reporting %}<th class="sorter-true"></th> {% endif %}
                <th>{{ "Name"|trans({}, 'across') }}</th>
                <th style="text-align: center;">{{"account/Portal"|trans({}, 'across') }}</th>
                <th style="text-align: center;">{{"account/Sales Information"|trans({}, 'across') }}</th>
                <th style="text-align: center;">{{"account/Mounting Information"|trans({}, 'across') }}</th>
                <th style="text-align: center;">{{"account/Service Information"|trans({}, 'across') }}</th>
                <th style="text-align: center;">{{"account/Technical Information"|trans({}, 'across') }}</th>
                <th style="text-align: center;">{{"account/Technical Data"|trans({}, 'across') }}</th>
                <th style="text-align: center;">{{"account/Docu"|trans({}, 'across') }}</th>
                <th style="text-align: center;">{{"account/BOX"|trans({}, 'across') }}</th>
                <th style="text-align: center;">{{"account/PL"|trans({}, 'across') }}</th>
                <th style="text-align: center;">{{"account/Media"|trans({}, 'across') }}</th>
                <th style="text-align: center;">{{"account/SpareParts"|trans({}, 'across') }}</th>
                <th style="text-align: center;">{{"account/Spare- Parts prices"|trans({}, 'across') }}</th>
                <th style="text-align: center;">{{"account/WinETIS"|trans({}, 'across') }}</th>
                <th style="text-align: center;">{{"account/AccessManagement"|trans({}, 'across') }}</th>
                <th style="text-align: center;">{{"account/VPN"|trans({}, 'across') }}</th>
                <th style="text-align: center;">{{"account/ABUKonfis"|trans({}, 'across') }}</th>
                <th style="text-align: center;">{{"account/ABUKonfis-WEB"|trans({}, 'across') }}</th>
                <th style="text-align: center;">{{"account/OVISS"|trans({}, 'across') }}</th>
                <th style="text-align: center;">{{"account/ProALPHA"|trans({}, 'across') }}</th>
            </tr>
            </thead>
            <tbody>

            {% for user in users %}
                {% if user.useraccountcontrol == 66048 or admin or superadmin %}
                    <tr style="font-size: .8rem;" {% if user.useraccountcontrol != 66048 %}class="deactivated"{% endif %}>
                        {% if reporting %}<td><a href="{{ path('abus_reporting2_show', {'domain': getDomain(), 'id': user.id }) }}" rel="external" style="width: 20px; height: 20px; padding: 0px; margin: 0px;"></a></td> {% endif %}
                        <td>{{ user.lastname }}, {{ user.firstname }}</td>
                        <td style="text-align: center;">{% if 'PORTAL' in user.cleanGroups %}<span style="display:none">1</span><span>Ja</span>{% else %}<span style="display:none">0</span>{% endif %}</td>
                        <td style="text-align: center;">{% if 'SALES' in user.cleanGroups %}<span style="display:none">1</span><span>Ja</span>{% else %}<span style="display:none">0</span>{% endif %}</td>
                        <td style="text-align: center;">{% if 'INFO_MOUNTING' in user.cleanGroups %}<span style="display:none">1</span><span>Ja</span>{% else %}<span style="display:none">0</span>{% endif %}</td>
                        <td style="text-align: center;">{% if 'INFO_SERVICE' in user.cleanGroups %}<span style="display:none">1</span><span>Ja</span>{% else %}<span style="display:none">0</span>{% endif %}</td>
                        <td style="text-align: center;">{% if 'INFO_TECHNIK' in user.cleanGroups %}<span style="display:none">1</span><span>Ja</span>{% else %}<span style="display:none">0</span>{% endif %}</td>
                        <td style="text-align: center;">{% if 'TDATA' in user.cleanGroups %}<span style="display:none">1</span><span>Ja</span>{% else %}<span style="display:none">0</span>{% endif %}</td>
                        <td style="text-align: center;">{% if 'DOCU' in user.cleanGroups %}<span style="display:none">1</span><span>Ja</span>{% else %}<span style="display:none">0</span>{% endif %}</td>
                        <td style="text-align: center;">{% if 'BOX' in user.cleanGroups %}<span style="display:none">1</span><span>Ja</span>{% else %}<span style="display:none">0</span>{% endif %}</td>
                        <td style="text-align: center;">{% if 'PL' in user.cleanGroups %}<span style="display:none">1</span><span>Ja</span>{% else %}<span style="display:none">0</span>{% endif %}</td>
                        <td style="text-align: center;">{% if 'MEDIA' in user.cleanGroups %}<span style="display:none">1</span><span>Ja</span>{% else %}<span style="display:none">0</span>{% endif %}</td>
                        <td style="text-align: center;">{% if 'SPAREPARTS' in user.cleanGroups %}<span style="display:none">1</span><span>Ja</span>{% else %}<span style="display:none">0</span>{% endif %}</td>
                        <td style="text-align: center;">{% if 'SPAREPARTS_PRICES' in user.cleanGroups %}<span style="display:none">1</span><span>Ja</span>{% else %}<span style="display:none">0</span>{% endif %}</td>
                        <td style="text-align: center;">{% if 'WINETIS' in user.cleanGroups %}<span style="display:none">1</span><span>Ja</span>{% else %}<span style="display:none">0</span>{% endif %}</td>
                        <td style="text-align: center;">{% if 'ACCESSMANAGEMENT' in user.cleanGroups %}<span style="display:none">1</span><span>Ja</span>{% else %}<span style="display:none">0</span>{% endif %}</td>
                        <td style="text-align: center;">{% if 'VPN' in user.cleanGroups %}<span style="display:none">1</span><span>Ja</span>{% else %}<span style="display:none">0</span>{% endif %}</td>
                        <td style="text-align: center;">{% if 'ABUKONFIS' in user.cleanGroups %}<span style="display:none">1</span><span>Ja</span>{% else %}<span style="display:none">0</span>{% endif %}</td>
                        <td style="text-align: center;">{% if 'WEB_ABUKONFIS' in user.cleanGroups %}<span style="display:none">1</span><span>Ja</span>{% else %}<span style="display:none">0</span>{% endif %}</td>
                        <td style="text-align: center;">{% if 'OVISS' in user.cleanGroups %}<span style="display:none">1</span><span>Ja</span>{% else %}<span style="display:none">0</span>{% endif %}</td>
                        <td style="text-align: center;">{% if 'PROALPHA' in user.cleanGroups %}<span style="display:none">1</span><span>Ja</span>{% else %}<span style="display:none">0</span>{% endif %}</td>
                    </tr>
                {% endif %}
            {% endfor %}
            </tbody>
        </table>
    </div>
</div>
