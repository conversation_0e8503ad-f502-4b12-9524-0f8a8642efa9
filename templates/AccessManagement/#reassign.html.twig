{% extends 'Frame/Layout/layout.html.twig' %}

{% block headline %}{{ "account/accessManagement"|trans({}, 'across') }} - {{ "account/reassign"|trans({}, 'across') }}{% endblock %}
{% block headline_description %}{% endblock %}
{% block headline_backbutton %}{% endblock %}

{% set admin = 'ROLE_WEB_ACCESSMANAGEMENT_ADMIN' in currentUser.roles %}
{% set superadmin = 'ROLE_WEB_ACCESSMANAGEMENT_SUPERADMIN' in currentUser.roles %}
{% set reporting = 'ROLE_WEB_PORTAL_REPORTING_USERS' in currentUser.roles %}

{% block content %}

    {% include 'AccessManagement/includes/infoBoxes.html.twig' with {'notComplete': notComplete, 'content': 'reassign_description' } %}

    <div class="">

        {% if admin or superadmin %}
            <div>
                <form action="" method="post" data-ajax="false">
                    <div class="form-group">
                        <label for="companies"></label>
                        <select name="companies" class="form-control" onChange="this.form.submit()" id="companies">
                            {% for key, c in companies %}
                                <option class="fw-bold" value="{{ c.company|base64encode }}" {% if company == c.company %}selected="selected"{% endif %}><strong>{{ c.company }}</strong></option>
                            {% endfor %}
                        </select>
                    </div>
                </form>
            </div>
        {% elseif companyPermission|length > 0 %}
            <div>
                <form action="" method="post" class="form-control" data-ajax="false">
                    <select name="companies" id="companies">
                        {% for c in companyPermission %}
                            <option value="{{ c|base64encode }}" {% if company == c %}selected="selected"{% endif %}>{{ c }}</option>
                        {% endfor %}
                    </select>
                </form>
            </div>
        {% endif %}

        <form method="post" data-ajax="false">

            <div class="form-group">
                <label for="from">{{ "account/from/user"|trans({}, 'across') }}:</label>
                <select class="form-control" id="from" name="from">
                    <option value="Appserver">Appserver</option>
                    {% for konfisuser in konfisusers %}
                        <option value="{{ konfisuser.ldapname }}" {% if konfisuser.ldapname == from %}selected="selected"{% endif %}>{{ konfisuser.lastname }}, {{ konfisuser.firstname }}</option>
                    {% endfor %}
                </select>
            </div>

            <div class="form-group">
                <label for="to">{{ "account/to/user"|trans({}, 'across') }}:</label>
                <select class="form-control" id="to" name="to">
                    {% for nokonfisuser in nokonfisusers %}
                        <option value="{{ nokonfisuser.ldapname }}" {% if nokonfisuser.ldapname == to %}selected="selected"{% endif %}>{{ nokonfisuser.lastname }}, {{ nokonfisuser.firstname }}</option>
                    {% endfor %}
                </select>
            </div>

            <div class="form-group">
                <input type="submit" class="form-control btn btn-success" name="submit" value="{{ "account/reassign"|trans({}, 'across') }} *">
            </div>
        </form>
    </div>


    <div style="clear: both; font-size: 0.8em; padding-top: 20px; font-style: italic;">
        <table><tr><td valign="top">*&nbsp;&nbsp;&nbsp;</td><td>{{ "reassign_footer_information"|trans({}, 'across') }}</td></tr></table>
    </div>

{% endblock %}

{% block javascripts %}

    {{ parent() }}

    <script type="text/javascript"></script>

{% endblock %}
