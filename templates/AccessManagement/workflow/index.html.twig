{% extends 'Frame/Layout/layout.html.twig' %}

{% block headline %}{{ "account/workflow/headline"|trans({}, 'across') }}{% endblock %}
{% block headline_description %}{% endblock %}
{% block headline_backbutton %}{% endblock %}

{% block content %}

    <div class="row my-2"><a class="btn btn-primary px-4" href="{{ path('accounts_workflow_archive') }}">{{ "module/access_management_account_workflow_archive"|trans({}, 'across') }}</a></div>

    <div class="mt-2">

        <workflows :accounts="{{ openWorkflows | json_encode }}" title="{{ 'account/workflow/open'|trans({}, 'across') }}"></workflows>

        <workflows :accounts="{{ closedWorkflows | json_encode }}" title="{{ 'account/workflow/close'|trans({}, 'across') }}"></workflows>

        <workflows :accounts="{{ deniedWorkflows | json_encode }}" title="{{ 'account/workflow/denied'|trans({}, 'across') }}"></workflows>

    </div>

{% endblock %}
