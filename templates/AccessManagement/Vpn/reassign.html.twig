{% extends 'Frame/Layout/layout.html.twig' %}

{% block headline %}{{ "account/accessManagement"|trans({}, 'across') }} - {{ "account/vpn/reassign"|trans({}, 'across') }}{% endblock %}
{% block headline_description %}{% endblock %}
{% block headline_backbutton %}{% endblock %}

{% set admin = user.hasRole('ROLE_WEB_ACCESSMANAGEMENT_ADMIN') %}
{% set superadmin = user.hasRole('ROLE_WEB_ACCESSMANAGEMENT_SUPERADMIN') %}
{% set reporting = user.hasRole('ROLE_WEB_PORTAL_REPORTING_USERS') %}

{% block content %}

    {% include 'AccessManagement/includes/infoBoxes.html.twig' with {'notComplete': notComplete, 'content': 'vpn_reassign_description' } %}

    <div class="">

        {% if admin or superadmin %}
            <div>
                <form action="" method="post" data-ajax="false">
                    <div class="form-group">
                        <label for="companies"></label>
                        <select name="company" class="form-control" onChange="this.form.submit()" id="companies">
                            {% for company in companies %}
                                <option class="fw-bold" value="{{ company.company }}" {% if selectedCompany == company.company %}selected="selected"{% endif %}><strong>{{ company.company }}</strong></option>
                            {% endfor %}
                        </select>
                    </div>
                </form>
            </div>
        {% elseif companyPermission|length > 0 %}
            <div>
                <form action="" method="post" data-ajax="false">
                    <div class="form-group">
                        <label for="companies"></label>
                        <select name="company" class="form-control" onChange="this.form.submit()" id="companyPermission">
                            {% for company in companyPermission %}
                                <option class="fw-bold" value="{{ company }}" {% if company == selectedCompany %}selected="selected"{% endif %}><strong>{{ company }}</strong></option>
                            {% endfor %}
                        </select>
                    </div>
                </form>
            </div>
        {% endif %}

        <form method="post" data-ajax="false">

            <div class="form-group my-4">
                <label for="from">{{ "account/from/user"|trans({}, 'across') }}:</label>
                <select class="form-control" id="from" name="from">
                    <option value=""></option>
                    {% for user in users %}
                        <option {% if user.ldapname == from %}selected{% endif %} value="{{ user.ldapname }}">{{ user.lastname }}, {{ user.firstname }}</option>
                    {% endfor %}
                </select>
            </div>

            <div class="form-group">
                <label for="remarkField">{{  "account/Remark"|trans({}, 'across') }}</label>
                <textarea class="form-control" id="remarkField"  name="remarkField" rows="3"></textarea>
            </div>

            <div class="form-group">
                <input type="submit" class="form-control btn btn-success" name="submit" value="{{ "account/vpn/reassign"|trans({}, 'across') }} *">
            </div>
        </form>
    </div>


    <div style="clear: both; font-size: 0.8em; padding-top: 20px; font-style: italic;">
        <table><tr><td valign="top">*&nbsp;&nbsp;&nbsp;</td><td>{{ "reassign_footer_information"|trans({}, 'across') }}</td></tr></table>
    </div>

{% endblock %}

{% block javascripts %}

    {{ parent() }}

    <script type="text/javascript"></script>

{% endblock %}
