{% extends 'Frame/Layout/layout.html.twig' %}

{% block headline %}{{ "account/accessManagement"|trans({}, 'across') }} - {{ "module/access_management_list_users"|trans({}, 'across') }}{% endblock %}
{% block headline_description %}{% endblock %}
{% block headline_backbutton %}{% endblock %}

{% set admin = user.hasRole('ROLE_WEB_ACCESSMANAGEMENT_ADMIN') %}
{% set superadmin = user.hasRole('ROLE_WEB_ACCESSMANAGEMENT_SUPERADMIN') %}
{% set reporting = user.hasRole('ROLE_WEB_PORTAL_REPORTING_USERS') %}

{% block content %}

    {% include 'AccessManagement/includes/infoBoxes.html.twig' with {'notComplete': notComplete, 'content': 'list_description' } %}

    <form action="" method="post" data-ajax="false">

        <div class="row mt-4">
            {% if admin or superadmin %}
                <div class="col-12">
                    <select name="companies" id="companies" class="form-control w-100" onchange="this.form.submit()">
                        {% for company in companies %}
                            <option value="{{ company.company|base64encode }}" {% if selectedCompany == company.company %}selected="selected"{% endif %}>{{ company.company }}</option>
                        {% endfor %}
                    </select>
                </div>
            {% elseif companyPermission|length > 0 %}
                <div class="col-12">
                    <select name="companies" id="companies" class="form-control w-100" onchange="this.form.submit()">
                        {% for company in companyPermission %}
                            <option value="{{ company|base64encode }}" {% if selectedCompany == company %}selected="selected"{% endif %}>{{ company }}</option>
                        {% endfor %}
                    </select>
                </div>
            {% endif %}
        </div>

    </form>

    <account-list :users="{{ users | json_encode }}" path="{{ path('abus_account_list_salesInformation') }}"></account-list>

    <div class="row">
        <div class="col-12">
            {% if admin or superadmin %}
                <form action="{{ path('abus_sales_excel_table_create') }}" method="POST">
                    <input type="hidden" name="company" value="{{ selectedCompany }}">
                    <div class="form-group">
                        <input type="submit" class="form-control btn btn-success d-flex justify-content-center align-item-center" name="submit" value="Excel Liste erstellen">
                    </div>
                </form>
            {% endif %}
        </div>
    </div>

{% endblock %}
