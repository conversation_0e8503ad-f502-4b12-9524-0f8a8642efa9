{% extends 'Frame/Layout/layout.html.twig' %}

{% block headline %}{{ "abus/account/delete"|trans({}, 'across') }}{% endblock %}
{% block headline_description %}{% endblock %}
{% block headline_backbutton %}{% endblock %}

{% set admin = user.hasRole('ROLE_WEB_ACCESSMANAGEMENT_ADMIN') %}
{% set superadmin = user.hasRole('ROLE_WEB_ACCESSMANAGEMENT_SUPERADMIN') %}

{% block content %}

    {% include 'AccessManagement/includes/infoBoxes.html.twig' with {'notComplete': notComplete, 'content': 'delete_description' } %}

    {% if admin or superadmin %}
        <div>
            <form action="" method="post" data-ajax="false">
                <div class="form-group">
                    <label for="companies"></label>
                    <select name="company" class="form-control" onChange="this.form.submit()" id="companies">
                        {% for company in companies %}
                            <option class="fw-bold" value="{{ company.company }}" {% if selectedCompany == company.company %}selected="selected"{% endif %}><strong>{{ company.company }}</strong></option>
                        {% endfor %}
                    </select>
                </div>
            </form>
        </div>
    {% elseif companyPermission|length > 0 %}
        <div>
            <form action="" method="post" data-ajax="false">
                <div class="form-group">
                    <label for="companies"></label>
                    <select name="company" class="form-control" onChange="this.form.submit()" id="companyPermission">
                        {% for company in companyPermission %}
                            <option class="fw-bold" value="{{ company }}" {% if company == selectedCompany %}selected="selected"{% endif %}><strong>{{ company }}</strong></option>
                        {% endfor %}
                    </select>
                </div>
            </form>
        </div>
    {% endif %}

    <form action="" method="post" data-ajax="false">

        <delete-abukonfis
                :users="{{ users | json_encode }}"
                :ovissusers="{{ ovissusers | json_encode }}"
        ></delete-abukonfis>

        <div class="form-group mt-4">
            <input type="submit" class="form-control btn btn-success" name="submit" value="{{ "abus/account/delete"|trans({}, 'across') }} *">
        </div>

    </form>

    <div style="clear: both; font-size: 0.8em; padding-top: 20px; font-style: italic;">
        <table><tr><td valign="top">*&nbsp;&nbsp;&nbsp;</td><td>{{ "delete_footer_information"|trans({}, 'across') }}</td></tr></table>
    </div>

{% endblock %}
