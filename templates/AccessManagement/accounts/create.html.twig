{% extends 'Frame/Layout/layout.html.twig' %}

{% block headline %}{{ "account/accessManagement"|trans({}, 'across') }} - {{ "module/access_management_account_create"|trans({}, 'across') }}{% endblock %}
{% block headline_description %}{% endblock %}
{% block headline_backbutton %}{% endblock %}

{% set admin = user.hasRole('ROLE_WEB_ACCESSMANAGEMENT_ADMIN') %}
{% set superadmin = user.hasRole('ROLE_WEB_ACCESSMANAGEMENT_SUPERADMIN') %}
{% set reporting = user.hasRole('ROLE_WEB_PORTAL_REPORTING_USERS') %}

{% block content %}

    {% include 'AccessManagement/includes/infoBoxes.html.twig' with {'errors': errors, 'content': 'create_description' } %}

    <div class="">
        {% if admin or superadmin %}
            <div>
                <form action="" method="post" data-ajax="false">
                    <div class="form-group">
                        <label for="companies"></label>
                        <select name="company" class="form-control" onChange="this.form.submit()" id="companies">
                            {% for company in companies %}
                                <option class="fw-bold" value="{{ company.company }}" {% if selectedCompany == company.company %}selected="selected"{% endif %}><strong>{{ company.company }}</strong></option>
                            {% endfor %}
                        </select>
                    </div>
                </form>
            </div>
        {% elseif companyPermission|length > 0 %}
            <div>
                <form action="" method="post" data-ajax="false">
                    <div class="form-group">
                        <label for="companies"></label>
                        <select name="company" class="form-control" onChange="this.form.submit()" id="companyPermission">
                            {% for company in companyPermission %}
                                <option class="fw-bold" value="{{ company }}" {% if company == selectedCompany %}selected="selected"{% endif %}><strong>{{ company }}</strong></option>
                            {% endfor %}
                        </select>
                    </div>
                </form>
            </div>
        {% endif %}

        <form method="post" data-ajax="false">

            <create-abukonfis
                    :nokonfisusers="{{ nokonfisusers | json_encode }}"
                    :konfisusers="{{ konfisusers | json_encode }}"
                    :abukonfisadmin="{{ abukonfisadmin ? 'true' : 'false' }}"
                    positionfunction="{{ position_function }}"
                    newaccountholder="{{ newAccountHolder }}"
                    referenceaccountholder="{{ referenceAccountHolder }}"
                    :isreferenzuser="{{ noreferenzuser ? 'true' : 'false' }}"
                    standardtext="{{ standardText }}"
                    remark="{{ remark }}"
            ></create-abukonfis>

            <div class="form-group">
                <input type="submit" class="form-control btn btn-success" name="submit" value="{{ "account/create"|trans({}, 'across') }} *">
            </div>
        </form>
    </div>


    <div style="clear: both; font-size: 0.8em; padding-top: 20px; font-style: italic;">
        <table><tr><td valign="top">*&nbsp;&nbsp;&nbsp;</td><td>{{ "create_footer_information"|trans({}, 'across') }}</td></tr></table>
    </div>

{% endblock %}
