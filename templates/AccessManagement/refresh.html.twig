{% extends 'Frame/Layout/layout.html.twig' %}

{% block headline %}{{ "account/accessManagement"|trans({}, 'across') }}{% endblock %}
{% block headline_description %}{% endblock %}
{% block headline_backbutton %}{% endblock %}

{% block content %}

    {% if success %}
        <div class="successMessageOuter mt-2">
            {% if count is defined %}
                <div class="alert alert-{{ success }}">{{ count }} - {{ content|trans({}, 'across' )|raw }}</div>
            {% else %}
                <div class="alert alert-{{ success }}">{{ content|trans({}, 'across' )|raw }}</div>
            {% endif %}
        </div>
    {% endif %}

{% endblock %}

