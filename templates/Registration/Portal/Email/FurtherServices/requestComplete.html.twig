{% extends 'Api/email/email.html.twig' %}
{% block content %}

    <table border=0 width=100% cellpadding=0 cellspacing=0 style="border-spacing: 0px; font-family: sans-serif; font-size: 10pt; padding: 10px;">
        <tr>
            <td>
                <h1 style="font-size: 12pt; color: #002F5C; font-weight: bold;">{{ "ABUS Portal - Further services requested - Complete"|trans({}, 'across', locale) }}</h1>
                <b>{{ "newregister/submittedData"|trans({}, 'across', locale) }}</b>
            </td>
        </tr>
    </table>

    <table cellpadding=0 cellspacing=0 style="font-family: sans-serif; font-size: 10pt; padding: 5px 10px;">
        <tr>
            <td style="font-weight: bold;">{{ "newregister/company"|trans({}, 'across', locale) }}</td>
            <td>{{ company }}</td>
        </tr>
        <tr>
            <td style="font-weight: bold;">{{ "newregister/name"|trans({}, 'across', locale) }}</td>
            <td>{{ name }}</td>
        </tr>
        <tr>
            <td style="font-weight: bold;">{{ "newregister/email"|trans({}, 'across', locale) }}</td>
            <td><a href="mailto: {{ email }}">{{ email }}</a></td>
        </tr>
        <br/>
        <tr>
            <td style="font-weight: bold;" valign="top">{{ "newregister/requestedServices"|trans({}, 'across', locale) }}</h1>
            <td>
                {% for service in requestedServices %}
                {% set level = '' %}
                {% set subServices = service.getSubModules() %}

                {% if service.getPermissionGrantedAsString() == null %}
                <div style="clear: both;">
                    {% elseif service.getPermissionGrantedAsString() == 'false' %}
                    <div style="color: red; clear: both;">
                        {% elseif service.getPermissionGrantedAsString() == 'true' %}
                        <div style="color: green; clear: both;">
                            {% else %}
                            <div style="color: green; clear: both;">
                                {% set level = [' - ', service.getPermissionGrantedAsString()]|join %}
                                {% endif %}
                                &bull; {{ service.getName()|trans({}, 'across', 'de_DE')  }} {{ level }}
                                {% if service.isSubmodule() %}
                                    {% set subService = service.isSubmodule() %}
                                    {{ subService.getName()|raw |trans({}, 'across', 'de_DE') }} -  {{ service.getName()|raw |trans({}, 'across', 'de_DE') }}
                                {% endif %}
                            </div>
                            {% endfor %}
            </td>
        </tr>
    </table>

{% endblock %}
