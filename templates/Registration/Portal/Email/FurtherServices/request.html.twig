{% extends 'Api/email/email.html.twig' %}

{% block content %}

    {% set subdomain = 'portal.' %}
    {% set path = '/register/grantpermission/' %}

    {% if legacy is defined and legacy %}
        {% set subdomain = 'portal.' %}
        {% set path = '/register/grantpermission/' %}
    {% endif %}

    <table border=0 width=100% cellpadding=0 cellspacing=0 style="border-spacing: 0px; font-family: sans-serif; font-size: 10pt; padding: 10px;">
        <tr>
            <td>
                <h1 style="font-size: 12pt; color: #002F5C; font-weight: bold;">{{ "ABUS Portal - Further services requested"|trans({}, 'across',  locale) }}</h1>
                <b>{{ "newregister/submittedData"|trans({}, 'across',  locale) }}</b>
            </td>
        </tr>
    </table>

    <table cellpadding=0 cellspacing=0 style="font-family: sans-serif; font-size: 10pt; padding: 5px 10px;">
        <tr>
            <td style="font-weight: bold;">{{ "newregister/company"|trans({}, 'across',  locale) }}</td>
            <td>{{ company }}</td>
        </tr>
        <tr>
            <td style="font-weight: bold;">{{ "newregister/name"|trans({}, 'across',  locale) }}</td>
            <td>{{ name }}</td>
        </tr>
        <tr>
            <td style="font-weight: bold;">{{ "newregister/email"|trans({}, 'across',  locale) }}</td>
            <td><a href="mailto: {{ email }}">{{ email }}</a></td>
        </tr>
        <br>
        <tr>
            <td style="font-weight: bold;" valign="top">{{ "newregister/requestedServices"|trans({}, 'across',  locale) }}</td>
            <td>
                &nbsp;&nbsp;
                {% for service in requestedServices %}
                    {% if service.isSubmodule() %}
                        {% set subService = service.isSubmodule() %}
                        {{ subService.getName()|raw |trans({}, 'across',  locale) }} -  {{ service.getName()|raw |trans({}, 'across',  locale) }}
                    {% else %}
                        {{ service.getName()|raw |trans({}, 'across',  locale) }}
                    {% endif %}
                    {% for descision_maker_email in service.getDecisionMakerEmail() %}
                        {% if descision_maker_email == receiver or isAdmin == true %}
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="{{ url('abus_portal_grantpermission', {'code': service.generateCode(lastInsertId,pin, true, email, type) }) }}">Erlauben</a>
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="{{ url('abus_portal_grantpermission', {'code': service.generateCode(lastInsertId,pin, false, email, type) }) }}">Verweigern</a>
                        {% endif %}
                    {% endfor %}
                    <br />
                {% endfor %}
            </td>
        </tr>
    </table>

    {% if isAdmin == true %}
        <br />
            <a href="{{ url('abus_portal_grantpermission', {'code': statusCode} ) }}">{{ "newregister/status"|trans({}, 'across',  locale) }}</a>
        <br />
    {% endif %}
{% endblock %}
