{% extends 'Api/email/email.html.twig' %}

{% block content %}

    {% set subdomain = ' across.' %}
    {% set path = '/register/grantpermission/' %}

    {% if legacy is defined and legacy %}
        {% set subdomain = ' across.' %}
        {% set path = '/register/grantpermission/' %}
    {% endif %}

    <table cellpadding=0 cellspacing=0 style="border: #eaeaea 1px solid; border-spacing: 0px; padding: 0px;">
        <tr>
            <td>
                <table border=0 width=100% cellpadding=0 cellspacing=0 style="border-spacing: 0px; font-family: sans-serif; font-size: 10pt; padding: 10px;">
                    <tr>
                        <td>
                            <h1 style="font-size: 12pt; color: #002F5C; font-weight: bold;">{% block headline %}{{ "ABUS Portal - Registration"|trans({}, 'across',  locale) }}{% endblock %}</h1>

                            {{ "As soon as your registration is activated you will be informed by an email."|trans({}, 'across',  locale) }}<br /><br />

                            <b>{{ "newregister/submittedData"|trans({}, 'across',  locale) }}</b>
                        </td>
                    </tr>
                </table>

                <table cellpadding=0 cellspacing=0 style="font-family: sans-serif; font-size: 10pt; padding: 5px 10px;">
                    <tr>
                        <td style="font-weight: bold;">{{ "newregister/company"|trans({}, 'across',  locale) }}</td>
                        <td>{{ company }}</td>
                    </tr>
                    <tr>
                        <td style="font-weight: bold;">{% block position %}{{ "newregister/position"|trans({}, 'across', locale) }}{% endblock %}</td>
                        <td>{{ position }}</td>
                    </tr>
                    <tr>
                        <td style="font-weight: bold;">{{ "newregister/name"|trans({}, 'across',  locale) }}</td>
                        <td>{{ gender }} {{ title }} {{ firstname }} {{ lastname }}</td>
                    </tr>
                    <tr>
                        <td style="font-weight: bold;">{{ "newregister/street"|trans({}, 'across',  locale) }}</td>
                        <td>{{ street }}</td>
                    </tr>
                    <tr>
                        <td style="font-weight: bold;">{{ "newregister/postcodeTown"|trans({}, 'across',  locale) }}</td>
                        <td>{{ postcode }} {{ town }}</td>
                    </tr>
                    <tr>
                        <td style="font-weight: bold;">{{ "newregister/country"|trans({}, 'across',  locale) }}</td>
                        <td>{{ country }}</td>
                    </tr>
                    <tr>
                        <td style="font-weight: bold;">{{ "newregister/telephone"|trans({}, 'across',  locale) }}</td>
                        <td>{{ telephone }}</td>
                    </tr>
                    <tr>
                        <td style="font-weight: bold;">{{ "newregister/email"|trans({}, 'across',  locale) }}</td>
                        <td><a href="mailto: {{ email }}">{{ email }}</a></td>
                    </tr>
                    <tr>
                        <td style="font-weight: bold;" valign="top">{{ "newregister/requestedServices"|trans({}, 'across',  locale) }}</td>
                        {% for service in requestedServices %}
                            &bull;
                            {% if service.isSubmodule() %}
                                {% set subService = service.isSubmodule() %}
                                {{ subService.getName()|raw |trans({}, 'across',  locale) }} -  {{ service.getName()|raw |trans({}, 'across',  locale) }}
                                {% else %}
                                    {{ service.getName()|raw |trans({}, 'across',  locale)  }}
                            {% endif %}
                            {% for descision_maker_email in service.getDecisionMakerEmail() %}
                                {% if descision_maker_email == receiver or isAdmin == true %}
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="{{ url('abus_portal_grantpermission', {'code': service.generateCode(lastInsertId,pin, true, email, type) }) }}">Erlauben</a>
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="{{ url('abus_portal_grantpermission', {'code': service.generateCode(lastInsertId,pin, false, email, type) }) }}">Verweigern</a>
                                {% endif %}
                            {% endfor %}
                            <br />
                        {% endfor %}
                    </tr>
                </table>
                <br />
                {% if isAdmin == true %}
                    <br />
                        <a href="{{ url('abus_portal_grantpermission', {'code': statusCode} ) }}">{{ "Check reply status"|trans({}, 'across',  locale) }}</a>
                    <br />
                {% endif %}
                <br />
            </td>
        </tr>
    </table>

{% endblock %}
