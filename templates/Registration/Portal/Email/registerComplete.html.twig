{% extends 'Api/email/email.html.twig' %}

{% block content %}

    <table border=0 width=100% cellpadding=0 cellspacing=0 style="border-spacing: 0px; font-family: sans-serif; font-size: 10pt; padding: 10px;">
        <tr>
            <td>
                <h1 style="font-size: 12pt; color: #002F5C; font-weight: bold;">{% block headline %}{{ "ABUS Portal - Registration - Complete"|trans({}, 'across', 'de_DE') }}{% endblock %}</h1>
                <b>{{ "newregister/submittedData"|trans({}, 'across', 'de_DE') }}</b>
            </td>
        </tr>
    </table>

    <table cellpadding=0 cellspacing=0 style="font-family: sans-serif; font-size: 10pt; padding: 5px 10px;">

        <tr>
            <td style="font-weight: bold;">{{ "newregister/company"|trans({}, 'across', 'de_DE') }}</td>
            <td>{{ company }}</td>
        </tr>
        <tr>
            <td style="font-weight: bold;">{% block position %}{{ "newregister/position"|trans({}, 'across', 'de_DE') }}{% endblock %}</td>
            <td>{{ position }}</td>
        </tr>
        {% if customernumber is defined and customernumber != '' %}
            <tr>
                <td style="font-weight: bold;">{{ "newregister/customernumber"|trans({}, 'across', 'de_DE') }}</td>
                <td>{{ customernumber }}</td>
            </tr>
        {% endif %}
        <tr>
            <td style="font-weight: bold;">{{ "newregister/name"|trans({}, 'across', 'de_DE') }}</td>
            <td>{{ gender == 0 ? "newregister/mr"|trans({}, 'across', 'de_DE') : "newregister/mrs"|trans({}, 'across', 'de_DE') }} {{ title }} {{ firstname }} {{ lastname }}</td>
        </tr>
        <tr>
            <td style="font-weight: bold;">{{ "newregister/street"|trans({}, 'across', 'de_DE') }}</td>
            <td>{{ street }}</td>
        </tr>
        <tr>
            <td style="font-weight: bold;">{{ "newregister/postcodeTown"|trans({}, 'across', 'de_DE') }}</td>
            <td>{{ postcode }} {{ town }}</td>
        </tr>
        <tr>
            <td style="font-weight: bold;">{{ "newregister/country"|trans({}, 'across', 'de_DE') }}</td>
            <td>{{ country }}</td>
        </tr>
        <tr>
            <td style="font-weight: bold;">{{ "newregister/telephone"|trans({}, 'across', 'de_DE') }}</td>
            <td>{{ telephone }}</td>
        </tr>
        <tr>
            <td style="font-weight: bold;">{{ "newregister/email"|trans({}, 'across', 'de_DE') }}</td>
            <td><a href="mailto: {{ email }}">{{ email }}</a></td>
        </tr>
        <tr>
            <td style="font-weight: bold;" valign="top">{{ "newregister/requestedServices"|trans({}, 'across', 'de_DE') }}</td>
            <td>
                {% for service in requestedServices %}
                {% set level = '' %}
                {% set subServices = service.getSubModules() %}

                {% if service.getPermissionGrantedAsString() == null %}
                <div style="clear: both;">
                    {% elseif service.getPermissionGrantedAsString() == 'false' %}
                    <div style="color: red; clear: both;">
                        {% elseif service.getPermissionGrantedAsString() == 'true' %}
                        <div style="color: green; clear: both;">
                            {% else %}
                            <div style="color: green; clear: both;">
                                {% set level = [' - ', service.getPermissionGrantedAsString()]|join %}
                                {% endif %}
                                &bull; {{ service.getName()|trans({}, 'across', 'de_DE')  }} {{ level }}
                                {% if service.isSubmodule() %}
                                    {% set subService = service.isSubmodule() %}
                                    {{ subService.getName()|raw |trans({}, 'across', 'de_DE') }} -  {{ service.getName()|raw |trans({}, 'across', 'de_DE') }}
                                {% endif %}
                            </div>
                            {% endfor %}
            </td>
        </tr>
    </table>
{% endblock %}
