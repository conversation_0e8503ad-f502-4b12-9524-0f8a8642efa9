{% extends 'Frame/Layout/base.html.twig' %}

{% block bodyClass %}layoutNotAuthenticated login{% endblock %}

{% block stylesheets %}
    {{ vite_css('assets/public/app/app.ts') }}
    {{ vite_css('assets/public/register/register.ts') }}
{% endblock %}

{% block body %}
<div data-role="page" id="grantpermission" class="default-background text-dark">
    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100vh; background-color: #0058A1; padding: -20px;">

        <div style="width: 980px; margin: 20px auto 0; padding: 20px; background-color: #e8e8e8;">

            <h2>ABUS Portal</h2>
            <div>Die Entscheidung für Dienst
                <b>
                    {% if sub_service_name %}
                        {{ sub_service_name|raw|trans({}, 'across') }} -
                    {% endif %}

                    {{ service_name|raw|trans({}, 'across') }}</b> wur<PERSON> für <b>{{ username }}
                </b>
                bereits getroffen.</div>
        </div>

    </div>

    {% endblock %}
