{% extends 'Frame/Layout/layout.html.twig' %}

{% block headline %}{{ "ABUS Portal - Request further services"|trans({}, 'across') }}{% endblock %}
{% block headline_description %}{% endblock %}
{% block headline_backbutton %}{% endblock %}

{% block content %}

    <div class="mt-4">

        <div class="row">
            <div class="col-9">
                <div class="card">
                    <div class="card-title">
                        {% if error is defined and not error %}
                            <div class="alert alert-success">{{ "Thank you for your kind interest in further ABUS services."|trans({}, 'across') }}</div>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        <div id="thankYou">
                            {{ "We will check your request and you will then be authorised to access these services."|trans({}, 'across') }}
                            <br /><br />
                            <b>{{ "Please observe that new services are only available after authorisation and a new log in."|trans({}, 'across') }}</b>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-3">
                <div class="text-dark ml-2">
                    <div class="card" style="width: 18rem;">
                        <div class="card-header bg-secondary text-white">
                            <div class="headline col-12"><b>{{ "newregister/notice"|trans({}, 'across') }}</b>:</div>
                        </div>
                        <div class="card-body">
                            {{ "Your request will be checked and, if approved, further services will be activated promptly. You will receive the confirmation of the activation via email to the email address provided by you."|trans({}, 'across') }}<br /><br />
                            <b class="font-weight-bold">ABUS Kransysteme GmbH</b>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

{% endblock %}

{% block footer %}{% endblock %}
