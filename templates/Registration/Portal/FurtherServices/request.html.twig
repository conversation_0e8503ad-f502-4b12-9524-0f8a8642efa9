{% extends 'Frame/Layout/layout.html.twig' %}

{% block headline %}{{ "ABUS Portal - Request further services"|trans({}, 'across') }}{% endblock %}
{% block headline_description %}{% endblock %}
{% block headline_backbutton %}{% endblock %}

{% block content %}

    <div class="mt-4">

        <div class="row">
            <div class="col-9">
                <div class="card">
                    <div class="card-title">
                        {% if error %}
                            <div class="alert alert-danger">{{ error|trans({}, 'across') }}</div>
                        {% endif %}
                    </div>
                    <div class="card-body">
                        {% if services|length > 0 %}

                            <form id="register" method="post" data-ajax="false">
                                {% for service in services %}
                                    <div class="custom-control custom-checkbox mr-sm-2 w-100 border my-2 py-2">
                                        {% if not service.isSubmodule() %}
                                            <input type="checkbox" class="custom-control-input px-2" id="{{ service.getIdentifier() }}" name="services[]" value="{{ service.getUniqueIdentifier() }}">
                                            <label class="custom-control-label" for="{{ service.getIdentifier() }}"><b class="font-weight-bold">{{ service.getName()|trans({}, 'across') }}</b><br>
                                                <span class="small">{{ service.getDescription()|trans({}, 'across') }}</span>
                                            </label>
                                        {% else %}
                                            <input type="checkbox" class="custom-control-input px-2" id="{{ service.getIdentifier() }}" name="servicesChilds[]" value="{{ service.getUniqueIdentifier() }}">
                                            <label class="custom-control-label" for="{{ service.getIdentifier() }}"><b class="font-weight-bold">{{ service.getName()|trans({}, 'across') }}</b><br>
                                                <span class="small">{{ service.getDescription()|trans({}, 'across') }}</span>
                                            </label>
                                        {% endif %}
                                    </div>
                                {%  endfor %}
                                <input type="submit" name="next" id="next" class="btn btn-warning w-100" value="{{ "Request"|trans({}, 'across') }}" />
                            </form>
                        {% else %}
                            <div class="alert alert-info">{{ "There are no further services to request"|trans({}, 'across') }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="col-3">
                <div class="text-dark ml-2">
                    <div class="card" style="width: 18rem;">
                        <div class="card-header bg-secondary text-white">
                            <div class="col-12 text-center"><b>{{ "newregister/notice"|trans({}, 'across') }}</b>:</div>
                        </div>
                        <div class="card-body">
                            {{ "Your request will be checked and, if approved, further services will be activated promptly. You will receive the confirmation of the activation via email to the email address provided by you."|trans({}, 'across') }}<br /><br />
                            <b class="font-weight-bold">ABUS Kransysteme GmbH</b>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

{% endblock %}

{% block footer %}{% endblock %}
