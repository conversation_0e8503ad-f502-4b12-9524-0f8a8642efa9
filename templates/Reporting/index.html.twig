{% extends 'Frame/Layout/layout.html.twig' %}

{% block headline %}{{ "reporting/users/list"|trans({}, 'across') }}{% endblock %}
{% block headline_description %}{% endblock %}
{% block headline_backbutton %}{% endblock %}

{% block content %}

    {% if users|length <= 0 %}
        <div class="alert alert-danger my-2 d-flex justify-content-between">
            <span>Es gibt keine Daten zu Ihrer Suche!</span>
            <a href="{{ path('abus_reporting_users') }}">Filter zurücksetzen</a>
        </div>
    {% endif %}

    <div id="appController">
        <form action="" method="post" name="form-users-list" enctype="multipart/form-data" data-ajax=false>
            <input type="text" class="form-control mt-2 p-2" name="searchUser" placeholder="Vor- oder Nachname des Benutzers" value="{{ searchUser }}">
            <input type="text" class="form-control mt-2 p-2" name="searchCompany" placeholder="Firma des Benutzers" value="{{ searchCompany }}">
            <input type="text" class="form-control mt-2 p-2" name="searchEmail" placeholder="E-Mail des Benutzers" value="{{ searchEmail }}">
            <select class="form-control text-center mt-3" name="searchGroup">
                <option value="" selected>Gruppe des Benutzers</option>
                {% for group in groups %}
                    <option value="{{ group.name }}" {% if group.name == searchGroup %}selected="selected"{% endif %}>{% if group.name_nice != '' %}{{ group.name_nice }}{% else %}{{ group.name }}{% endif %}</option>
                {% endfor %}
            </select>
            <br />

            <button type="submit" class="btn btn-primary w-100 mt-2" name="submit">Benutzer suchen</button>

            {% if users|length > 0 %}

                <div style="border-top: #888888 1px solid; margin-top: 20px; margin-bottom: 20px;"></div>

                <list-users
                        :users="{{ users | json_encode }}"
                        path="{{ path('abus_reporting_user_show') }}"
                ></list-users>

            {% endif %}

        </form>
        {% if users|length > 0 %}
            {# Liste ausdrucken. aber die filterung behalten #}
            <form action="" method="post">
                <input type="hidden" name="searchUser" value="{{ searchUser }}">
                <input type="hidden" name="searchCompany" value="{{ searchCompany }}">
                <input type="hidden" name="searchEmail" value="{{ searchEmail }}">
                <input type="hidden" name="searchGroup" value="{{ searchGroup }}">
                <input type="hidden" name="excel">

                <button type="submit" class="btn btn-primary w-100 mb-4">Liste ausdrucken</button>

            </form>
        {% endif %}
    </div>

{% endblock %}

{% block footer %}{% endblock %}
