{% extends 'Frame/Layout/layout.html.twig' %}

{% block headline %}{{ "reporting/users/show"|trans({}, 'across') }}{% endblock %}
{% block headline_description %}{% endblock %}
{% block headline_backbutton %}{% endblock %}

{% block content %}
    <div class="card mt-5">
        <div class="card-header">
            <span class="font-weight-bold">ABUS Portal - Reporting 2.0 - User N° {{ reportingUser.id }}</span>
        </div>
        <div class="card-body">
           <div class="row">
               <div class="col-6">
                   <p><span class="font-weight-bold pr-4">Name:</span> {{ reportingUser.lastname ? reportingUser.lastname ~ ',' : '' }} {{ reportingUser.firstname }}</p>
                   <p><span class="font-weight-bold pr-4">Firma:</span> {{ reportingUser.company }}</p>
                   <p><span class="font-weight-bold pr-4">E-Mail:</span><a href="mailto:{{ reportingUser.email }}">{{ reportingUser.email }}</a></p>
                   <p><span class="font-weight-bold pr-4">Telefon:</span> {{ reportingUser.telephone }}</p>
               </div>
               <div class="col-6">
                   <p><span class="font-weight-bold pr-4">Letzter Login Portal:</span> {% if reportingUser.lastvisit != '' %}{{ reportingUser.lastvisit|date("d.m.y H:i:s") }}{% endif %}</p>
                   <p>
                       <span class="font-weight-bold pr-4">Gruppen:</span>
                       {% for group in reportingUser.groups %}{% if group.name_nice != '' %} <span class="badge badge-pill badge-info">{{ group.name_nice }}</span>{% else %} <span class="badge badge-pill badge-secondary">{{ group.name }}</span> {% endif %}{% endfor %}
                   </p>
               </div>
           </div>
        </div>
    </div>

{% endblock %}

{% block footer %}{% endblock %}
