# Nextcloud Integration - Fehlerbehebung

## Problem
```
cURL error 7: Failed to connect to nextcloud.development.konstantin port 443 after 5 ms: 
Couldn't connect to server for https://nextcloud.development.konstantin/remote.php/dav/files/portal/%2F
```

## Ursache
Die Portal-Anwendung versuchte über die externe URL `https://nextcloud.development.konstantin:443` auf Nextcloud zuzugreifen, aber:

1. **Netzwerk-Problem**: Der `nextcloud_nginx` Container war nicht im `portal-net` Netzwerk
2. **URL-Problem**: Externe HTTPS-URL nicht erreichbar, interne Container verwenden HTTP/Port 80

## Lösung

### 1. Nextcloud URL in Portal-Konfiguration ändern
**Datei**: `/srv/portal/data/www/.env.local`

```bash
# Vorher:
NEXTCLOUD_URL=https://nextcloud.development.konstantin

# Nachher:
NEXTCLOUD_URL=http://nextcloud_nginx
```

**Hinweis**: Der "portal" Benutzer muss in Nextcloud existieren und das korrekte Passwort haben.

### 2. Nextcloud Container ins Portal-Netzwerk einbinden
**Datei**: `/var/docker/nextcloud/common.yml`

```yaml
# Service: nextcloud_nginx
networks:
  - nextcloud-net
  - traefik-net
  - portal-net  # <- hinzugefügt
```

### 3. Container neu starten
```bash
# Portal Container neu starten
cd /var/docker/portal
docker compose -f docker-compose.dev.yml restart portal_php-fpm

# Nextcloud Container neu erstellen (für Netzwerk-Änderung)
cd /var/docker/nextcloud
docker compose -f docker-compose.dev.yml up -d nextcloud_nginx
```

## Verifikation
```bash
# Test der internen Verbindung
docker exec portal_php-fpm curl -I http://nextcloud_nginx/remote.php/dav/

# Erwartete Antwort: HTTP/1.1 400 Bad Request (normal ohne Host-Header)
```

## Vorteile der Lösung
- ✅ Direkte Container-zu-Container Kommunikation
- ✅ Keine Abhängigkeit von externer DNS-Auflösung
- ✅ Bessere Performance (kein Routing über externe Netzwerke)
- ✅ Zuverlässigere Verbindung
- ✅ Automatische Netzwerk-Konfiguration bei Container-Neustart

## Netzwerk-Übersicht
```
portal-net:
├── portal_php-fpm
├── portal_nginx
├── portal_db
├── portal_redis
├── nextcloud_nginx  # <- jetzt auch hier
└── nextcloud_fmp
```

## Benutzer-Setup in Nextcloud
Falls der "portal" Benutzer noch nicht existiert:

1. **Nextcloud Web-Interface öffnen**: `https://nextcloud.development.konstantin`
2. **Als Admin anmelden**: `admin` / `nextcloudpwd`
3. **Benutzer erstellen**:
   - Benutzername: `portal`
   - Passwort: `264N-FU2p8LZ[pBD`
4. **Dateien hochladen** in den portal-Benutzer-Ordner

## Hinweise
- Die externe URL `https://nextcloud.development.konstantin` funktioniert weiterhin für Browser-Zugriff über Traefik
- Interne API-Calls verwenden jetzt die direkte Container-Kommunikation
- Bei zukünftigen Container-Updates bleibt die Netzwerk-Konfiguration erhalten
- Der "portal" Benutzer sollte für die Produktion verwendet werden (nicht "admin")