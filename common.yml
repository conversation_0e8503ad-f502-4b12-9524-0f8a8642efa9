version: '2'

services:
  nextcloud_db:
    build: ./mariadb
    container_name: ${NAMEPREFIX}_db
    hostname: ${NAMEPREFIX}_db
    volumes:
      - /srv/${NAMEPREFIX}/data/mariadb:/var/lib/mysql
      - /srv/${NAMEPREFIX}/data/exchange:/data_exchange
    env_file:
      - ./.secrets
    environment:
      - MYSQL_DATABASE=${NAMEPREFIX}
      - MYSQL_USER=${NAMEPREFIX}
    networks:
      - nextcloud-net
      - database-net
      - portal-net

  nextcloud_fpm:
    build:
      context: .
      dockerfile: nextcloud/Dockerfile
    container_name: ${NAMEPREFIX}_fpm
    hostname: ${NAMEPREFIX}_fpm
    volumes:
      - /srv/${NAMEPREFIX}/data/localstorage:/srv/data
      - /srv/${NAMEPREFIX}/data/www:/var/www/html
      - /srv/${NAMEPREFIX}/data/data:/var/www/data
      - /srv/${NAMEPREFIX}/logs/cron:/var/log/cron
      - /srv/${NAMEPREFIX}/data/exchange:/data_exchange
    env_file:
      - ./.secrets
    environment:
      - MYSQL_DATABASE=${NAMEPREFIX}
      - MYSQL_USER=${NAMEPREFIX}
      - MYSQL_HOST=${NAMEPREFIX}_db
      - NEXTCLOUD_ADMIN_USER=admin
      - NEXTCLOUD_DATA_DIR=/var/www/data
      - NO_PROXY=elasticsearch
    networks:
      - nextcloud-net
      - portal-net
      - elasticsearch-net

  nextcloud_nginx:
    build:
      context: .
      dockerfile: nginx/Dockerfile
    container_name: ${NAMEPREFIX}_nginx
    hostname: ${NAMEPREFIX}_nginx
    volumes:
      - /srv/${NAMEPREFIX}/data/www:/var/www/html
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.${NAMEPREFIX}-secure.entrypoints=${TRAEFIK_ENTRYPOINT}"
      - "traefik.http.routers.${NAMEPREFIX}-secure.service=${NAMEPREFIX}"
      - "traefik.http.routers.${NAMEPREFIX}-secure.tls=true"
      - "traefik.http.services.${NAMEPREFIX}.loadbalancer.server.port=80"
    networks:
      - nextcloud-net
      - traefik-net

  nextcloud_redis:
    build:
      context: .
      dockerfile: redis/Dockerfile
    container_name: ${NAMEPREFIX}_redis
    hostname:  ${NAMEPREFIX}_redis
    volumes:
      - /srv/${NAMEPREFIX}/data/redis:/data
    networks:
      - nextcloud-net
      - database-net

#  nextcloud_webdav:
#    build:
#      context: .
#      dockerfile: webdav/Dockerfile
#    container_name: ${NAMEPREFIX}_webdav
#    hostname: ${NAMEPREFIX}_webdav
#    volumes:
#      - /nextcloud-webdav-volume:/webdav
#    devices:
#      - /dev/fuse:/dev/fuse
#    cap_add:
#      - SYS_ADMIN
#    security_opt:
#      - apparmor:unconfined
#    networks:
#      - nextcloud-webdav-net
