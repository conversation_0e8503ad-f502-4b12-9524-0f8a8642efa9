{"name": "abus/portal", "type": "project", "license": "proprietary", "description": "ABUS Kransysteme GmbH Portal", "require": {"php": "^8.3.19", "ext-json": "*", "ext-pdo": "*", "ext-simplexml": "*", "doctrine/doctrine-bundle": "2.13.*", "doctrine/doctrine-migrations-bundle": "3.4.*", "doctrine/orm": "^3.2.3", "firebase/php-jwt": "^v6.11.1", "graylog2/gelf-php": "^1.7.1", "guzzlehttp/guzzle": "^7.9.3", "knplabs/knp-snappy": "^v1.5.1", "knplabs/knp-snappy-bundle": "*", "league/html-to-markdown": "^5.1.1", "mainick/keycloak-client-bundle": "*", "maxmind-db/reader": "~v1.12.0", "mobiledetect/mobiledetectlib": "^2.8", "phpdocumentor/reflection-docblock": "^5.6.1", "phpstan/phpdoc-parser": "^2.1.0", "sabre/dav": "^4.7.0", "smalot/pdfparser": "^v2.12.0", "symfony/asset": "7.2.*", "symfony/browser-kit": "7.2.*", "symfony/config": "7.2.*", "symfony/console": "7.2.*", "symfony/expression-language": "7.2.*", "symfony/form": "7.2.*", "symfony/framework-bundle": "7.2.*", "symfony/http-client": "7.2.*", "symfony/intl": "7.2.*", "symfony/ldap": "7.2.*", "symfony/mailer": "7.2.*", "symfony/monolog-bundle": "^v3.10.0", "symfony/property-access": "7.2.*", "symfony/property-info": "7.2.*", "symfony/routing": "7.2.*", "symfony/runtime": "7.2.*", "symfony/security-bundle": "7.2.*", "symfony/serializer": "7.2.*", "symfony/stopwatch": "7.2.*", "symfony/translation": "7.2.*", "symfony/twig-bundle": "7.2.*", "symfony/validator": "7.2.*", "symfony/web-profiler-bundle": "7.2.*", "symfony/webpack-encore-bundle": "^v2.2.0", "symfony/workflow": "7.2.*", "symfony/yaml": "7.2.*", "twig/extra-bundle": "*", "twig/intl-extra": "*"}, "require-dev": {"doctrine/instantiator": "^1.5.0", "myclabs/deep-copy": "^1.13.0", "sebastian/code-unit-reverse-lookup": "^4.0.1", "sebastian/comparator": "^6.3.1", "sebastian/diff": "^6.0.2", "sebastian/environment": "^6.1.0", "sebastian/exporter": "^6.3.0", "sebastian/global-state": "^7.0.2", "sebastian/object-enumerator": "^6.0.1", "sebastian/recursion-context": "^6.0.2", "sebastian/resource-operations": "^3.0.4", "sebastian/version": "^5.0.2", "symfony/dotenv": "7.2.*", "symfony/flex": "^v2.5.0", "symfony/maker-bundle": "^v1.62.1", "symfony/phpunit-bridge": "7.2.*"}, "config": {"preferred-install": {"*": "dist"}, "sort-packages": true, "allow-plugins": {"ocramius/package-versions": true, "symfony/flex": true, "symfony/runtime": true}}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"id": "01C0V90CFGXEFWTE8BYGSPSY3X", "allow-contrib": false, "require": "7.2.*"}}}