{"compilerOptions": {"target": "ES2020", "module": "ESNext", "lib": ["ES2020", "DOM"], "moduleResolution": "node", "strict": true, "allowJs": true, "sourceMap": true, "outDir": "./public/build/", "noImplicitAny": true, "noImplicitThis": false, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "strictPropertyInitialization": false, "resolveJsonModule": true, "baseUrl": ".", "paths": {"EventBuses*": ["assets/protected/vue/eventBuses*"], "Assets*": ["assets*"], "Flags*": ["node_modules/svg-country-flags*"], "@flags/*": ["node_modules/svg-country-flags/*"], "@assets/*": ["assets/*"], "@translations/*": ["translations/*"]}}, "exclude": ["node_modules", "vendor"], "include": ["./**/*.ts", "./**/*.vue"]}