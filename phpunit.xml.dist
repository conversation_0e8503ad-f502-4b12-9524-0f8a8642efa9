<?xml version="1.0" encoding="UTF-8"?>

<!-- https://phpunit.de/manual/current/en/appendixes.configuration.html -->
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="http://schema.phpunit.de/6.5/phpunit.xsd"
         backupGlobals="false"
         colors="true"
         bootstrap="vendor/autoload.php"
>
    <php>
        <ini name="error_reporting" value="-1" />
        <env name="KERNEL_CLASS" value="App\Kernel" />
        <env name="APP_ENV" value="test" />
        <env name="APP_DEBUG" value="1" />
        <env name="APP_SECRET" value="s$cretf0rt3st" />
        <env name="SHELL_VERBOSITY" value="-1" />
        <!-- define your env variables for the test env here -->

        <env name="DOMAIN" value="development.dirk" />
        <env name="COOKIE_DOMAIN" value=".development.dirk" />

        <env name="GRAYLOG_SERVER" value="192.168.51.251" />
        <env name="GRAYLOG_PORT" value="12201" />
        <env name="LDAP_READ_PASSWORD" value="mv%X62@p6RQ9xJ3S6M" />
        <env name="LDAP_WRITE_PASSWORD" value="Svf?Sb7f5JJe%t@r" />
        <env name="PORTAL_DATABASE_PASSWORD" value="UspZbcwxMEWxzVc3" />
        <env name="BOX_DATABASE_PASSWORD" value="UKQCtNUVqRSne59Z" />
        <env name="TASKRUNNER_DATABASE_PASSWORD" value="rjwhTgkrkZkL2e7w" />
        <env name="INFORMATION_DATABASE_PASSWORD" value="Jpe8We9PezXfEgYR" />
        <env name="NEWS_DATABASE_PASSWORD" value="ADnpwTvqsIUdqdki" />
        <env name="USERMANAGEMENT_DATABASE_PASSWORD" value="FhIx7HMYgdWliBI5" />
        <env name="CATALOG_DATABASE_PASSWORD" value="catalogcreator" />

        <env name="USER_INITIAL_PASSWORD" value="CP8HrGQ$4azg" />

        <env name="DN_USERS" value="OU=Benutzer,OU=UnitTests,OU=DMZ,DC=abus-vpn,DC=de" />
        <env name="DN_COMPANIES" value="OU=Firmen,OU=UnitTests,OU=DMZ,DC=abus-vpn,DC=de" />
        <env name="DN_GROUPS" value="OU=Gruppen,OU=UnitTests,OU=DMZ,DC=abus-vpn,DC=de" />

        <env name="REDIS_DSN" value="redis://abus_portal2018_redis:6379" />
        <env name="REDIS_COMPANYCATEGORY" value="unittest_companyCategory" />
        <env name="REDIS_COMPANY" value="unittest_company" />
        <env name="REDIS_GROUP" value="unittest_group" />
        <env name="REDIS_USER" value="unittest_user" />

        <env name="NEXTCLOUD_URL" value="https://nextcloud.development.dirk" />
        <env name="NEXTCLOUD_VERIFY_SSL" value="0" />
        <env name="NEXTCLOUD_USER" value="admin" />
        <env name="NEXTCLOUD_PASSWORD" value="yJ9kM-sKAY4-oEcZd-XFLFZ-D48pN" />
        <env name="NEXTCLOUD_DATABASE_PASSWORD" value="nextcloud" />

        <env name="NEXTCLOUD_CAD_EXCHANGE_FOLDER" value="CADExchange" />
        <env name="NEXTCLOUD_CAD_EXCHANGE_EMAIL" value="<EMAIL>" />
        <env name="NEXTCLOUD_CAD_EXCHANGE_USER" value="CADExchange" />
        <env name="NEXTCLOUD_CAD_EXCHANGE_PASSWORD" value="JL3eHdGut7" />
        <env name="NEXTCLOUD_CAD_EXCHANGE_DELETE_AFTER_SECONDS" value="2419200" />

        <!-- ###+ symfony/framework-bundle ### -->
        <env name="APP_ENV" value="dev"/>
        <env name="APP_SECRET" value="0ce23ac4f37cf74977446ff8eea64adf"/>
        <!-- env name="TRUSTED_PROXIES" value="*********/8,10.0.0.0/8,**********/12,***********/16" -->
        <!-- env name="TRUSTED_HOSTS" value="'^(localhost|example\.com)$'" -->
        <!-- ###- symfony/framework-bundle ### -->

        <!-- ###+ doctrine/doctrine-bundle ### -->
        <!-- Format described at https://www.doctrine-project.org/projects/doctrine-dbal/en/latest/reference/configuration.html#connecting-using-a-url -->
        <!-- IMPORTANT: You MUST configure your server version, either here or in config/packages/doctrine.yaml -->
        <!--  -->
        <!-- DATABASE_URL="sqlite:///%kernel.project_dir%/var/data.db" -->
        <!-- DATABASE_URL="mysql://db_user:db_password@127.0.0.1:3306/db_name?serverVersion=5.7" -->
        <env name="DATABASE_URL" value="postgresql://db_user:db_password@127.0.0.1:5432/db_name?serverVersion=13&amp;charset=utf8"/>
        <!-- ###- doctrine/doctrine-bundle ### -->
    </php>

    <testsuites>
        <testsuite name="Project Test Suite">
            <directory>tests/</directory>
        </testsuite>
    </testsuites>

    <filter>
        <whitelist>
            <directory>./src/</directory>
        </whitelist>
    </filter>

    <listeners>
        <listener class="Symfony\Bridge\PhpUnit\SymfonyTestsListener" />
    </listeners>
</phpunit>
