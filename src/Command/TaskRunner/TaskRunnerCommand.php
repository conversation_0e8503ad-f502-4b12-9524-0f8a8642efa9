<?php

namespace App\Command\TaskRunner;

use App\Model\TaskRunner\TaskExecutor;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class TaskRunnerCommand extends Command
{
    /** @var TaskExecutor */
    private $taskExecutor;

    /**
     * TaskRunnerCommand constructor.
     */
    public function __construct(TaskExecutor $taskExecutor)
    {
        parent::__construct();
        $this->taskExecutor = $taskExecutor;
    }

    protected function configure()
    {
//        parent::configure();
        $this
        ->setName('taskrunner:run:all')
        ->setDescription('Runs all open tasks')
        ;
    }

    /**
     * @return int|void|null
     *
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->taskExecutor->runAllOpenTasks();

//        $now = time();
//        $crawlerManagement = $this->getContainer()->get('abus_box.crawlermanagement');
//        $path = $input->getArgument('path');
//        $pdf = $input->getArgument('pdf');
//
//        if( $pdf == '' ) $pdf = false;
//
//        if ($path) {
//            $output->writeln('Crawl ' . $path);
//            $crawlerManagement->crawler($path, $pdf, true);
//        } else {
//            $output->writeln('Crawl /');
//            $crawlerManagement->crawler(false, $pdf, true);
//        }
//
//        $output->writeln('Time elapsed: ' . (time() - $now) . ' seconds' );

        return Command::SUCCESS;
    }
}
