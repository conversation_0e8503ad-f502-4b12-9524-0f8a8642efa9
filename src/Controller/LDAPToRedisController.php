<?php

declare(strict_types=1);

namespace App\Controller;

use App\Model\Api\LDAP2Redis\LDAP2Redis;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class LDAPToRedisController extends AbstractController
{
    public function __construct(
        private readonly LDAP2Redis $LDAP2Redis
    )
    {
    }

    #[Route('/ldapto-redis')]
    public function index(): Response
    {
//        $this->LDAP2Redis->storeCompanyCategories();
//        $this->LDAP2Redis->storeCompanies();
        $this->LDAP2Redis->storeGroups();
        $this->LDAP2Redis->storeUsers();

        return new Response('Hello World!');
    }
}
