<?php
/**
 * Ersteller: <PERSON>
 * E-Mail: <EMAIL>
 * Datum: 27.04.18 07:00.
 */

namespace App\Controller\Box;

use App\Exception\Box\Nextcloud\NextcloudException;
use App\Exception\Box\Nextcloud\NotAuthenticatedException;
use App\Model\Api\PortalUser\PortalUserInterface;
use App\Model\Api\Setting\SettingRepositoryInterface;
use App\Model\Box\Nextcloud\Connector\ElasticSearch\ElasticSearchConnector;
use App\Model\Box\Nextcloud\Connector\Webdav\WebdavConnectorGuzzle;
use App\Model\Box\Nextcloud\Connector\WebdavConnectorInterface;
use App\Model\Box\Nextcloud\Database\Nextcloud\NextcloudDatabaseConnectorMySQL;
use App\Model\Box\Nextcloud\FileZipper\FileZipper;
use App\Model\Box\Permission\PermissionChecker;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class NextcloudController extends AbstractController
{
    /**
     * @throws \App\Exception\FileInDatabaseNotFoundException
     * @throws \App\Exception\PermissionNotHandledException
     */
    #[Route('/box/nextcloud', name: 'abus_nextcloud_home', methods: ['GET'])]
    public function index(PortalUserInterface $portalUser, PermissionChecker $permissionChecker): Response
    {
        $portalUser->fetch();

        return $this->render(
            'Box/Frontend/nextcloud.html.twig',
            [
                'user' => $portalUser,
                'openModule' => 'box',
                'isAdmin' => $permissionChecker->isAdmin($portalUser),
                'openFolder' => '',
            ]
        );
    }

    #[Route('/box/nextcloud/downloadZip', name: 'abus_box_nextcloud_download_zip', methods: ['POST'])]
    public function downloadZipAction(Request $request, FileZipper $fileZipper): BinaryFileResponse
    {
        return $fileZipper->getZippedFiles($request->request->get('files'));
    }

    /**
     * @param WebdavConnectorInterface $nextcloudConnector
     *
     * @return JsonResponse
     *
     * @throws NextcloudException
     * @throws NotAuthenticatedException
     */
    #[Route('/box/nextcloud/search', name: 'abus_box_nextcloud_search', methods: ['GET'])]
    public function search(Request $request, PortalUserInterface $portalUser, WebdavConnectorGuzzle $nextcloudConnector, ElasticSearchConnector $elasticSearchConnector)
    {
        $portalUser->fetch();

        $searchResults = $elasticSearchConnector->search(
            $request->get('search'),
            $request->get('language'),
            $request->get('folder')
        );

        $searchResults['files'][] = $searchResults['total'];

        return new JsonResponse($searchResults['files']);
    }

    /**
     * @param WebdavConnectorInterface $nextcloudConnector
     *
     * @return JsonResponse
     *
     * @throws NextcloudException
     * @throws NotAuthenticatedException
     */
    #[Route('/box/nextcloud/searchDocumentation', name: 'abus_box_nextcloud_search_documentation', methods: ['GET'])]
    public function searchDocumentation(Request $request, PortalUserInterface $portalUser, WebdavConnectorGuzzle $nextcloudConnector, ElasticSearchConnector $elasticSearchConnector)
    {
        $portalUser->fetch();

        $currentOnly = ('true' === $request->get('currentOnly'));
        $archiveOnly = ('true' === $request->get('archiveOnly'));

        $searchResults = $elasticSearchConnector->search(
            $request->get('search'),
            $request->get('language'),
            $request->get('folder'),
            true,
            $currentOnly,
            $archiveOnly
        );

        $searchResults['files'][] = $searchResults['total'];

        return new JsonResponse($searchResults['files']);
    }

    #[Route('/box/nextcloud/getDocumentationFolders', name: 'abus_box_nextcloud_documentation_folders', methods: ['GET'])]
    public function getDocumenatationFolders(): JsonResponse
    {
        return new JsonResponse([
            'abuliner' => 'box/search/abuliner',
            'allgemein' => 'box/search/allgemein',
            'elektronik' => 'box/search/elektronik',
            'funk' => 'box/search/funk',
            'hb-system' => 'box/search/hb-system',
            'kettenzug' => 'box/search/kettenzug',
            'laufkran' => 'box/search/laufkran',
            'leichtportalkran' => 'box/search/leichtportalkran',
            'schwenkkran' => 'box/search/schwenkkran',
            'seilzug' => 'box/search/seilzug',
        ]);
    }

    #[Route('/box/nextcloud/latestFiles/saveSettings', name: 'abus_box_nextcloud_latest_files_save_settings', methods: ['POST'])]
    public function getLatestFilesSaveSettings(Request $request, PortalUserInterface $portalUser, SettingRepositoryInterface $settingRepository): JsonResponse
    {
        $portalUser->fetch();

        $settings = $request->request->get('settings');

        if (isset($settings['language']) && isset($settings['time']) && isset($settings['limit'])) {
            $language = filter_var($settings['language'], \FILTER_SANITIZE_STRING);
            $time = filter_var($settings['time'], \FILTER_SANITIZE_NUMBER_INT);
            $limit = filter_var($settings['limit'], \FILTER_SANITIZE_NUMBER_INT);

            $settingRepository->setBoxDashboard($portalUser->getUser()->getUsername(), $language, $time, $limit);
        }

        return new JsonResponse();
    }

    #[Route('/box/nextcloud/latestFiles/loadSettings', name: 'abus_box_nextcloud_latest_files_load_settings', methods: ['GET'])]
    public function getLatestFilesLoadSettings(PortalUserInterface $portalUser, SettingRepositoryInterface $settingRepository): JsonResponse
    {
        $portalUser->fetch();

        return new JsonResponse($settingRepository->getBoxDashboard($portalUser->getUser()->getUsername()));
    }

    #[Route('/box/nextcloud/latestFiles/{language}/{time}/{limit}', name: 'abus_box_nextcloud_latest_files', requirements: ['language' => '\w*', 'time' => '\d+', 'limit' => '\d+'], methods: ['GET'])]
    public function getLatestFiles(ElasticSearchConnector $elasticSearchConnector, NextcloudDatabaseConnectorMySQL $nextcloudDatabaseConnectorMySQL, string $language, int $time, int $limit): JsonResponse
    {
        $fileIds = $nextcloudDatabaseConnectorMySQL->getLatestFileIds($time);

        return new JsonResponse($elasticSearchConnector->getFilesByIds($language, $fileIds, $limit));
    }

    #[Route('/box/nextcloud/get/{path}', name: 'abus_box_nextcloud_root', requirements: ['path' => '.+'], methods: ['GET'])]
    public function getFolder(WebdavConnectorGuzzle $connector, string $path = '/'): JsonResponse
    {
        try {
            return new JsonResponse($connector->getFolder($path));
        } catch (NextcloudException $e) {
            return new JsonResponse(['code' => $e->getCode(), 'message' => $e->getMessage()], 500);
        } catch (NotAuthenticatedException $e) {
            return new JsonResponse(['code' => $e->getCode(), 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * @throws NextcloudException
     * @throws NotAuthenticatedException
     */
    #[Route('/box/nextcloud/download/{path}', name: 'abus_box_nextcloud_file', requirements: ['path' => '.+'], methods: ['GET'])]
    public function getFile(WebdavConnectorGuzzle $connector, string $path): BinaryFileResponse
    {
        return $connector->getFile('/'.$path);
    }

    /**
     * @return BinaryFileResponse
     */
    #[Route('/box/nextcloud/{path}', name: 'abus_box_nextcloud_open', requirements: ['path' => '.+'], methods: ['GET'])]
    public function open(PortalUserInterface $portalUser, PermissionChecker $permissionChecker, string $path): Response
    {
        $portalUser->fetch();

        $trailingPath = '';
        if (!preg_match('/\.(?!.*\.)((?!\d+$).{2,4})$/', $path)) {
            $trailingPath = '/';
        }

        return $this->render(
            'Box/Frontend/nextcloud.html.twig',
            [
                'user' => $portalUser,
                'openModule' => 'box',
                'isAdmin' => $permissionChecker->isAdmin($portalUser),
                'openFolder' => urldecode('/'.$path.$trailingPath),
            ]
        );
    }
}
