<?php

/**
 * <PERSON><PERSON><PERSON>: <PERSON>
 * E-Mail: <EMAIL>
 * Datum: 29.08.17 13:00.
 */

namespace App\Controller\Box;

use App\Entity\TaskRunner\Task;
use App\Model\Api\PortalUser\PortalUserInterface;
use App\Model\Box\Content\Loader;
use App\Model\Box\Crawler\Crawler;
use App\Model\Box\Download\Download;
use App\Model\Box\Permission\PermissionChecker;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

class BoxController extends AbstractController
{
    /**
     * Nur zum Testen der Crawlfunktion. Kann im Livesystem auskommentiert werden.
     *
     * @throws \App\Exception\BrokenLinkException
     * @throws \App\Exception\FileInDatabaseNotFoundException
     * @throws \Doctrine\ORM\ORMException
     * @throws \Doctrine\ORM\OptimisticLockException
     */
    #[Route('/box/crawl', name: 'abus_box_crawl', methods: ['GET'])]
    public function crawlTest(Crawler $crawler)
    {
        $task = new Task();
        $task->setBundleName('abus_box');
        $task->setParameters(['path' => '/', 'pdf' => false]);
        $crawler->crawl($task);
    }

    /**
     * @param string $path
     *
     * @throws \App\Exception\FileInDatabaseNotFoundException
     * @throws \App\Exception\PermissionNotHandledException
     */
    #[Route('/box', name: 'abus_box_home', methods: ['GET'])]
    public function index(PortalUserInterface $portalUser, PermissionChecker $permissionChecker, Loader $loader, $path = '/'): Response
    {
        $portalUser->fetch();

        if ($loader->isFile($path)) {
            return $loader->loadFile($path, $portalUser);
        }

        return $this->render(
            'Box/Frontend/index.html.twig',
            [
                'user' => $portalUser,
                'openModule' => 'box',
                'folder' => $path,
                'folderName' => '',
                'languages' => $loader->getLanguages(),
                'isAdmin' => $permissionChecker->isAdmin($portalUser),
            ]
        );
    }

    /**
     * @return mixed
     *
     * @throws \App\Exception\PermissionNotHandledException
     */
    #[Route('/box/download/{path}', name: 'abus_box_download', requirements: ['path' => '.+'], methods: ['GET'])]
    public function download(Download $download, string $path): Response
    {
        return $download->download($path);
    }
}
