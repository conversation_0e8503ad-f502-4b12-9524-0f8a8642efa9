<?php

/**
 * Created by PhpStorm.
 * User: dblicker
 * Date: 25.05.18
 * Time: 07:23.
 */

namespace App\Model\Box\Nextcloud\Connector\Webdav;

use App\Exception\Box\Nextcloud\DoNotListException;
use App\Model\Api\Language\LanguageSanitizer;
use App\Model\Api\Locale\Locale;
use App\Model\Box\Nextcloud\ConfigBuilder\Enricher;
use App\Model\Box\Nextcloud\ConfigBuilder\Merger;
use App\Model\Box\Nextcloud\inMemory\inMemoryConnectorInterface;
use App\Model\Box\Nextcloud\Permission\PermissionChecker;
use Symfony\Component\Serializer\SerializerInterface;

class XmlToArray
{
    private $serializer;
    private $inMemoryConnector;

    /** @var Enricher */
    private $enricher;

    /** @var Merger */
    private $merger;

    /** @var PermissionChecker */
    private $permissionChecker;

    private $element;

    private LanguageSanitizer $languageSanitizer;

    /**
     * XmlToArray constructor.
     */
    public function __construct(SerializerInterface $serializer, inMemoryConnectorInterface $inMemoryConnector, Enricher $enricher, Merger $merger, PermissionChecker $permissionChecker, LanguageSanitizer $languageSanitizer)
    {
        $this->serializer = $serializer;
        $this->inMemoryConnector = $inMemoryConnector;
        $this->enricher = $enricher;
        $this->merger = $merger;
        $this->permissionChecker = $permissionChecker;
        $this->languageSanitizer = $languageSanitizer;
    }

    /**
     * Wandelt ein SEARCH Result in ein Array um.
     */
    protected function searchToArray(string $xml, string $path, string $language = null, bool $listYml = false): array
    {
        $object = simplexml_load_string(str_replace('oc:', '', str_replace('d:', '', $xml)));

        $search = [];

        foreach ($object->response as $value) {
            try {
                $search[] = $this->simpleXMLElementToArray($value, $path, $language, $listYml, true);
            } catch (DoNotListException $doNotListException) {
                // Dann eben nicht
            }
        }

        return $this->sortSearchResults($search);
    }

    /**
     * Sortiert die Suchergebnisse alphabetisch, weil die Sortierung über die Nextcloudanfrage nicht funktioniert.
     */
    private function sortSearchResults(array $searchResults): array
    {
        usort($searchResults, function ($a, $b) {
            return $a['filename'] > $b['filename'];
        });

        return $searchResults;
    }

    /**
     * Wandelt ein SimpleXMLElement in ein Array um.
     *
     * @throws DoNotListException
     */
    protected function simpleXMLElementToArray(\SimpleXMLElement $simpleXMLElement, string $path, string $language = null, bool $configParser = false, bool $parent = false): array
    {
        $locale = $this->languageSanitizer->sanitize( $this->languageSanitizer->getLocale('en'), true);

        $this->element = json_decode(json_encode($simpleXMLElement), true);

        $properties = [];

        $properties['fullpath'] = $this->enricher->extractFullPath($this->element['href']);
        $properties['fullpathBase64encoded'] = base64_encode($properties['fullpath']);
        $properties['extension'] = $this->enricher->extractExtension($properties['fullpath']);

        $mergedConfiguration = $this->merger->mergeConfigTree($this->enricher->getConfigTree($properties['fullpath']));

        // Darf der User die Datei/das Verzeichnis sehen?
        if (false === $configParser && false === $this->permissionChecker->isAccessGranted($properties['fullpath'], $mergedConfiguration)) {
            throw new DoNotListException();
        }

        // Yaml Dateien nicht anzeigen
        if (false === $configParser && \in_array($properties['extension'], ['yml', 'yaml'])) {
            throw new DoNotListException();
        }

        $properties['pathOnly'] = $this->enricher->extractPathOnly($properties['fullpath']);
        //$properties['pathOnlyURLDecoded'] = urldecode($properties['pathOnly']);
        //$properties['pathOnlyTranslatedURLDecoded'] = $this->enricher->translatePath($properties['pathOnly']));
        $properties['pathOnlyURLDecoded'] = $this->enricher->translatePath($properties['pathOnly'], $locale);

        $properties['filename'] = urldecode($this->enricher->cleanFilename($this->enricher->extractFilename($properties['fullpath'])));

        $properties['isDir'] = $this->enricher->isDir($properties['fullpath']);
        $properties['isFile'] = $this->enricher->isFile($properties['fullpath']);

        // Properties übernehmen
        $props = $this->enricher->mergeProps($this->element['propstat']);

        $properties['lastmodified'] = $this->enricher->lastModified($props);

        $properties['fileid'] = $props['fileid'] ?? null;
        $properties['size'] = $props['size'] ?? null;
        $properties['resourcetype'] = $props['resourcetype'] ?? null;
        $properties['contentlength'] = $props['getcontentlength'] ?? null;

        $properties['contenttype'] = null;
        if (isset($props['getcontenttype']) && !\is_array($props['getcontenttype'])) {
            $properties['contenttype'] = $props['getcontenttype'];
        }

        $properties['languages'] = $mergedConfiguration->languages;

        $properties['languagesImages'] = '';

        if (\is_array($properties['languages'])) {
            $properties['languages'] = array_map('strtolower', $properties['languages']);

            // Nur bestimmte Sprachen anzeigen
            if (null !== $language) {
                if (false == \in_array($language, $properties['languages'])) {
                    throw new DoNotListException();
                }
            }

//            foreach ($properties['languages'] as $fileLanguage) {
//                // Es gibt keine Flagge EN
//                if ($fileLanguage == 'en') $fileLanguage = 'gb';
//                $properties['languagesImages'] .= '<img src="/dist/images/public/node_modules/svg-country-flags/png100px/' . $fileLanguage . '.png" class="flag" style="margin-right: 5px;">';
//            }
        } else {
            // Nur bestimmte Sprachen anzeigen
            if (null !== $language) {
                throw new DoNotListException();
            }
        }

        $properties['language_only'] = $mergedConfiguration->language_only;

        if (\is_array($properties['language_only']) && !\in_array(strtolower($locale), array_map('strtolower', $properties['language_only']))) {
            throw new DoNotListException();
        }

        // Übersetzungen hinzufügen
        if (null !== $mergedConfiguration->folderNameEN && 'en' == $locale) {
            $properties['filename'] = $mergedConfiguration->folderNameEN;
        }
        if (null !== $mergedConfiguration->folderNameDE && 'de' == $locale) {
            $properties['filename'] = $mergedConfiguration->folderNameDE;
        }
        if (null !== $mergedConfiguration->folderNameFR && 'fr' == $locale) {
            $properties['filename'] = $mergedConfiguration->folderNameFR;
        }
        if (null !== $mergedConfiguration->folderNameES && 'es' == $locale) {
            $properties['filename'] = $mergedConfiguration->folderNameES;
        }

        $properties['filenameURLDecoded'] = urldecode($properties['filename']);

        // Tags aus REDIS holen
        $properties['tags'] = $this->inMemoryConnector->getTagsByFileId($props['fileid']);

//        $properties['language'] = [];
//        foreach ($properties['tags'] as $tag) {
//
//            // Languages erkennen
//            if (preg_match( '/^LANGUAGE_([A-Z]{2})/', strtoupper($tag),$language) == 1) {
//                $properties['languages'][] = $language[1];
//            };
//        }

        return $properties;
    }
}
