<?php
/**
 * Created by PhpStorm.
 * User: dblicker
 * Date: 27.04.18
 * Time: 09:34.
 */

namespace App\Model\Box\Nextcloud\Connector\Webdav;

use App\Exception\Box\Nextcloud\DoNotListException;
use App\Exception\Box\Nextcloud\NextcloudException;
use App\Exception\Box\Nextcloud\NotAuthenticatedException;
use App\Model\Api\Language\LanguageSanitizer;
use App\Model\Api\PortalUser\PortalUserInterface;
use App\Model\Box\Nextcloud\ConfigBuilder\Enricher;
use App\Model\Box\Nextcloud\ConfigBuilder\Merger;
use App\Model\Box\Nextcloud\Connector\WebdavConnectorInterface;
use App\Model\Box\Nextcloud\inMemory\inMemoryConnectorInterface;
use App\Model\Box\Nextcloud\Permission\PermissionChecker;
use GuzzleHttp\Client;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\File\Exception\AccessDeniedException;
use Symfony\Component\Serializer\SerializerInterface;

class WebdavConnectorGuzzle extends XmlToArray implements WebdavConnectorInterface
{
    /** @var inMemoryConnectorInterface */
    private $inMemoryConnector;

    /** @var Enricher */
    private $enricher;

    /** @var Merger */
    private $merger;

    /** @var PermissionChecker */
    private $permissionChecker;

    /** @var PortalUserInterface */
    private $portalUser;

    /** @var Client */
    private $client;
    private SerializerInterface $serializer;

    /**
     * ConnectorGuzzle constructor.
     *
     * @throws \App\Exception\LanguageNotSupportedException
     */
    public function __construct(SerializerInterface $serializer, inMemoryConnectorInterface $inMemoryConnector, PortalUserInterface $portalUser, LanguageSanitizer $languageSanitizer, Enricher $enricher, Merger $merger, PermissionChecker $permissionChecker)
    {
        $this->inMemoryConnector = $inMemoryConnector;
        $this->enricher = $enricher;
        $this->merger = $merger;
        $this->permissionChecker = $permissionChecker;

//        // Aktuell eingestellte Sprache des Users
//        $locale = ($session->get('_locale')) ? $languageSanitizer->sanitize($session->get('_locale'), true) : 'en';

        parent::__construct($serializer, $this->inMemoryConnector, $this->enricher, $this->merger, $this->permissionChecker, $languageSanitizer);

        $this->portalUser = $portalUser;
        $portalUser->fetch();

        $this->client = new Client([
            'base_uri' => $_ENV['NEXTCLOUD_URL'].'/remote.php/',
            'verify' => !('false' === $_ENV['NEXTCLOUD_VERIFY_SSL'] || '0' === $_ENV['NEXTCLOUD_VERIFY_SSL']),
            'auth' => [
                $_ENV['NEXTCLOUD_USER'],
                $_ENV['NEXTCLOUD_PASSWORD'],
            ],
            'headers' => [
                'Content' => 'application/x-www-form-urlencoded',
                'content-Type' => 'text/xml',
            ],
        ]);
        $this->serializer = $serializer;
    }

    /**
     * Sucht Dateien.
     *
     * @throws NextcloudException
     * @throws NotAuthenticatedException
     */
    public function search(string $searchString, string $language = '', string $folder = '/'): array
    {
        $searchString = filter_var($searchString, \FILTER_SANITIZE_STRING);
        $language = filter_var($language, \FILTER_SANITIZE_STRING);
        $folder = filter_var($folder, \FILTER_SANITIZE_STRING);

        // Leere Language wird als NULL weitergegeben
        if (empty($language)) {
            $language = null;
        }

        try {
            $response = $this->client->request(
                'SEARCH',
                'dav',
                [
                'body' => '<?xml version="1.0" encoding="UTF-8"?>
                             <d:searchrequest xmlns:d="DAV:" xmlns:oc="http://owncloud.org/ns">
                                 <d:basicsearch>
                                     <d:select>
                                         <d:prop>
                                             <oc:fileid/>
                                             <d:displayname/>
                                             <d:getcontenttype/>
                                             <oc:size/>
                                         </d:prop>
                                     </d:select>
                                     <d:from>
                                         <d:scope>
                                             <d:href>/files/'.$_ENV['NEXTCLOUD_USER'].$folder.'</d:href>
                                             <d:depth>infinity</d:depth>
                                         </d:scope>
                                     </d:from>
                                     <d:where>
                                         <d:like>
                                             <d:prop>
                                                 <d:displayname/>
                                             </d:prop>
                                             <d:literal>%'.$searchString.'%</d:literal>
                                         </d:like>
                                     </d:where>
                                     <d:orderby>
                                         <d:prop>
                                             <d:displayname/>
                                         </d:prop>
                                         <d:ascending/>
                                     </d:orderby>
                                </d:basicsearch>
                            </d:searchrequest>',
            ]
            );

            return $this->searchToArray($response->getBody()->getContents(), $folder, $language, false);
        } catch (\Exception $e) {
            if (401 == $e->getCode()) {
                throw new NotAuthenticatedException('Not authorized');
            }

            throw new NextcloudException('Search could not be performed by webdav');
        }
    }

    /**
     * @throws NextcloudException
     * @throws NotAuthenticatedException
     */
    public function getFile(string $path): BinaryFileResponse
    {
        $path = filter_var($path, \FILTER_SANITIZE_STRING, \FILTER_FLAG_NO_ENCODE_QUOTES);

        $mergedConfiguration = $this->merger->mergeConfigTree($this->enricher->getConfigTree($path));

        // Darf der User die Datei herunterladen?
        if (false === $this->permissionChecker->isAccessGranted($path, $mergedConfiguration)) {
            throw new AccessDeniedException($path);
        }

        try {
            $tmpFile = tempnam(sys_get_temp_dir(), 'nextcloudDownload');

            $this->client->request(
                'GET',
                'dav/files/'.$_ENV['NEXTCLOUD_USER'].'/'.rawurldecode($path),
                [
                    'sink' => $tmpFile,
                ]
            );

            return new BinaryFileResponse($tmpFile);
        } catch (\Exception $e) {
            if (401 == $e->getCode()) {
                throw new NotAuthenticatedException('Not authorized');
            }

            throw new NextcloudException('File "'.$path.' could not be loaded by webdav');
        }
    }

    /**
     * @throws NextcloudException
     * @throws NotAuthenticatedException
     */
    public function getFolder(string $path): array
    {
        $path = htmlspecialchars($path);

        try {
            $response = $this->client->request(
                'PROPFIND',
                'dav/files/'.$_ENV['NEXTCLOUD_USER'].'/'.rawurlencode($path),
                [
                    'headers' => [
                        'Content-Type' => 'text/xml; charset=utf-8',
                        'Depth' => '1',
                        'Host' => $_ENV['NEXTCLOUD_HOST'],
                    ],
                    'body' => '<?xml version="1.0" encoding="UTF-8"?>
                        <d:propfind xmlns:d="DAV:" xmlns:oc="http://owncloud.org/ns">
                          <d:prop>
                            <d:getlastmodified/>
                            <d:getcontentlength/>
                            <d:getcontenttype/>
                            <d:displayname/>
                            <d:resourcetype/>
                            <oc:fileid/>
                            <oc:size/>
                            <oc:tags/>
                          </d:prop>
                        </d:propfind>',
                ]
            );

            return $this->listToArray($response->getBody()->getContents(), $path);
        } catch (\Exception $e) {
            if (401 == $e->getCode()) {
                throw new NotAuthenticatedException('Not authorized');
            }

            throw new NextcloudException('Folder "'.$path.' could not be loaded by webdav');
        }
    }

    /**
     * Wandelt ein PROPFIND Result in ein Array um.
     */
    private function listToArray(string $xml, string $path): array
    {
        $object = simplexml_load_string(str_replace('oc:', '', str_replace('d:', '', $xml)));

        $parent = [];
        $childs = [];

        $counter = 0;
        foreach ($object->response as $value) {
            try {
                (0 == $counter) ? $parent = $this->simpleXMLElementToArray($value, $path, null, false, true) : $childs[] = $this->simpleXMLElementToArray($value, $path);
                ++$counter;
            } catch (DoNotListException $e) {
                // Einfach nicht aufnehmen
            }
        }

        return [
            'parent' => $parent,
            'childs' => $this->sortChilds($childs),
        ];
    }

    /**
     * Sortiert die Childs nach Ordner/Nicht Ordner und dann nach dem Alphabet.
     */
    private function sortChilds(array $childs): array
    {
        usort($childs, function ($a, $b) {
            $aSortString = $a['isDir'] ? '0' : '1';
            $aSortString .= $a['filename'];

            $bSortString = $b['isDir'] ? '0' : '1';
            $bSortString .= $b['filename'];

            return $aSortString > $bSortString;
        });

        return $childs;
    }
}
