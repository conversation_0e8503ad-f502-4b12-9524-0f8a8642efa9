<?php

/**
 * E<PERSON>eller: <PERSON>er
 * E-Mail: <EMAIL>
 * Datum: 20.06.17 11:02.
 */

namespace App\Model\TaskRunner;

use App\Exception\InvalidTaskPostDataException;

class TaskPostDataAnalyzer
{
    /**
     * Sanitizing POST Data.
     *
     * @throws InvalidTaskPostDataException
     */
    public function analyzePostData(array $postData): TaskPostData
    {
        $taskPostData = new TaskPostData();

        $bundleName = $postData['bundleName'] ?? null;
        $task = $postData['task'] ?? null;
        $parameters = $postData['parameters'] ?? [];

        $taskPostData->bundleName = filter_var($bundleName, \FILTER_SANITIZE_STRING, \FILTER_FLAG_STRIP_HIGH);
        $taskPostData->task = filter_var($task, \FILTER_SANITIZE_STRING, \FILTER_FLAG_STRIP_HIGH);

        // Prüfen ob parameters ein JSON-String ist und diesen dekodieren
        if (is_string($parameters)) {
            $decodedParameters = json_decode($parameters, true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decodedParameters)) {
                $parameters = $decodedParameters;
            } else {
                // Falls JSON-Dekodierung fehlschlägt, als leeres Array behandeln
                $parameters = [];
            }
        }

        // Sicherstellen, dass $parameters ein Array ist
        if (!is_array($parameters)) {
            $parameters = [];
        }

        foreach ($parameters as $key => $value) {
            $key = filter_var($key, \FILTER_SANITIZE_STRING, \FILTER_FLAG_STRIP_HIGH);

            // Wert nur filtern wenn es ein String ist
            if (is_string($value)) {
                $value = filter_var($value, \FILTER_SANITIZE_STRING, \FILTER_FLAG_STRIP_HIGH);
            }

            // True bzw. False als Value erkennen
            if ('true' === $value) {
                $value = true;
            } elseif ('false' === $value) {
                $value = false;
            }

            $taskPostData->parameters[$key] = $value;
        }

        if (empty($taskPostData->bundleName) || empty($taskPostData->task)) {
            throw new InvalidTaskPostDataException();
        }

        return $taskPostData;
    }
}
