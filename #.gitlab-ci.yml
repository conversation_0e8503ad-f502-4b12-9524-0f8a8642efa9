stages:
    - test
    - build
    - staging
    - production

test:
    stage: test
    image: gitlab-php-ci-runner:latest
    tags:
        - test
    services:
        - mariadb
        - redis
    cache:
        key: "$CI_BUILD_REF_NAME gitlab-php-ci-runner:latest"
        paths:
            - .composercache
            - vendor
    artifacts:
        expire_in: 12 hours
        paths:
            - vendor
    before_script:
        - composer config cache-files-dir .composercache
    script:
        - composer install --optimize-autoloader --no-scripts --no-interaction
        - vendor/bin/simple-phpunit
        - echo "Run Karma Tests - TBD"
#        - npm run test
    variables:
        MYSQL_ROOT_PASSWORD: mariapwd

        REDIS_DSN: 'redis://redis:6379'

        PORTAL_DATABASE_HOST: mariadb
        PORTAL_DATABASE_USER: root
        PORTAL_DATABASE_PASSWORD: mariapwd

        SALES_DATABASE_HOST: mariadb
        SALES_DATABASE_USER: root
        SALES_DATABASE_PASSWORD: mariapwd

        SERVICE_DATABASE_HOST: mariadb
        SERVICE_DATABASE_USER: root
        SERVICE_DATABASE_PASSWORD: mariapwd

        MONTAGE_DATABASE_HOST: mariadb
        MONTAGE_DATABASE_USER: root
        MONTAGE_DATABASE_PASSWORD: mariapwd

        APPLICATION_DATABASE_HOST: mariadb
        APPLICATION_DATABASE_USER: root
        APPLICATION_DATABASE_PASSWORD: mariapwd

        APPLICATION_REMOTE_DATABASE_HOST: mariadb
        APPLICATION_REMOTE_DATABASE_USER: root
        APPLICATION_REMOTE_DATABASE_PASSWORD: mariapwd

        TDATA_DATABASE_HOST: mariadb
        TDATA_DATABASE_USER: root
        TDATA_DATABASE_PASSWORD: mariapwd

        TDATA_PREVIEW_DATABASE_HOST: mariadb
        TDATA_PREVIEW_DATABASE_USER: root
        TDATA_PREVIEW_DATABASE_PASSWORD: mariapwd

        WEBSITE_DATABASE_HOST: mariadb
        WEBSITE_DATABASE_USER: root
        WEBSITE_DATABASE_PASSWORD: mariapwd

        MEDIA_DATABASE_HOST: mariadb
        MEDIA_DATABASE_USER: root
        MEDIA_DATABASE_PASSWORD: mariapwd

        BOX_DATABASE_HOST: mariadb
        BOX_DATABASE_USER: root
        BOX_DATABASE_PASSWORD: mariapwd

        CATALOG_DATABASE_HOST: mariadb
        CATALOG_DATABASE_USER: root
        CATALOG_DATABASE_PASSWORD: mariapwd

        ABUKONFIS_DATABASE_HOST: mariadb
        ABUKONFIS_DATABASE_USER: root
        ABUKONFIS_DATABASE_PASSWORD: mariapwd

        TASKRUNNER_DATABASE_HOST: mariadb
        TASKRUNNER_DATABASE_USER: root
        TASKRUNNER_DATABASE_PASSWORD: mariapwd

        USERMANAGEMENT_DATABASE_HOST: mariadb
        USERMANAGEMENT_DATABASE_USER: root
        USERMANAGEMENT_DATABASE_PASSWORD: mariapwd

        LDAP_READ_PASSWORD: mv%X62@p6RQ9xJ3S6M
        LDAP_WRITE_PASSWORD: 123456%q

        NODE_ENV: "test"

build:
    stage: build
    image: gitlab-php-ci-runner:latest
    tags:
        - build
    cache:
        key: "$CI_BUILD_REF_NAME gitlab-php-ci-runner:latest"
        paths:
            - node_modules
            - bower_components
    artifacts:
        expire_in: 12 hours
        paths:
            - node_modules
            - bower_components
    before_script:
        - npm install
        - bower install --allow-root
    script:
        - npm run encore:dev
    only:
        - master
    variables:
        NODE_ENV: "production"

staging:
    stage: staging
    tags:
        - staging
    script:
        - echo "Deploy to staging server - TBD"
    environment:
        name: staging
        url: https://portalstaging.abus-kransysteme.de
    only:
        - master

production:
    stage: production
    tags:
        - production
    script:
        - echo "Deploy to production server - TBD"
    environment:
        name: production
        url: https://portal2018.abus-kransysteme.de
    when: manual
    only:
        - master
