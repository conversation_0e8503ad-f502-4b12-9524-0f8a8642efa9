/** @type {import("prettier").Config} */

export default {
  singleQuote: true,
  printWidth: 120,
  trailingComma: 'all',
  bracketSameLine: true,
  vueIndentScriptAndStyle: false,
  endOfLine: 'lf',
  tabWidth: 2,
  useTabs: false,
  plugins: ['prettier-plugin-organize-attributes'],
  attributeGroups: [
    '^:?is$',
    '^v-for$',
    '^v-(if|else-if|else|show|cloak)$',
    '^v-(pre|once)$',
    '^:?id$',
    '^:?ref$',
    '^:?key$',
    '^#',
    '^v-badge$',
    '^v-model$',
    '^v-model:',
    '^v-observe-visibility$',
    '$CODE_GUIDE',
    '$DEFAULT',
    '^v-on$',
    '^v-html$',
    '^v-text$',
  ],
};
